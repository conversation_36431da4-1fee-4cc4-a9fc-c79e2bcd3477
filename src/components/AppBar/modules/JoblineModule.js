import {
  Module,
  ModuleLabel,
  ModuleOption,
  ModuleOptions,
} from "@/components/AppBar/utils";
import { StaffMattersIcon } from "@/components/AppBar/icons";

export default function JoblineModule() {
  return (
    <Module value="staffs">
      <ModuleLabel
        label="Jobline Resources"
        icon={<StaffMattersIcon className="fill-amber-600" />}
      />

      <ModuleOptions>
        <ModuleOption path="/ws/staffs">Staff Database</ModuleOption>
        <ModuleOption path="/ws/my-hr">My HR</ModuleOption>
        <ModuleOption path="/ws/roster">Roster</ModuleOption>
        <ModuleOption path="/ws/company-news">Company News</ModuleOption>
      </ModuleOptions>
    </Module>
  );
}
