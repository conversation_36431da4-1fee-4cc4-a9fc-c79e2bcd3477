"use client";
import { useCurrentUser } from "@/providers/CurrentUserProvider";

export default function Permit({ children, roles = [], permissions = [] }) {
  const user = useCurrentUser();

  if (!user) return children;

  const hasRole = roles.some((r) => (user.roles || []).includes(r));
  const hasPermission = permissions.some((p) =>
    (user.permissions || []).includes(p),
  );

  if (hasRole || hasPermission) {
    return children;
  }
}
