"use client";

import { useId, useState } from "react";
import { isEmpty } from "lodash";
import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import Card from "@/ui/Card";
import ErrorMessage from "@/ui/ErrorMessage";

export default function RadioBox({
  defaultValue,
  setFormValue,
  containerClassName,
  items,
  error = null,
  showErrorMessage = true,
  disabled = false,
}) {
  const id = useId();
  const [value, setValue] = useState(defaultValue);

  const hasError = !isEmpty(error);

  if (isEmpty(items)) return null;

  const handleChange = (val) => {
    if (disabled) return;

    setValue(val);
    setFormValue && setFormValue(val);
  };

  return (
    <VStack className="gap-0">
      <div
        className={cn(
          "grid grid-cols-1 gap-2 sm:grid-cols-2",
          containerClassName,
        )}
      >
        {items.map((item, index) => (
          <RadioBoxItem
            key={`radio-box-${id}-${index}`}
            item={item}
            selectedValue={value}
            handleChange={handleChange}
            hasError={hasError}
            disabled={disabled}
          />
        ))}
      </div>

      {hasError && showErrorMessage && (
        <ErrorMessage errorMessage={error.message} />
      )}
    </VStack>
  );
}

function RadioBoxItem({
  selectedValue,
  item,
  handleChange,
  hasError,
  disabled,
}) {
  const hasValueSelected = !isEmpty(selectedValue);
  const { label, value, description } = item;
  const selected = selectedValue === value;

  return (
    <Card
      tabIndex={0}
      className={cn(
        "cursor-pointer rounded-md border-none outline outline-input",
        {
          "focus-within:outline-2 focus-within:outline-input-ring/60 hover:outline-2 hover:outline-input-ring/60":
            !disabled && !selected && !hasError,
          "cursor-not-allowed opacity-50": disabled,
          "outline-gray-200": hasValueSelected && !selected,
          "outline-2 outline-input-ring": selected,
          "text-danger outline-danger": hasError,
        },
      )}
      onKeyDown={(e) => {
        if (disabled) return;
        if (e.keyCode === 13) handleChange(value);
      }}
      onClick={() => {
        if (disabled) return;

        handleChange(value);
      }}
    >
      <HStack className="flex-nowrap items-start gap-4">
        <div
          className={cn("mt-1 h-5 w-5 rounded-full border border-input p-0.5", {
            "border-gray-200": hasValueSelected && !selected,
            "border-primary": selected,
            "border-danger": hasError,
          })}
        >
          <div
            className={cn("h-3.5 w-3.5 rounded-full bg-primary opacity-0", {
              "opacity-100": selected,
              "bg-danger": hasError,
            })}
          />
        </div>

        <VStack className="gap-1">
          <p>{label}</p>
          <p className="text-sm text-muted">{description}</p>
        </VStack>
      </HStack>
    </Card>
  );
}
