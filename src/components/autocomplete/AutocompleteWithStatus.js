"use client";

import { useState } from "react";
import { useController } from "react-hook-form";
import { useQuery } from "urql";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
  CommandItem,
} from "@/ui/Command";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import Input from "@/ui/Input";
import { ChevronsUpDown, Search } from "lucide-react";
import { Spinner } from "@blueprintjs/core";
import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";

export default function AutocompleteWithStatus({
  gqlQuery,
  gqlVariables = {},
  label,
  name,
  rules,
  placeholder,
  defaultValue,
  onInputValueChange,
  emptyMessage = "No items found.",
  children = (item) => item.name,
  renderSelectedItem = (selectedItem) => selectedItem.name,
  segmentedActiveLabel = "Active",
  onSelect = null,
}) {
  const [query, setQuery] = useState(defaultValue || "");
  const [selectedItem, setSelectedItem] = useState(defaultValue);
  const [status, setStatus] = useState("ACTIVE");

  const { field, fieldState } = useController({ name, rules, defaultValue });
  const error = fieldState.error;

  const [result] = useQuery({
    query: gqlQuery,
    variables: { query, status, ...gqlVariables },
  });

  const { data, fetching } = result;
  const items = data?.items?.nodes || [];

  return (
    <Popover>
      <PopoverTrigger>
        <div data-leftalignfix="DO_NOT_REMOVE">
          <Input
            label={label}
            suffix={<ChevronsUpDown size={20} strokeWidth={2} />}
            value={
              selectedItem ? renderSelectedItem(selectedItem) : placeholder
            }
            error={error}
            onChange={() => {}}
          />
        </div>
      </PopoverTrigger>

      <PopoverContent
        align="start"
        className="min-w-(--radix-popover-trigger-width)"
      >
        <Command className="w-full" shouldFilter={false}>
          <div className="border-b p-2">
            <CommandInput
              placeholder={placeholder || label}
              prefix={<Search className="ml-0" size={20} />}
              suffix={fetching && <Spinner size={20} />}
              size="sm"
              borderless
              value={query}
              onValueChange={(newValue) => {
                setQuery(newValue);

                if (newValue === "") {
                  setSelectedItem(null);
                  field.onChange(null); // Needed!
                  onInputValueChange && onInputValueChange(null);
                }
              }}
            />
          </div>

          <CommandList className="p-2">
            <div className="mb-2 flex justify-center">
              <SegmentedControlRoot
                size="sm"
                defaultValue={
                  selectedItem
                    ? selectedItem.status === "ACTIVE"
                      ? "ACTIVE"
                      : "ALL"
                    : status || "ACTIVE"
                }
                onValueChange={(status) => setStatus(status)}
              >
                <SegmentedControlItem value="ALL">All</SegmentedControlItem>
                <SegmentedControlItem value="ACTIVE">
                  {segmentedActiveLabel}
                </SegmentedControlItem>
              </SegmentedControlRoot>
            </div>

            <CommandEmpty>{emptyMessage}</CommandEmpty>

            <div className="space-y-0.5">
              {items.map((item) => {
                return (
                  <CommandItem
                    key={item.id}
                    value={item.name}
                    onSelect={() => {
                      if (onSelect) {
                        onSelect(item);
                      } else {
                        setQuery(item.name);
                        setSelectedItem(item);
                        field.onChange({
                          id: item.id,
                          name: item.name,
                        });
                      }

                      onInputValueChange && onInputValueChange(item);
                    }}
                  >
                    {children({ item, query })}
                  </CommandItem>
                );
              })}
            </div>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
