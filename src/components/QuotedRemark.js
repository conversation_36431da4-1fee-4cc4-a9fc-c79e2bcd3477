import { Citation } from "@blueprintjs/icons";
import VStack from "@/ui/VStack";
import RawHtml from "@/ui/RawHtml";
import cn from "@/lib/cn";

export default function QuotedRemark({ title, children, bg = true }) {
  // if (!children) return null;

  const classNames = cn("grid grid-cols-[44px_1fr]", {
    "rounded-xl bg-amber-50 p-2": bg,
  });

  return (
    <div className={classNames}>
      <div className="flex justify-center">
        <Citation className="text-amber-600" />
      </div>
      <VStack>
        <h1 className="text-xs text-amber-600 uppercase">{title}</h1>
        <RawHtml>{children}</RawHtml>
      </VStack>
    </div>
  );
}
