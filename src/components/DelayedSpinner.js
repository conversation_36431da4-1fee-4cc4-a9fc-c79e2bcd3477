import { DelayedRender } from "@/hooks/useDelayedRender";
import { Spinner } from "@blueprintjs/core";
import cn from "@/lib/cn";

export default function DelayedSpinner({ className, size = 48 }) {
  const classNames = cn("flex items-center justify-center", className);

  return (
    <DelayedRender delay={1000}>
      <div className={classNames}>
        <Spinner size={size} />
      </div>
    </DelayedRender>
  );
}
