"use client";

import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import Button from "@/ui/Button";
import { ChevronsUpDownIcon, SearchIcon } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/ui/Command";
import { useEffect, useState } from "react";
import HStack from "@/ui/HStack";
import { usePathname, useRouter } from "next/navigation";
import { useQuery } from "urql";
import { Spinner } from "@blueprintjs/core";

const QUERY = `
  query clientOtherMemberships {
    clientOtherMemberships {
      id
      slug

      company {
        id
        name
        slug
      }
    }

    clientCurrentCompany {
      id
      fmId
      name
      slug
      logoUrl
    }
  }
`;

export default function ClientCompanySwitcher() {
  const [query, setQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const [res, executeQuery] = useQuery({ query: QUERY, pause: true });

  useEffect(() => {
    setTimeout(() => {
      executeQuery();
    }, 0); // Simulate a delay for the initial loading state
  }, [executeQuery]);

  const { data, fetching } = res;
  const memberships = data?.clientOtherMemberships || [];
  const currentCompany = data?.clientCurrentCompany;

  if (fetching) {
    return (
      <Button
        variant="ghost"
        className="justify-between font-semibold"
        fullWidth
      >
        <HStack className="w-full flex-nowrap justify-end gap-2 overflow-hidden text-ellipsis">
          <Spinner size={20} />
        </HStack>
      </Button>
    );
  }

  if (memberships.length === 0) {
    return (
      <Button
        variant="ghost"
        className="justify-between font-semibold"
        fullWidth
      >
        <HStack className="max-w-[230px] flex-nowrap gap-2 overflow-hidden text-ellipsis">
          {currentCompany?.logoUrl && (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={currentCompany.logoUrl}
              alt="Company logo"
              className="aspect-square h-[24px] w-[24px] rounded-full object-cover object-center"
            />
          )}
          {currentCompany?.name}
        </HStack>
      </Button>
    );
  }

  return (
    <Popover>
      <PopoverTrigger>
        <Button
          variant="ghost"
          className="justify-between font-semibold"
          suffix={<ChevronsUpDownIcon size={20} strokeWidth={2} />}
          onClick={() => setIsOpen(!isOpen)}
          fullWidth
        >
          <HStack className="max-w-[230px] flex-nowrap gap-2 overflow-hidden text-ellipsis">
            {currentCompany.logoUrl && (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={currentCompany.logoUrl}
                alt="Company logo"
                className="aspect-square h-[24px] w-[24px] rounded-full object-cover object-center"
              />
            )}
            {currentCompany.name}
          </HStack>
        </Button>
      </PopoverTrigger>

      <PopoverContent align="start" className="min-w-96">
        <Command className="w-full">
          <div className="border-b p-2">
            <CommandInput
              placeholder="Filter company..."
              prefix={<SearchIcon className="ml-0" size={20} />}
              size="sm"
              borderless
              value={query}
              onValueChange={(newValue) => {
                setQuery(newValue);
              }}
            />
          </div>

          <CommandList className="p-2">
            <CommandEmpty>No companies found.</CommandEmpty>

            {memberships.map((membership) => {
              return (
                <CommandItem
                  key={membership.company.id}
                  value={membership.company.name}
                  onSelect={() => {
                    const preparedForReplacement = pathname.split("/");
                    preparedForReplacement[2] = membership.slug;
                    const replacedUrl = preparedForReplacement
                      .slice(0, 4)
                      .join("/");

                    router.push(replacedUrl);
                  }}
                >
                  {membership.company.name}
                </CommandItem>
              );
            })}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
