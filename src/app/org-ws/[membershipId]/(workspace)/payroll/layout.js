import HStack from "@/ui/HStack";
import StickyHeader from "@/components/StickyHeader";
import { ClientAppBarDrawer } from "@/components/ClientAppBar";
import PayrollPrimaryTab from "@/app/org-ws/[membershipId]/(workspace)/payroll/_ui/PayrollPrimaryTab";

export default async function Layout({ children, params }) {
  return (
    <div className="h-screen">
      <StickyHeader className="border-b-0">
        <HStack className="h-[52px] flex-nowrap px-4">
          <ClientAppBarDrawer />
          <h1 className="w-full truncate text-2xl font-bold font-stretch-expanded dark:text-amber-500">
            Payroll Approval
          </h1>
        </HStack>

        <PayrollPrimaryTab params={params} />
      </StickyHeader>

      {children}
    </div>
  );
}
