"use client";

import { useState } from "react";
import { Accordion } from "@/ui/Accordion";
import { Item } from "@/components/accordion-variants/left-arrow-basic";
import SolidCaretRightIcon from "@/icons/SolidCaretRightIcon";
import TimesheetsApprovalTable from "@/app/org-ws/[membershipId]/(workspace)/payroll/_ui/TimesheetsApprovalTable";

export default function TimesheetsAccordion() {
  const allAccordionValues = ["1"];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  return (
    <Accordion
      type="multiple"
      value={accordionValues}
      onValueChange={(values) => setAccordionValues(values)}
    >
      <Item
        title="Timesheets (3 contractors, 3 timesheets)"
        value="1"
        borderless
        triggerIcon={
          <div className="accordion-arrow transition-transform duration-150">
            <SolidCaretRightIcon />
          </div>
        }
      >
        <TimesheetsApprovalTable />
      </Item>
    </Accordion>
  );
}
