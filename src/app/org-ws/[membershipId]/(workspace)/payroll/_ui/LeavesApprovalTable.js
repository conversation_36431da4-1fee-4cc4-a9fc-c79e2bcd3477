import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
// import apiQuery from "@/lib/apiQuery";
// import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
// import mergeColumns from "@/ui/DataGrid/mergeColumns";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
// import decompressSearchParam from "@/lib/decompressSearchParam";
// import QueryInput from "@/ui/DataGrid/QueryInput";
// import HStack from "@/ui/HStack";
// import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
// import Button from "@/ui/Button";
// import { Filter } from "lucide-react";
// import SortsDialog from "@/ui/DataGrid/SortsDialog";
// import DateCell from "@/ui/DataGrid/cells/DateCell";
import LeaveDateCell from "@/ui/DataGrid/cells/LeaveDateCell";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";

const QUERY = `
  query plural($id: ID!) {
    plural(id: $employmentId) {
      nodes {
        id

        creator {
          id
          name
        }
        modifier {
          id
          name
        }

        createdAt
        updatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "XX") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Type",
    columnWidth: 150,
    propertyName: "leaveType",
    visible: true,
  },
  {
    columnName: "Leave Date",
    columnWidth: 200,
    propertyName: "leaveDate",
    visible: true,
    cellComponent: LeaveDateCell,
  },
  {
    columnName: "Submitted Date",
    columnWidth: 200,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
  {
    columnName: "1st Approver",
    columnWidth: 250,
    propertyName: "firstApprover",
    visible: true,
  },
  {
    columnName: "2nd Approver",
    columnWidth: 150,
    propertyName: "secondApprover",
    visible: true,
    cellComponent: ResourceLinkCell,
  },
];

const records = [
  {
    id: "1",
    leaveType: "Annual",
    leaveDate: "2024-07-01",
    createdAt: "2024-06-20T10:30:00Z",
    firstApprover: "John Doe approved on 2024-06-21",
    secondApprover: "Jane Smith",
  },
  {
    id: "2",
    leaveType: "Medical",
    leaveDate: "2024-07-05",
    createdAt: "2024-06-22T14:45:00Z",
    firstApprover: "Alice Johnson",
    secondApprover: "Bob Brown",
  },
  {
    id: "3",
    leaveType: "Annual",
    leaveDate: "2024-07-10",
    createdAt: "2024-06-25T09:15:00Z",
    firstApprover: "Charlie Davis",
    secondApprover: "Diana Evans",
  },
];

export default function LeavesApprovalTable() {
  // let { page, query, sorts } = await searchParams;
  // const currentPage = Number(page || 1);
  // sorts = decompressSearchParam(sorts, [{ id: "name", desc: true }]);
  // const { srid } = await params;

  // const res = await apiQuery(QUERY, {
  //   id: srid,
  //   query,
  //   sorts,
  //   page: currentPage,
  // });
  //
  // const {
  //   records: XXX,
  //   totalPages,
  //   totalCount,
  //   processingTimeMs,
  //   personalTableColumns,
  // } = getTableMetadata(res.data.xx, res.data.personalTableColumn);
  //
  // const tableColumns = mergeColumns(
  //   DEFAULT_TABLE_COLUMNS,
  //   personalTableColumns,
  // );

  return (
    <DataGrid.Root className="space-y-4">
      <DataGrid.Content>
        <DataGridTable
          numRows={records.length}
          data={records}
          tableColumns={DEFAULT_TABLE_COLUMNS}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
}
