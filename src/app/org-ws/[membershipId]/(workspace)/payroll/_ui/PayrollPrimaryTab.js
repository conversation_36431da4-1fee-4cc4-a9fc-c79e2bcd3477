import { PrimaryTabs, Tab } from "@/ui/PrimaryTabs";
import {
  BadgeQuestionMarkIcon,
  HistoryIcon,
  CalendarDaysIcon,
} from "lucide-react";

export default async function PayrollPrimaryTab({ params }) {
  const { membershipId } = await params;
  const baseUrl = `/org-ws/${membershipId}/payroll`;

  return (
    <PrimaryTabs className="border-t border-b">
      <Tab href={`${baseUrl}`} leftIcon={<BadgeQuestionMarkIcon />}>
        Approval Required
      </Tab>

      <Tab href={`${baseUrl}/history`} leftIcon={<HistoryIcon />}>
        Approval History
      </Tab>

      <Tab href={`${baseUrl}/history`} leftIcon={<CalendarDaysIcon />}>
        Leave Roster
      </Tab>
    </PrimaryTabs>
  );
}
