"use client";

import { useState } from "react";
import { Accordion } from "@/ui/Accordion";
import { Item } from "@/components/accordion-variants/left-arrow-basic";
import SolidCaretRightIcon from "@/icons/SolidCaretRightIcon";
import LeavesApprovalTable from "@/app/org-ws/[membershipId]/(workspace)/payroll/_ui/LeavesApprovalTable";

export default function LeavesAccordion() {
  const allAccordionValues = ["1"];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  return (
    <Accordion
      type="multiple"
      value={accordionValues}
      onValueChange={(values) => setAccordionValues(values)}
    >
      <Item
        title="Leaves (2 contractors, 6 leaves)"
        value="1"
        borderless
        triggerIcon={
          <div className="accordion-arrow transition-transform duration-150">
            <SolidCaretRightIcon />
          </div>
        }
        contentClassName="py-0"
      >
        <ContractorsAccordion />
      </Item>
    </Accordion>
  );
}

const ContractorsAccordion = () => {
  const allAccordionValues = ["1", "2"];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  return (
    <Accordion
      type="multiple"
      value={accordionValues}
      onValueChange={(values) => setAccordionValues(values)}
    >
      <Item
        title="Thomas Shelby"
        value="1"
        borderless
        triggerIcon={
          <div className="accordion-arrow transition-transform duration-150">
            <SolidCaretRightIcon />
          </div>
        }
      >
        <LeavesApprovalTable />
      </Item>
      <Item
        title="Alex Tan Oi"
        value="2"
        borderless
        triggerIcon={
          <div className="accordion-arrow transition-transform duration-150">
            <SolidCaretRightIcon />
          </div>
        }
      >
        <LeavesApprovalTable />
      </Item>
    </Accordion>
  );
};
