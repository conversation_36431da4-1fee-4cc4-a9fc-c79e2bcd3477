import HStack from "@/ui/HStack";
import LeavesAccordion from "@/app/org-ws/[membershipId]/(workspace)/payroll/_ui/LeavesAccordion";
import TimesheetsAccordion from "@/app/org-ws/[membershipId]/(workspace)/payroll/_ui/TimesheetsAccordion";

export default function Page() {
  return (
    <div className="space-y-4 p-4">
      <HStack>
        <ApprovalIcon />

        <h1 className="text-xl font-semibold text-amber-800">
          42 contractors required attention.{" "}
          <a href="" className="text-underline-link text-base font-normal">
            Unable to approve? Request change of approver here.
          </a>
        </h1>
      </HStack>

      <HStack className="hidden">
        <ClapIcon />
        <p>All caught up. Nothing to approve for now.</p>
      </HStack>

      <div>
        <LeavesAccordion />
        <TimesheetsAccordion />
      </div>
    </div>
  );
}

const ClapIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="21.178">
      <path
        fill="#FF9300"
        fillRule="nonzero"
        d="M7.096 3.597c.296-.061.505-.366.435-.644L6.983.438A.548.548 0 0 0 6.33.012a.562.562 0 0 0-.435.652l.549 2.506a.55.55 0 0 0 .652.427Zm-3.246.861a.549.549 0 0 0 .774-.774L2.807 1.865a.534.534 0 0 0-.774 0 .534.534 0 0 0 0 .775L3.85 4.458Zm6.596 14.733c2.715 2.715 6.092 2.55 8.537.104 1.95-1.958 2.35-3.985 1.514-6.44-.435-1.383-1.166-2.845-2.054-4.498-.356-.679-.783-1.567-1.079-1.862-.33-.331-.791-.305-1.14.043-.391.392-.382.862-.087 1.732l.949 2.654c.113.278.087.452-.017.557-.122.122-.279.139-.505-.079l-6.448-6.456a.951.951 0 0 0-1.323 0 .951.951 0 0 0 0 1.322l4.612 4.612a4.014 4.014 0 0 0-.687.4L7.358 5.93a.924.924 0 0 0-1.315 0 .924.924 0 0 0 0 1.314l5.282 5.29a9.298 9.298 0 0 0-.504.653L5.982 8.348a.94.94 0 0 0-1.314 0 .935.935 0 0 0 0 1.323l5.256 5.256a5.909 5.909 0 0 0-.235.87l-3.654-3.655a.935.935 0 0 0-1.323 0 .924.924 0 0 0 0 1.314l5.734 5.735ZM14.928 6.66l-1.714-1.714a.951.951 0 0 0-1.323 0c-.017.017-.026.026-.044.052l3.16 3.167c-.105-.54-.14-1.035-.08-1.505Zm-11.956.696a.55.55 0 0 0 .644-.426.55.55 0 0 0-.427-.644l-2.523-.54a.549.549 0 0 0-.662.453.567.567 0 0 0 .435.626l2.533.531Zm19.091 11.948c1.967-1.967 2.359-4.003 1.523-6.457-.435-1.375-1.166-2.828-2.053-4.49-.357-.679-.784-1.567-1.08-1.854-.33-.33-.791-.313-1.14.035a.896.896 0 0 0-.252.4c.192.357.366.723.557 1.07.853 1.602 1.593 3.107 2.019 4.517.957 2.95.374 5.421-1.819 7.605a8.207 8.207 0 0 1-1.192.984c1.236-.174 2.428-.8 3.437-1.81Z"
      />
    </svg>
  );
};

const ApprovalIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25.87" height="31.018">
      <defs>
        <linearGradient
          id="a"
          x1="33.508%"
          x2="70.85%"
          y1="31.873%"
          y2="84.432%"
        >
          <stop offset="0%" stopColor="#74BC00" />
          <stop offset="100%" stopColor="#489E00" />
        </linearGradient>
        <linearGradient id="b" x1="71.366%" x2="50%" y1="-.891%" y2="88.347%">
          <stop offset="0%" stopColor="#FB5050" />
          <stop offset="100%" stopColor="#E13A87" />
        </linearGradient>
      </defs>
      <g fill="none" fillRule="nonzero">
        <path
          fill="url(#a)"
          d="M3 7.456H0v7h3c.55 0 1-.394 1-.875V8.33c0-.482-.45-.875-1-.875Zm14.643 2.398c-.086-.666-.788-1.102-1.55-1.2-.166-.017-.348-.044-.53-.053.061-.009.122-.009.174-.018.762-.089 1.326-.542 1.248-1.2-.087-.657-.789-1.101-1.552-1.199-.364-.044-.78-.08-1.23-.107h-.009l-.815-.026a37.344 37.344 0 0 1-1.724-.09c-.39-.026-.442-.32-.408-.603a8.855 8.855 0 0 0 .538-3.324c-.035-.986-.642-1.928-1.517-2.026-.728-.08-1.256.444-1.066 1.12.607 2.195-2.236 4.825-4.55 6.291v6.86c1.803 1.182 4.715 1.786 8.103 1.715.208 0 .425-.009.668-.017h.095c.234-.018.494-.036.789-.063.762-.07 1.664-.586 1.62-1.253-.034-.577-.433-.897-.944-1.066.294-.027.58-.044.858-.071.771-.071 1.343-.515 1.265-1.182-.07-.666-.763-1.11-1.525-1.217-.026 0-.052-.01-.078-.01.32-.017.615-.035.884-.052.788-.045 1.343-.543 1.256-1.209Z"
          transform="rotate(-10 17.518 1.533)"
        />
        <path
          fill="url(#b)"
          d="M2 1.003H0v7h2c.55 0 1-.45 1-1v-5c0-.55-.45-1-1-1Zm13.99 4.38c.08-.58-.44-1.02-1.15-1.05-.25-.01-.52-.03-.81-.05.02 0 .05-.01.07-.01.7-.1 1.34-.49 1.41-1.07.06-.58-.46-.97-1.17-1.04-.25-.02-.52-.04-.79-.06.47-.15.84-.42.87-.93.04-.58-.79-1.03-1.5-1.09-.27-.02-.51-.04-.73-.05h-.09c-.23-.02-.43-.02-.62-.03-3.13-.05-5.82.47-7.48 1.51v6c2.14 1.29 4.76 3.59 4.21 5.51-.18.59.31 1.05.98.98.81-.09 1.37-.91 1.4-1.78.04-1-.15-2.01-.5-2.91-.04-.25.01-.5.37-.53.49-.03 1.11-.06 1.59-.08.26 0 .51-.01.75-.02h.01c.41-.02.8-.05 1.13-.09.7-.09 1.35-.47 1.43-1.05.08-.58-.44-.97-1.15-1.05-.05-.01-.11-.01-.16-.02.17-.01.33-.03.49-.05.72-.08 1.37-.46 1.44-1.04Z"
          transform="translate(9.872 17.007)"
        />
      </g>
    </svg>
  );
};
