import { PrimaryTabs, Tab } from "@/ui/PrimaryTabs";
import {
  FolderTreeIcon,
  TablePropertiesIcon,
  UsersIcon,
  CoinsIcon,
  KeyRoundIcon,
  ChartPieIcon,
} from "lucide-react";

export default async function ProjectPrimaryTab({ params }) {
  const { membershipId, projectId } = await params;
  const baseUrl = `/org-ws/${membershipId}/projects/${projectId}`;

  return (
    <PrimaryTabs className="border-t border-b">
      <Tab
        activeHrefs={[baseUrl]}
        href={`${baseUrl}/overview`}
        leftIcon={<TablePropertiesIcon />}
        exact
      >
        Overview
      </Tab>

      <Tab href={`${baseUrl}/contractors`} leftIcon={<UsersIcon />}>
        Contractors
      </Tab>

      <Tab href={`${baseUrl}/files`} leftIcon={<FolderTreeIcon />}>
        Files
      </Tab>

      <Tab href={`${baseUrl}/billing`} leftIcon={<CoinsIcon />}>
        Billing
      </Tab>

      <Tab href={`${baseUrl}/acl`} leftIcon={<KeyRoundIcon />}>
        Access Control
      </Tab>

      <Tab href={`${baseUrl}/reporting`} leftIcon={<ChartPieIcon />}>
        Reporting
      </Tab>
    </PrimaryTabs>
  );
}
