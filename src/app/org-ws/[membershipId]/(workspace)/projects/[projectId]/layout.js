import HStack from "@/ui/HStack";
import StickyHeader from "@/components/StickyHeader";
import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import { ClientAppBarDrawer } from "@/components/ClientAppBar";
import VStack from "@/ui/VStack";
import ProjectPrimaryTab from "@/app/org-ws/[membershipId]/(workspace)/projects/[projectId]/_ui/ProjectPrimaryTab";

const QUERY = `
  query clientProject($id: ID!) {
    clientProject(id: $id) {
      id
      fmId
      name
      fmIdName
      status

      createdAt
      updatedAt

      creator {
        id
        name
      }

      modifier {
        id
        name
      }
    }
  }
`;

export default async function Layout({ children, params }) {
  const { projectId } = await params;
  const res = await apiQuery(QUERY, { id: projectId });
  const project = res.data.clientProject;

  if (project === null) notFound();

  return (
    <div className="h-screen">
      <StickyHeader className="border-b-0">
        <HStack className="h-[52px] flex-nowrap px-4">
          <ClientAppBarDrawer />
          <VStack className="gap-0 overflow-hidden">
            <h1 className="w-full truncate text-2xl font-bold font-stretch-expanded dark:text-amber-500">
              {project.fmIdName}
            </h1>
          </VStack>
        </HStack>

        <ProjectPrimaryTab params={params} />
      </StickyHeader>

      {children}
    </div>
  );
}
