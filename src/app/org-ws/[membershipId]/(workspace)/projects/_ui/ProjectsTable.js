import StickyHeader from "@/components/StickyHeader";
import HStack from "@/ui/HStack";
import TableIcon from "@/icons/TableIcon";
import { H1 } from "@/ui/Typography";
import QueryInput from "@/ui/DataGrid/QueryInput";
import { ClientAppBarDrawer } from "@/components/ClientAppBar";
import decompressSearchParam from "@/lib/decompressSearchParam";
import apiQuery from "@/lib/apiQuery";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import Pagination from "@/ui/Pagination";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";

const QUERY = `
  query clientProjects($query: String, $sorts: [SortInput!], $page: Int!) {
    clientProjects(query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        fmId
        name
        status
        createdAt
        updatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "CLIENT_PROJECTS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 120,
    propertyName: "fmId",
    visible: true,
  },
  {
    columnName: "Name",
    columnWidth: 400,
    propertyName: "name",
    removable: false,
    visible: true,
    href: "/org-ws/${params.membershipId}/projects/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Status",
    columnWidth: 150,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Updated",
    columnWidth: 200,
    propertyName: "updatedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function ProjectsTable({ searchParams }) {
  let { page, query, sorts } = await searchParams;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "updatedAt", desc: true }]);

  const res = await apiQuery(QUERY, {
    query,
    sorts,
    page: currentPage,
  });

  const {
    records: projects,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(res.data.clientProjects, res.data.personalTableColumn);

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      <StickyHeader className="space-y-4 p-4 pb-2">
        <HStack>
          <ClientAppBarDrawer />
          <TableIcon />
          <H1>Project Management</H1>
        </HStack>

        <HStack className="w-full justify-between gap-x-8 gap-y-2">
          <QueryInput placeholder="Search for projects..." />

          <HStack className="flex-nowrap gap-8">
            <FieldsDialog
              tableName="CLIENT_PROJECTS"
              tableColumns={tableColumns}
              defaultTableColumns={DEFAULT_TABLE_COLUMNS}
            />
            <Button variant="plainIcon" prefix={<Filter size={16} />}>
              Filters
            </Button>
            <SortsDialog
              initialSorts={sorts}
              options={[
                { label: "ID", propertyName: "fmId" },
                { label: "Name", propertyName: "name" },
                { label: "Status", propertyName: "status" },
                { label: "Updated", propertyName: "updatedAt" },
              ]}
            />
          </HStack>
        </HStack>
      </StickyHeader>

      <DataGrid.Root className="space-y-2 p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            numRows={projects.length}
            data={projects}
            tableName="CLIENT_PROJECTS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
