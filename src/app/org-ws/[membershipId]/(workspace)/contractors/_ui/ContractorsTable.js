import StickyHeader from "@/components/StickyHeader";
import HStack from "@/ui/HStack";
import TableIcon from "@/icons/TableIcon";
import { H1 } from "@/ui/Typography";
import QueryInput from "@/ui/DataGrid/QueryInput";
import { ClientAppBarDrawer } from "@/components/ClientAppBar";
import decompressSearchParam from "@/lib/decompressSearchParam";
import apiQuery from "@/lib/apiQuery";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import Pagination from "@/ui/Pagination";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";

const QUERY = `
  query clientEmployments($query: String, $sorts: [SortInput!], $page: Int!) {
    clientEmployments(query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        fmId
        contractorName
        designation
        displayPeriod
        status
        createdAt
        updatedAt

        project {
          id
          fmId
          fmIdName
        }
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "CLIENT_EMPLOYMENTS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 100,
    propertyName: "fmId",
    removable: false,
    visible: true,
  },
  {
    columnName: "Name",
    columnWidth: 180,
    propertyName: "contractorName",
    removable: false,
    visible: true,
    href: "/org-ws/${params.membershipId}/contractors/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Designation",
    columnWidth: 300,
    propertyName: "designation",
    removable: false,
    visible: true,
  },
  {
    columnName: "Project",
    columnWidth: 300,
    propertyName: "project.fmIdName",
    visible: true,
  },
  {
    columnName: "Period",
    columnWidth: 230,
    propertyName: "displayPeriod",
    visible: true,
  },
  {
    columnName: "Status",
    columnWidth: 150,
    propertyName: "status",
    visible: true,
  },
];

export default async function ContractorsTable({ searchParams }) {
  let { page, query, sorts } = await searchParams;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [
    { id: "status", desc: false },
    { id: "contractorName", desc: false },
  ]);

  const res = await apiQuery(QUERY, {
    query,
    sorts,
    page: currentPage,
  });

  const {
    records: employments,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(
    res.data.clientEmployments,
    res.data.personalTableColumn,
  );

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      <StickyHeader className="space-y-4 p-4 pb-2">
        <HStack>
          <ClientAppBarDrawer />
          <TableIcon />
          <H1>Contractors &amp; Employments</H1>
        </HStack>

        <HStack className="w-full justify-between gap-x-8 gap-y-2">
          <QueryInput placeholder="Search for contractors..." />

          <HStack className="flex-nowrap gap-8">
            <FieldsDialog
              tableName="CLIENT_EMPLOYMENTS"
              tableColumns={tableColumns}
              defaultTableColumns={DEFAULT_TABLE_COLUMNS}
            />
            <Button variant="plainIcon" prefix={<Filter size={16} />}>
              Filters
            </Button>
            <SortsDialog
              initialSorts={sorts}
              options={[
                { label: "ID", propertyName: "fmId" },
                { label: "Name", propertyName: "contractorName" },
                { label: "Status", propertyName: "status" },
                { label: "Updated", propertyName: "updatedAt" },
              ]}
            />
          </HStack>
        </HStack>
      </StickyHeader>

      <DataGrid.Root className="space-y-2 p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            numRows={employments.length}
            data={employments}
            tableName="CLIENT_EMPLOYMENTS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
