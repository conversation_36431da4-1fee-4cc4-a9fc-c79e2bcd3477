import HStack from "@/ui/HStack";
import StickyHeader from "@/components/StickyHeader";
import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import { ClientAppBarDrawer } from "@/components/ClientAppBar";
import ContractorName from "@/app/ws/SR/[srid]/_ui/ContractorName";
import ContractorPrimaryTab from "@/app/org-ws/[membershipId]/(workspace)/contractors/[employmentId]/_ui/ContractorPrimaryTab";

const QUERY = `
  query clientEmployment($id: ID!) {
    clientEmployment(id: $id) {
      id
      fmId
      contractorName
      fmIdName
      status
      designation

      createdAt
      updatedAt

      company {
        id
        fmId
        name
        fmIdName
      }

      creator {
        id
        name
      }

      modifier {
        id
        name
      }
    }
  }
`;

export default async function Layout({ children, params }) {
  const { employmentId } = await params;
  const res = await apiQuery(QUERY, { id: employmentId });
  const employment = res.data.clientEmployment;

  if (employment === null) notFound();

  return (
    <div className="h-screen">
      <StickyHeader>
        <HStack className="flex-nowrap px-2">
          <ClientAppBarDrawer />
          <ContractorName employment={employment} />
        </HStack>

        <ContractorPrimaryTab params={params} />
      </StickyHeader>

      {children}
    </div>
  );
}
