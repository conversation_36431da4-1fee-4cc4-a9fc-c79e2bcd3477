import { PrimaryTabs, Tab } from "@/ui/PrimaryTabs";
import {
  FolderTreeIcon,
  TablePropertiesIcon,
  ScanFaceIcon,
  CoinsIcon,
  ChartPieIcon,
  SignatureIcon,
  BadgeMinusIcon,
  ShapesIcon,
} from "lucide-react";

export default async function ContractorPrimaryTab({ params }) {
  const { membershipId, employmentId } = await params;
  const baseUrl = `/org-ws/${membershipId}/contractors/${employmentId}`;

  return (
    <PrimaryTabs className="border-0 border-t">
      <Tab
        activeHrefs={[baseUrl]}
        href={`${baseUrl}/overview`}
        leftIcon={<TablePropertiesIcon />}
        exact
      >
        Overview
      </Tab>

      <Tab href={`${baseUrl}/work-passes`} leftIcon={<ScanFaceIcon />}>
        Passes
      </Tab>

      <Tab href={`${baseUrl}/agreements`} leftIcon={<SignatureIcon />}>
        Agreements
      </Tab>

      <Tab href={`${baseUrl}/closure`} leftIcon={<BadgeMinusIcon />}>
        Closure
      </Tab>

      <Tab href={`${baseUrl}/files`} leftIcon={<FolderTreeIcon />}>
        Files
      </Tab>

      <Tab href={`${baseUrl}/payroll`} leftIcon={<ShapesIcon />}>
        Payroll
      </Tab>

      <Tab href={`${baseUrl}/billing`} leftIcon={<CoinsIcon />}>
        Billing
      </Tab>

      <Tab href={`${baseUrl}/reporting`} leftIcon={<ChartPieIcon />}>
        Reporting
      </Tab>
    </PrimaryTabs>
  );
}
