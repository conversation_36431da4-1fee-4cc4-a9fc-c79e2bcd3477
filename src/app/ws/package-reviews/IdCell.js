"use client";

import useWorkspace from "@/hooks/useWorkspace";
import { ProgressBarLink } from "@/ui/ProgressBar";
import React from "react";
import useQuerySearchParams from "@/hooks/useQuerySearchParams";
import VStack from "@/ui/VStack";
import Highlighter from "react-highlight-words";

export default React.memo(function IdCell({ rowIndex, data }) {
  const { searchWords } = useQuerySearchParams();
  const { isCaWorkspace } = useWorkspace();

  const {
    id,
    fmId,
    contractorResourceUrl,
    staffResourceUrl,
    employment: { fmId: employmentFmId },
  } = data[rowIndex];
  const href = isCaWorkspace ? contractorResourceUrl : staffResourceUrl;

  return (
    <ProgressBarLink href={href} className="text-blue-700">
      <VStack className="gap-0">
        <span className="text-base">
          <Highlighter
            textToHighlight={fmId}
            autoEscape={true}
            searchWords={searchWords}
          />
        </span>
        <span className="text-muted text-sm">
          <Highlighter
            textToHighlight={employmentFmId}
            autoEscape={true}
            searchWords={searchWords}
          />
        </span>
      </VStack>
    </ProgressBarLink>
  );
});
