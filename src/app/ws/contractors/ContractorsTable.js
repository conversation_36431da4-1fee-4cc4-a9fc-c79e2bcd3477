import StickyHeader from "@/components/StickyHeader";
import { AppBarDrawer } from "@/components/AppBar";
import { H1 } from "@/ui/Typography";
import HStack from "@/ui/HStack";
import QueryInput from "@/ui/DataGrid/QueryInput";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import decompressSearchParam from "@/lib/decompressSearchParam";
import apiQuery from "@/lib/apiQuery";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import Pagination from "@/ui/Pagination";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import ContractorDrawer from "@/app/ws/contractors/ContractorDrawer";
import TableIcon from "@/icons/TableIcon";

const QUERY = `
  query contractors($query: String, $sorts: [SortInput!], $page: Int!, $peekId: ID) {
    contractors(query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        name
        email
        status
        createdAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "CONTRACTORS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }

    contractor(id: $peekId) {
      id
      name
      email
      status
      nationality
      dob
      gender
      race
      religion
      atsCandidateProfileId
      createdAt
      updatedAt
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 100,
    propertyName: "id",
    visible: true,
  },
  {
    columnName: "Name",
    columnWidth: 200,
    propertyName: "name",
    removable: false,
    visible: true,
    peek: true,
    href: "/ws/contractors/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Email",
    columnWidth: 300,
    propertyName: "email",
    visible: true,
  },
  {
    columnName: "Status",
    columnWidth: 150,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Created",
    columnWidth: 200,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function ContractorsTable({ searchParams }) {
  let { page, query, sorts, peek } = await searchParams;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "id", desc: true }]);

  const res = await apiQuery(QUERY, {
    query,
    sorts,
    page: currentPage,
    peekId: peek,
  });

  const {
    records: contractors,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(res.data.contractors, res.data.personalTableColumn);

  let contractor = null;
  if (peek) {
    contractor = res.data.contractor;
  }

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      {contractor && <ContractorDrawer contractor={contractor} />}

      <StickyHeader className="space-y-4 bg-[#FBFBFF] p-4 pb-2 text-[#344F7F] dark:bg-[#01061a] dark:text-[#7f9ed3]">
        <HStack>
          <AppBarDrawer />
          <TableIcon />
          <H1>Contractor Database</H1>
        </HStack>

        <HStack className="w-full justify-between gap-x-8 gap-y-2">
          <QueryInput placeholder="Search contractors by name, email, etc." />

          <HStack className="flex-nowrap gap-8">
            <FieldsDialog
              tableName="CONTRACTORS"
              tableColumns={tableColumns}
              defaultTableColumns={DEFAULT_TABLE_COLUMNS}
            />
            <Button variant="plainIcon" prefix={<Filter size={16} />}>
              Filters
            </Button>
            <SortsDialog
              initialSorts={sorts}
              options={[
                { label: "ID", propertyName: "id" },
                { label: "Name", propertyName: "name" },
                { label: "Date", propertyName: "createdAt" },
                { label: "Status", propertyName: "status" },
              ]}
            />
          </HStack>
        </HStack>
      </StickyHeader>

      <DataGrid.Root className="p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            numRows={contractors.length}
            data={contractors}
            tableName="CONTRACTORS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
