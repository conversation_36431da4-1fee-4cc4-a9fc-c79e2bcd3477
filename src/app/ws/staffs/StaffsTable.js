import StickyHeader from "@/components/StickyHeader";
import { AppBarDrawer } from "@/components/AppBar";
import { H1 } from "@/ui/Typography";
import HStack from "@/ui/HStack";
import QueryInput from "@/ui/DataGrid/QueryInput";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import decompressSearchParam from "@/lib/decompressSearchParam";
import apiQuery from "@/lib/apiQuery";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import Pagination from "@/ui/Pagination";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import ContractorDrawer from "@/app/ws/contractors/ContractorDrawer";
import TableIcon from "@/icons/TableIcon";
import EmailCell from "@/ui/DataGrid/cells/EmailCell";

const QUERY = `
  query staffs($query: String, $sorts: [SortInput!], $page: Int!) {
    staffs(query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        name
        email
        status
        createdAt
        updatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "STAFFS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 100,
    propertyName: "id",
    removable: false,
    visible: true,
  },
  {
    columnName: "Name",
    columnWidth: 200,
    propertyName: "name",
    removable: false,
    visible: true,
    href: "/ws/staffs/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Email",
    columnWidth: 300,
    propertyName: "email",
    visible: true,
    cellComponent: EmailCell,
  },
  {
    columnName: "Status",
    columnWidth: 150,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Modified",
    columnWidth: 200,
    propertyName: "updatedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function StaffsTable({ searchParams }) {
  let { page, query, sorts } = await searchParams;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [
    { id: "status", desc: false },
    { id: "updatedAt", desc: true },
  ]);

  const res = await apiQuery(QUERY, {
    query,
    sorts,
    page: currentPage,
  });

  const {
    records: staffs,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(res.data.staffs, res.data.personalTableColumn);

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      <StickyHeader className="space-y-4 bg-[#FFFBF5] p-4 pb-2 text-[#92400e] dark:bg-[#01061a] dark:text-[#7f9ed3]">
        <HStack>
          <AppBarDrawer />
          <TableIcon />
          <H1>Staff Database</H1>
        </HStack>

        <HStack className="w-full justify-between gap-x-8 gap-y-2">
          <QueryInput placeholder="Search staffs by name, email, etc." />

          <HStack className="flex-nowrap gap-8">
            <FieldsDialog
              tableName="STAFFS"
              tableColumns={tableColumns}
              defaultTableColumns={DEFAULT_TABLE_COLUMNS}
            />
            <Button variant="plainIcon" prefix={<Filter size={16} />}>
              Filters
            </Button>
            <SortsDialog
              initialSorts={sorts}
              options={[
                { label: "ID", propertyName: "id" },
                { label: "Name", propertyName: "name" },
                { label: "Updated", propertyName: "updatedAt" },
                { label: "Status", propertyName: "status" },
              ]}
            />
          </HStack>
        </HStack>
      </StickyHeader>

      <DataGrid.Root className="p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            numRows={staffs.length}
            data={staffs}
            tableName="STAFFS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
