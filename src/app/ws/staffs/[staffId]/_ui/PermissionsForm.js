import { Form } from "@/ui/Form";
import Checkbox from "@/ui/Checkbox";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Button from "@/ui/Button";
import VStack from "@/ui/VStack";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

const MUTATION = `
  mutation updateRolePermission($id: ID!, $permissions: [String!]!) {
    execute:updateRolePermission(id: $id, permissions: $permissions) {
      userErrors {
        path
        message
      }

      success
    }
  }
`;

export default function PermissionsForm({ selectedRolePermission }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      permissions: selectedRolePermission?.permissions || [],
    },
  });
  const {
    register,
    formState: { errors },
    reset,
  } = form;

  useEffect(() => {
    if (selectedRolePermission) {
      reset({
        permissions: selectedRolePermission.permissions || [],
      });
    }
  }, [reset, selectedRolePermission]);

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Permission updated");
      router.refresh();
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    execute({
      id: selectedRolePermission.id,
      permissions: input.permissions,
    });
  };

  if (!selectedRolePermission) {
    return (
      <div className="p-2 text-center">Select a role to view permissions</div>
    );
  }

  return (
    <div className="p-4">
      <VStack className="gap-0 text-lg font-bold">
        Set Permissions for &quot;{selectedRolePermission.roleName}&quot;
        <span className="font-mono text-xs font-normal text-muted">
          {selectedRolePermission.id}
        </span>
      </VStack>

      <div className="mt-4 space-y-2">
        <Form
          form={form}
          onSubmit={onSubmit}
          formData={formData}
          className="space-y-4"
        >
          <div className="space-y-2">
            <h2 className="font-semibold text-rose-700">Project permissions</h2>

            <ul>
              <li>
                <Checkbox
                  suffix="project:list"
                  value="project:list"
                  {...register("permissions")}
                />
              </li>
              <li>
                <Checkbox
                  suffix="project:view"
                  value="project:view"
                  {...register("permissions")}
                />
              </li>
              <li>
                <Checkbox
                  suffix="project:edit"
                  value="project:edit"
                  {...register("permissions")}
                />
              </li>
            </ul>
          </div>

          <div className="space-y-2">
            <h2 className="font-semibold text-rose-700">Invoice permissions</h2>

            <ul>
              <li>
                <Checkbox
                  suffix="invoice:list"
                  value="invoice:list"
                  {...register("permissions")}
                />
              </li>
              <li>
                <Checkbox
                  suffix="invoice:view"
                  value="invoice:view"
                  {...register("permissions")}
                />
              </li>
              <li>
                <Checkbox
                  suffix="invoice:edit"
                  value="invoice:edit"
                  {...register("permissions")}
                />
              </li>
            </ul>
          </div>

          <Button fullWidth type="submit" variant="primary" loading={pending}>
            Save permissions for &quot;{selectedRolePermission.roleName}&quot;
          </Button>
        </Form>
      </div>
    </div>
  );
}
