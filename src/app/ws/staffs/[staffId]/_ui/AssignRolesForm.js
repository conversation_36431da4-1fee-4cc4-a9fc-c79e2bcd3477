import VStack from "@/ui/VStack";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { isEmpty } from "lodash";
import { Form, FormBody, FormSection } from "@/ui/Form";
import Button from "@/ui/Button";
import Checkbox from "@/ui/Checkbox";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

const MUTATION = `
  mutation assignStaffRoles($staffId: ID!, $roles: [String!]!) {
    execute:assignStaffRoles(staffId: $staffId, roles: $roles) {
      userErrors {
        path
        message
      }

      success
    }
  }
`;

export default function AssignRolesForm({ staff, rolePermissions }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();

  // We filter out any foreign element from the staff roles which is not in the official
  // rolePermissions list. This can happen if a role is deleted after being assigned.
  const form = useForm({
    defaultValues: {
      roles: [
        ...staff.roles.filter((r) =>
          rolePermissions.map((rp) => rp.roleName).includes(r),
        ),
      ],
    },
  });
  const { register } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Roles assigned successfully");
      router.refresh();
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    execute({
      staffId: staff.id,
      roles: input.roles,
    });
  };

  return (
    <div className="space-y-4">
      <VStack className="gap-0">
        <h1 className="text-lg font-bold">Assign roles for {staff.name}</h1>
        <p className="text-sm text-muted">Only admin can assign role.</p>
      </VStack>

      {isEmpty(rolePermissions) && (
        <p className="text-center text-xl text-rose-700">
          No roles available. Please create one.
        </p>
      )}

      <Form form={form} onSubmit={onSubmit} formData={formData}>
        <FormBody className="mx-auto max-w-[500px] space-y-4">
          <FormSection className="space-y-1">
            {rolePermissions.map((role) => (
              <Checkbox
                key={role.id}
                suffix={role.roleName}
                value={role.roleName}
                {...register("roles")}
              />
            ))}
          </FormSection>

          <Button type="submit" fullWidth variant="success" loading={pending}>
            Assign
          </Button>
        </FormBody>
      </Form>
    </div>
  );
}
