"use client";

import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";
import { UserPlusIcon, PlusIcon } from "lucide-react";
import HStack from "@/ui/HStack";

export default function ManageAccessSegmentedControl({ value, onValueChange }) {
  return (
    <SegmentedControlRoot
      value={value}
      onValueChange={(v) => {
        if (v) {
          // Ensure it always has a value
          onValueChange(v);
        }
      }}
      style={{ "--segmented-control-border-radius": "9999px" }}
    >
      <SegmentedControlItem value="Assign roles">
        <HStack>
          <UserPlusIcon size={20} />
          Assign roles
        </HStack>
      </SegmentedControlItem>
      <SegmentedControlItem value="Create role">
        <HStack>
          <PlusIcon size={20} />
          Create role
        </HStack>
      </SegmentedControlItem>
    </SegmentedControlRoot>
  );
}
