"use client";

import { PanelGroup, Panel, PanelResizeHandle } from "react-resizable-panels";
import HStack from "@/ui/HStack";
import { useState } from "react";
import StaffRolePermissionsList from "@/app/ws/staffs/[staffId]/_ui/StaffRolePermissionsList";
import PermissionsForm from "@/app/ws/staffs/[staffId]/_ui/PermissionsForm";

export default function RolePermissionPanel() {
  const [selectedRolePermission, setSelectedRolePermission] = useState(null);
  const [editMode, setEditMode] = useState(false);

  return (
    <PanelGroup direction="horizontal">
      <Panel defaultSize={50} minSize={30} maxSize={70}>
        <div className="p-4 pl-0">
          <HStack className="justify-between">
            <h1 className="text-lg font-bold">Roles</h1>

            <button
              onClick={() => setEditMode((prev) => !prev)}
              className="text-underline-link cursor-pointer"
            >
              {editMode ? "Done" : "Edit"}
            </button>
          </HStack>

          <StaffRolePermissionsList
            editMode={editMode}
            selectedRolePermission={selectedRolePermission}
            setSelectedRolePermission={setSelectedRolePermission}
          />
        </div>
      </Panel>

      <PanelResizeHandle className="w-1 cursor-col-resize rounded-full bg-border/50 transition-colors hover:bg-border" />

      <Panel defaultSize={50} minSize={30} maxSize={70}>
        <PermissionsForm selectedRolePermission={selectedRolePermission} />
      </Panel>
    </PanelGroup>
  );
}
