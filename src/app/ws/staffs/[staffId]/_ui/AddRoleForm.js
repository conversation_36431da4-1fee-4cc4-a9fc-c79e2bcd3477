"use client";

import { Form, FormRow } from "@/ui/Form";
import { useForm } from "react-hook-form";
import { useState } from "react";
import Input from "@/ui/Input";
import Button from "@/ui/Button";
import { CirclePlusIcon } from "lucide-react";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import HStack from "@/ui/HStack";

const MUTATION = `
  mutation createRolePermission($roleName: String!) {
    execute:createRolePermission(roleName: $roleName) {
      userErrors {
        path
        message
      }

      success
      successIndexes
    }
  }
`;

export default function AddRoleForm() {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm();
  const {
    register,
    formState: { errors },
    reset,
  } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Role created successfully");
      reset();
      router.refresh();
    },
  });

  const onSubmit = (input) => {
    setFormData(input);
    execute({
      roleName: input.roleName,
    });
  };

  return (
    <div>
      <Form form={form} onSubmit={onSubmit} formData={formData}>
        <HStack className="flex-nowrap gap-4">
          <Input
            label="Role name"
            {...register("roleName", { required: true })}
            error={errors.roleName}
          />

          <Button
            type="submit"
            className="max-w-[300px]"
            prefix={<CirclePlusIcon strokeWidth={2} size={20} />}
            loading={pending}
          >
            Create new role
          </Button>
        </HStack>
      </Form>
    </div>
  );
}
