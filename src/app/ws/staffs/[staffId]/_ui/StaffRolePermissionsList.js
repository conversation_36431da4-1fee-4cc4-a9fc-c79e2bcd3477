"use client";

import { useQuery } from "urql";
import { ChevronRightIcon } from "lucide-react";
import { Spinner } from "@blueprintjs/core";
import cn from "@/lib/cn";
import TrashIcon from "@/icons/emp/TrashIcon";
import Button from "@/ui/Button";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

const QUERY = `
  query rolePermissions($userType: String!) {
    rolePermissions(userType: $userType) {
      id
      roleName
      permissions
    }
  }
`;

const MUTATION = `
  mutation deleteRolePermission($id: ID!) {
    execute:deleteRolePermission(id: $id) {
      success
    }
  }
`;

export default function StaffRolePermissionsList({
  editMode,
  selectedRolePermission,
  setSelectedRolePermission,
}) {
  const router = useRouter();
  const [result] = useQuery({
    query: QUERY,
    variables: { userType: "STAFF" },
  });
  const { data, fetching } = result;
  const roles = data?.rolePermissions || [];

  const [execute] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Role removed");
      router.refresh();
    },
  });

  if (fetching) return <Spinner />;

  return (
    <ul className="mt-4 space-y-1">
      {roles.map((role) => (
        <li
          key={role.id}
          className={cn(
            "flex h-[44px] items-center justify-between border p-2 transition-colors first:rounded-t-lg last:rounded-b-lg hover:bg-gray-100",
            { "bg-gray-100 font-bold": role.id === selectedRolePermission?.id },
          )}
        >
          <button
            className="w-full cursor-pointer text-left"
            onClick={() => {
              setSelectedRolePermission(role);
            }}
          >
            {role.roleName}
          </button>
          {editMode ? (
            <Button
              prefix={<TrashIcon color="red" />}
              variant="ghost"
              onClick={() => execute({ id: role.id })}
            />
          ) : (
            <Button
              prefix={<ChevronRightIcon size={20} strokeWidth={1.5} />}
              variant="ghost"
              onClick={() => setSelectedRolePermission(role)}
            />
          )}
        </li>
      ))}
    </ul>
  );
}
