"use client";

import {
  Di<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTrigger,
} from "@/ui/Dialog";
import { useState } from "react";
import Button from "@/ui/Button";
import { PlusIcon } from "lucide-react";
import AddRoleForm from "@/app/ws/staffs/[staffId]/_ui/AddRoleForm";
import RolePermissionPanel from "@/app/ws/staffs/[staffId]/_ui/RolePermissionPanel";
import ManageAccessSegmentedControl from "@/app/ws/staffs/[staffId]/_ui/ManageAccessSegmentedControl";
import AssignRolesForm from "@/app/ws/staffs/[staffId]/_ui/AssignRolesForm";

export default function AssignMoreRolesDialog({ staff, rolePermissions }) {
  const [open, setOpen] = useState(false);
  const [segmentedValue, setSegmentedValue] = useState("Assign roles");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" prefix={<PlusIcon size={20} />}>
          Manage access
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>Manage access by assigning roles</DialogHeader>

        <div className="mt-2 text-center">
          <ManageAccessSegmentedControl
            value={segmentedValue}
            onValueChange={setSegmentedValue}
          />
        </div>

        {segmentedValue === "Assign roles" && (
          <div className="mt-4 space-y-4 pb-4">
            <AssignRolesForm staff={staff} rolePermissions={rolePermissions} />
          </div>
        )}

        {segmentedValue === "Create role" && (
          <div className="mt-4 space-y-4">
            <AddRoleForm />

            <div className="border-t py-2">
              <RolePermissionPanel />
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
