"use client";

import { useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "urql";
import { isEmpty } from "lodash";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import { ChevronsUpDown, Search } from "lucide-react";
import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/ui/Command";
import { Spinner } from "@blueprintjs/core";
import Highlighter from "react-highlight-words";

const QUERY = `
  query staffs($query: String) {
    staffs(query: $query, page: 1, perPage: 100) {
      nodes {
        id
        name
      }
    }
  }
`;

export default function StaffSwitcher({ currentStaff }) {
  const [query, setQuery] = useState(currentStaff.name);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(currentStaff);
  const router = useRouter();

  const [result] = useQuery({
    query: QUERY,
    variables: { query },
    pause: isEmpty(query) || query === currentStaff.name,
  });

  const { data, fetching } = result;
  const items = data?.staffs?.nodes || [];

  return (
    <Popover>
      <PopoverTrigger>
        <Button
          variant="ghost"
          className="justify-between font-semibold"
          suffix={<ChevronsUpDown size={20} strokeWidth={2} />}
          onClick={() => setIsOpen(!isOpen)}
        >
          <HStack className="max-w-[500px] gap-2 overflow-hidden text-ellipsis">
            {selectedItem ? (
              <StaffName staff={selectedItem} />
            ) : (
              "Choose a staff"
            )}
          </HStack>
        </Button>
      </PopoverTrigger>

      <PopoverContent align="start" className="min-w-96">
        <Command className="w-full" shouldFilter={false}>
          <div className="border-b p-2">
            <CommandInput
              placeholder="Search for a staff..."
              prefix={<Search className="ml-0" size={20} />}
              suffix={fetching && <Spinner size={20} />}
              size="sm"
              borderless
              value={query}
              onValueChange={(newValue) => {
                setQuery(newValue);

                if (newValue === "") {
                  setSelectedItem(currentStaff);
                }
              }}
            />
          </div>

          <CommandList className="p-2">
            <CommandEmpty>No staffs found.</CommandEmpty>

            {items.map((item) => {
              return (
                <CommandItem
                  key={item.id}
                  value={item.name}
                  onSelect={() => {
                    setQuery(item.name);
                    setSelectedItem(item);

                    const url = `/ws/staffs/${item.id}`;
                    router.push(url);
                  }}
                >
                  <HStack className="w-full justify-between">
                    <Highlighter
                      textToHighlight={item.name}
                      autoEscape={true}
                      searchWords={query.split(/\s+/)}
                    />

                    <div className="font-mono text-sm text-muted tabular-nums">
                      {item.id}
                    </div>
                  </HStack>
                </CommandItem>
              );
            })}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

const StaffName = ({ staff }) => {
  return (
    <HStack className="flex-nowrap gap-1">
      <span className="font-normal text-muted">{staff.id}:</span>
      <span>{staff.name}</span>
    </HStack>
  );
};
