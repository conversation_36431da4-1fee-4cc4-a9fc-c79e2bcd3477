import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import Card from "@/ui/Card";
import { PropertyList, List } from "@/ui/PropertyList";
import { InfoIcon } from "lucide-react";
import AssignMoreRolesDialog from "@/app/ws/staffs/[staffId]/_ui/AssignMoreRolesDialog";

const QUERY = `
  query staff($id: ID!) {
    staff(id: $id) {
      id
      name
      email
      fullName
      personalEmail
      status

      roles
      rolePermissions {
        id
        roleName
        permissions
      }

      createdAt
      updatedAt

      creator {
        id
        name
      }

      modifier {
        id
        name
      }
    }

    rolePermissions(userType: "STAFF") {
      id
      roleName
    }
  }
`;

export default async function Page({ params }) {
  const { staffId } = await params;
  const res = await apiQuery(QUERY, { id: staffId });
  const staff = res.data.staff;
  const rolePermissions = res.data.rolePermissions;

  if (staff === null) notFound();

  return (
    <div className="mx-auto max-w-[600px] space-y-4 p-4 pt-2">
      <Card className="space-y-4">
        <PropertyList title="Personal info">
          <List label="ID">{staff.id}</List>
          <List label="Name">
            <strong>{staff.name}</strong>
          </List>
          <List label="Full name">{staff.fullName}</List>
          <List.Status label="Status">{staff.status}</List.Status>
          <List.Email label="Email">{staff.email}</List.Email>
          <List.Email label="Personal email">{staff.personalEmail}</List.Email>
        </PropertyList>

        <PropertyList title="Address"></PropertyList>

        <PropertyList title="Bank info"></PropertyList>

        <PropertyList title="Roles assigned">
          <div className="space-y-0.5">
            {staff.rolePermissions.map((role) => (
              <div
                key={role.id}
                className="flex items-center justify-between border p-2 first:rounded-t-lg last:rounded-b-lg"
              >
                {role.roleName}
                <span className="flex items-center gap-1 text-link">
                  <InfoIcon size={16} /> View permissions
                </span>
              </div>
            ))}

            <div className="flex items-center justify-center gap-1 border p-2 text-base text-green-700 first:rounded-t-lg last:rounded-b-lg">
              <AssignMoreRolesDialog
                staff={staff}
                rolePermissions={rolePermissions}
              />
            </div>
          </div>
        </PropertyList>
      </Card>
    </div>
  );
}
