import { AppBarDrawer } from "@/components/AppBar";
import StickyHeader from "@/components/StickyHeader";
import HStack from "@/ui/HStack";
import { PrimaryTabs, Tab } from "@/ui/PrimaryTabs";
import { KeyRoundIcon, ShapesIcon, TablePropertiesIcon } from "lucide-react";
import StaffSwitcher from "@/app/ws/staffs/[staffId]/StaffSwitcher";
import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";

const QUERY = `
  query staff($id: ID!) {
    staff(id: $id) {
      id
      name
    }
  }
`;

export default async function Layout({ children, params }) {
  const { staffId } = await params;
  const res = await apiQuery(QUERY, { id: staffId });
  const staff = res.data.staff;

  if (staff === null) notFound();

  const baseUrl = `/ws/staffs/${staffId}`;

  return (
    <div className="h-screen overflow-scroll">
      <StickyHeader>
        <HStack className="flex-nowrap px-2">
          <AppBarDrawer />
          <HStack className="w-full justify-between py-2">
            <StaffSwitcher currentStaff={staff} />
          </HStack>
        </HStack>

        <PrimaryTabs className="border-t border-b-0">
          <Tab href={`${baseUrl}`} leftIcon={<TablePropertiesIcon />} exact>
            Profile
          </Tab>
          <Tab href={`${baseUrl}/my-hr`} leftIcon={<ShapesIcon />}>
            My HR
          </Tab>
        </PrimaryTabs>
      </StickyHeader>

      {children}
    </div>
  );
}
