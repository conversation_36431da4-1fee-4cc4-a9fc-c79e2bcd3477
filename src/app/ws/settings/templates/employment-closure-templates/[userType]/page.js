import apiQuery from "@/lib/apiQuery";
import decompressSearchParam from "@/lib/decompressSearchParam";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import { DataGrid } from "@/ui/DataGrid";
import NewTemplateDialog from "@/app/ws/settings/templates/employment-closure-templates/_ui/NewTemplateDialog";
import Pagination from "@/ui/Pagination";
import TemplateClientTable from "@/app/ws/settings/templates/employment-closure-templates/_ui/TemplateClientTable";
import TextCell from "@/ui/DataGrid/cells/TextCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import HtmlCell from "@/ui/DataGrid/cells/HtmlCell";
import { upperFirst } from "lodash";

const QUERY = `
  query employmentClosureTemplates($userType: String!, $query: String, $sorts: [SortInput!], $page: Int!) {
    employmentClosureTemplates(userType: $userType, query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        userType
        name
        body
        createdAt
        updatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 60,
    propertyName: "id",
    visible: true,
    cellComponent: TextCell,
  },
  {
    columnName: "Name",
    columnWidth: 300,
    propertyName: "name",
    visible: true,
  },
  {
    columnName: "Template Body",
    columnWidth: 300,
    propertyName: "body",
    visible: true,
    cellComponent: HtmlCell,
  },
  {
    columnName: "Updated",
    columnWidth: 220,
    propertyName: "updatedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function Page({ params, searchParams }) {
  const { userType } = await params;
  const normalizedUserType = upperFirst(userType || "client");
  let { page, query, sorts } = await searchParams;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "updatedAt", desc: true }]);
  const res = await apiQuery(QUERY, {
    userType: normalizedUserType,
    query,
    sorts,
    page: currentPage,
  });

  const { records, totalPages, totalCount, processingTimeMs } =
    getTableMetadata(res.data.employmentClosureTemplates);

  return (
    <div className="space-y-4">
      <h1 className="flex items-center justify-between text-xl font-semibold">
        {normalizedUserType} employment closure templates
        <NewTemplateDialog />
      </h1>

      <DataGrid.Root className="space-y-2">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <TemplateClientTable
            data={records}
            tableColumns={DEFAULT_TABLE_COLUMNS}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </div>
  );
}
