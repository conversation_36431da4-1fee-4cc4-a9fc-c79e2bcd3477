"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  DialogFooterWithCTA,
} from "@/ui/Dialog";
import Button from "@/ui/Button";
import { useForm } from "react-hook-form";
import { Form, FormBody, FormRow, showFormErrors } from "@/ui/Form";
import Input from "@/ui/Input";
import { useState } from "react";
import { toast } from "sonner";
import { useParams, useRouter } from "next/navigation";
import ClosureTemplateEditor from "@/app/ws/settings/templates/employment-closure-templates/_ui/ClosureTemplateEditor";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { upperFirst } from "lodash";

const MUTATION = `
  mutation createEmploymentClosureTemplate($input: EmploymentClosureTemplateInput!) {
    execute:createEmploymentClosureTemplate(input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on EmploymentClosureTemplate {
          id
        }
      }
    }
  }
`;

export default function NewTemplateDialog() {
  const { userType } = useParams();
  const normalizedUserType = upperFirst(userType || "client");
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const form = useForm({
    defaultValues: {
      userType: normalizedUserType,
      name: "",
      body: "",
    },
  });
  const router = useRouter();
  const {
    register,
    reset,
    formState: { errors },
    setError,
  } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      router.refresh();
      toast.success(`${normalizedUserType} template created successfully`);
      reset();
      setOpen(false);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);
    execute({ input });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="secondary">New {userType || client} template</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          New {userType || "client"} contract closure template
        </DialogHeader>

        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody className="mx-auto max-w-[700px] py-4">
            <FormRow>
              <Input
                data-1p-ignore
                label="Name"
                {...register("name", { required: "Name is required!" })}
                error={errors.name}
              />
            </FormRow>

            <FormRow>
              <ClosureTemplateEditor />
            </FormRow>
          </FormBody>

          <DialogFooterWithCTA label="Create" pending={pending} />
        </Form>
      </DialogContent>
    </Dialog>
  );
}
