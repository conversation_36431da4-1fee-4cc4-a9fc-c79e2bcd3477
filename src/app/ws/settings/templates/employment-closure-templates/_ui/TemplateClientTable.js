"use client";

import DataGridTable from "@/ui/DataGrid/DataGridTable";
import { Drawer, DrawerContent } from "@/ui/Drawer";
import { useEffect, useState } from "react";
import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import ClosureTemplateEditor from "@/app/ws/settings/templates/employment-closure-templates/_ui/ClosureTemplateEditor";
import { useForm } from "react-hook-form";
import { Form, FormRow, showFormErrors } from "@/ui/Form";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Input from "@/ui/Input";
import { XCircleIcon } from "@phosphor-icons/react";
import jsbeautify from "js-beautify";
import SyntaxHighlighter from "react-syntax-highlighter";

export default function TemplateClientTable({
  data,
  tableColumns,
  defaultTableColumns,
}) {
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [open, setOpen] = useState(false);

  return (
    <>
      {selectedRecord && open && (
        <SelectedRecordDrawer
          record={selectedRecord}
          open={open}
          setOpen={setOpen}
        />
      )}

      <DataGridTable
        data={data}
        numRows={data.length}
        tableColumns={tableColumns}
        defaultTableColumns={defaultTableColumns}
        onSelection={(regions) => {
          const rows = regions.map((s) => s.rows).filter(Boolean);

          // Note: rows can be empty [] if click on the header
          if (rows.length > 0) {
            // We only ever allow single row selection
            const record = data[rows[0][0]];
            setSelectedRecord(record);
            setOpen(true);
          }
        }}
      />
    </>
  );
}

const MUTATION = `
  mutation updateEmploymentClosureTemplate($id: ID!, $input: EmploymentClosureTemplateInput!) {
    execute:updateEmploymentClosureTemplate(id: $id, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on EmploymentClosureTemplate {
          id
        }
      }
    }
  }
`;

const SelectedRecordDrawer = ({ record, open, setOpen }) => {
  const form = useForm({
    defaultValues: {
      name: record.name,
      body: record.body,
    },
  });
  const router = useRouter();
  const {
    register,
    reset,
    formState: { errors },
    setError,
  } = form;

  // We must reset the record using useEffect, or else the TiptapEditor will not reflect
  // the new content even though the editor content looks correct and updated. It is the
  // underlying RHF form state that is not updated.
  useEffect(() => {
    if (record) {
      reset({
        name: record.name,
        body: record.body,
      });
    }
  }, [record, reset]);

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      router.refresh();
      toast.success("Template updated successfully");
      // reset();
      // setOpen(false);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    execute({ id: record.id, input });
  };

  return (
    <Drawer open={open} setOpen={setOpen}>
      <DrawerContent
        side="right"
        className="w-1/2"
        onInteractOutside={() => setOpen(false)}
        onEscapeKeyDown={() => setOpen(false)}
      >
        <HStack className="justify-between p-4">
          <h1 className="text-lg font-semibold">{record.name}</h1>
          <Button variant="plainIcon" onClick={() => setOpen(false)}>
            <span className="text-gray-300 dark:text-neutral-500">
              <XCircleIcon size="32" weight="fill" />
            </span>
          </Button>
        </HStack>
        <section className="space-y-4 p-4">
          <Form form={form} onSubmit={onSubmit} className="space-y-4">
            <HStack className="justify-end">
              <Button type="submit" loading={pending}>
                Update
              </Button>
            </HStack>

            <FormRow>
              <Input
                data-1p-ignore
                label="Name"
                {...register("name", { required: "Name is required!" })}
                error={errors.name}
              />
            </FormRow>

            <ClosureTemplateEditor defaultValue={record.body} />
          </Form>

          <SyntaxHighlighter language="html">
            {jsbeautify.html(record.body, { indent_size: 2 })}
          </SyntaxHighlighter>
          {/*<RawHtml>{record.body}</RawHtml>*/}
        </section>
      </DrawerContent>
    </Drawer>
  );
};
