import TiptapEditor from "@/ui/editors/TiptapEditor";
import ClosureVariableMention from "@/ui/editors/mentions/ClosureVariableMention";

export default function ClosureTemplateEditor({
  defaultValue,
  label = "Template",
  name = "body",
}) {
  return (
    <TiptapEditor
      label={label}
      name={name}
      enablePagebreak
      extensions={[ClosureVariableMention]}
      rules={{
        required: "Template is required!",
        validate: (value) => {
          const empty = '{"type":"doc","content":[{"type":"paragraph"}]}';

          if (JSON.stringify(value) === empty) {
            return false;
          }
        },
      }}
      defaultValue={defaultValue}
    />
  );
}
