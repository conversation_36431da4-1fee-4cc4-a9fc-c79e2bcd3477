import MailDetailsTable from "@/app/ws/SR/[srid]/logs/mail-log/MailDetailsTable";
import apiQuery from "@/lib/apiQuery";
import decompressSearchParam from "@/lib/decompressSearchParam";
import { DataGrid } from "@/ui/DataGrid";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import QueryInput from "@/ui/DataGrid/QueryInput";
import Pagination from "@/ui/Pagination";
import VStack from "@/ui/VStack";

const QUERY = `
  query mailLogsForEmployment($query: String, $sorts: [SortInput!], $page: Int!, $employmentId: ID!) {
    mailLogsForEmployment(employmentId: $employmentId, query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        subject
        textBody
        htmlBody
        status
        attachmentFilenames
        mailFrom
        mailTo
        mailCc
        mailBcc

        formattedMailFrom
        formattedMailTo
        formattedMailCc
        formattedMailBcc
        formattedAttachmentFilenames

        messageEvents
        metadata

        createdAt
        updatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "MAIL_LOGS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Subject",
    columnWidth: 300,
    propertyName: "subject",
    visible: true,
  },
  {
    columnName: "Status",
    columnWidth: 120,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "To",
    columnWidth: 200,
    propertyName: "formattedMailTo",
    visible: true,
  },
  {
    columnName: "From",
    columnWidth: 200,
    propertyName: "formattedMailFrom",
    visible: true,
  },
  {
    columnName: "CC",
    columnWidth: 200,
    propertyName: "formattedMailCc",
    visible: true,
  },
  {
    columnName: "Sent",
    columnWidth: 230,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
  // {
  //   columnName: "Record",
  //   columnWidth: 230,
  //   propertyName: "formattedRecordType",
  //   visible: true,
  // },
];

export default async function MailLogTable({ searchParams, params }) {
  let { page, query, sorts } = await searchParams;
  const { srid } = await params;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "id", desc: true }]);

  const res = await apiQuery(QUERY, {
    employmentId: srid,
    query,
    sorts,
    page: currentPage,
  });

  const {
    records: mailLogs,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(
    res.data.mailLogsForEmployment,
    res.data.personalTableColumn,
  );

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <div className="space-y-4">
      <VStack>
        <h1 className="text-xl font-bold">Mail Log</h1>

        <QueryInput placeholder="Search mail log" />
      </VStack>

      <DataGrid.Root>
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <MailDetailsTable
            data={mailLogs}
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </div>
  );
}
