import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import apiQuery from "@/lib/apiQuery";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import Pagination from "@/ui/Pagination";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import VStack from "@/ui/VStack";

const QUERY = `
  query actionLogsForEmployment($employmentId: ID!) {
    actionLogsForEmployment(employmentId: $employmentId, sorts: [{id: "createdAt", desc: true}]) {
      nodes {
        user {
          id
          name
          email
        }

        name
        log
        severity
        createdAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "EMPLOYMENT_ACTION_LOGS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Who",
    columnWidth: 150,
    propertyName: "user.name",
    visible: true,
  },
  {
    columnName: "Action",
    columnWidth: 300,
    propertyName: "name",
    visible: true,
  },
  {
    columnName: "Log",
    columnWidth: 400,
    propertyName: "log",
    visible: true,
  },
  {
    columnName: "Severity",
    columnWidth: 120,
    propertyName: "severity",
    visible: true,
  },
  {
    columnName: "Date",
    columnWidth: 220,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function EmploymentActionLogTable({
  searchParams,
  params,
}) {
  let { page } = await searchParams;
  const { srid } = await params;
  const currentPage = Number(page || 1);

  const res = await apiQuery(QUERY, {
    employmentId: srid,
    page: currentPage,
  });

  const {
    records: actionLogs,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(
    res.data.actionLogsForEmployment,
    res.data.personalTableColumn,
  );

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <div className="space-y-4">
      <VStack className="gap-0">
        <h1 className="text-xl font-bold">Action Log</h1>
      </VStack>

      <DataGrid.Root>
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            numRows={actionLogs.length}
            data={actionLogs}
            tableName="EMPLOYMENT_ACTION_LOGS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </div>
  );
}
