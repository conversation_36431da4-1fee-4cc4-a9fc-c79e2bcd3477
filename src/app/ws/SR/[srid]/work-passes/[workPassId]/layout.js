import WorkPassSegmentedControl from "@/app/ws/SR/[srid]/work-passes/[workPassId]/WorkPassSegmentedControl";
import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import HStack from "@/ui/HStack";
import { RotateCwIcon } from "lucide-react";

const QUERY = `
  query workPass($id: ID!) {
    workPass(id: $id) {
      id
      fmId
      gid
      passType
      passTypeShortForm
      applicationType
      applicationNumber
      finNumber
      periodGranted
      passNumber
      isRenewal
    }
  }
`;

export default async function Layout({ params, children }) {
  const { workPassId } = await params;
  const res = await apiQuery(QUERY, { id: workPassId });
  const workPass = res.data.workPass;

  if (workPass === null) notFound();

  return (
    <div className="space-y-2 py-4">
      <div className="space-y-4 px-4">
        <HStack className="justify-between text-xl font-bold">
          <HStack className>
            {workPass.fmId}: {workPass.passType} - {workPass.applicationType}
            {workPass.isRenewal && (
              <RotateCwIcon className="stroke-rose-400" strokeWidth={3} />
            )}
          </HStack>

          <h1 className="text-muted">{workPass.passTypeShortForm}</h1>
        </HStack>

        <div className="text-center">
          <WorkPassSegmentedControl />
        </div>
      </div>

      {children}
    </div>
  );
}
