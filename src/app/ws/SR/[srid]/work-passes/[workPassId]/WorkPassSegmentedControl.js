"use client";

import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";
import HStack from "@/ui/HStack";
import { useParams, usePathname } from "next/navigation";
import DependantPassIcon from "@/icons/emp/DependantPassIcon";
import WorkPassIcon from "@/icons/emp/WorkPassIcon";
import ClipIcon from "@/icons/emp/ClipIcon";

export default function WorkPassSegmentedControl() {
  const { srid, workPassId } = useParams();
  const pathname = usePathname();
  const baseUrl = `/ws/SR/${srid}/work-passes/${workPassId}`;

  let testPathname = pathname;

  if (pathname.startsWith(`${baseUrl}/family-passes`)) {
    testPathname = `${baseUrl}/family-passes`;
  }

  return (
    <SegmentedControlRoot
      value={testPathname}
      style={{ "--segmented-control-border-radius": "9999px" }}
    >
      <SegmentedControlItem href={baseUrl}>
        <HStack>
          <WorkPassIcon className="text-orange-400" />
          Work Pass
        </HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/family-passes`}>
        <HStack>
          <DependantPassIcon className="text-rose-600" />
          Family Passes
        </HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/files`}>
        <HStack>
          <ClipIcon className="text-indigo-800" />
          Files
        </HStack>
      </SegmentedControlItem>
    </SegmentedControlRoot>
  );
}
