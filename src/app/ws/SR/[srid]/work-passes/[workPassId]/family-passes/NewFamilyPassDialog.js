"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooterWithCTA,
} from "@/ui/Dialog";
import { useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { useParams, useRouter } from "next/navigation";
import Button from "@/ui/Button";
import { CirclePlus } from "lucide-react";
import {
  Form,
  FormBody,
  FormRow,
  FormSection,
  showFormErrors,
} from "@/ui/Form";
import Select from "@/ui/Select";
import WorkPassAppTypeSelect from "@/components/select/WorkPassAppTypeSelect";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import Input from "@/ui/Input";

const MUTATION = `
  mutation createFamilyPass($workPassId: ID!, $input: FamilyPassInput!) {
    execute:createFamilyPass(workPassId: $workPassId, input: $input) {
      userErro<PERSON> {
        path
        message
      }

      success

      record {
        ... on FamilyPass {
          id
        }
      }
    }
  }
`;

export default function NewFamilyPassDialog() {
  const { workPassId } = useParams();
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const form = useForm();
  const router = useRouter();
  const {
    control,
    register,
    reset,
    formState: { errors },
    setError,
  } = form;

  const watchPassType = useWatch({ control, name: "passType" });
  const isLtvp = watchPassType === "Long-Term Visit Pass";

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      router.refresh();
      toast.success("Family pass added successfully");
      reset();
      setOpen(false);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    execute({ workPassId, input });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <CirclePlus strokeWidth={1.5} />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>Add family pass</DialogHeader>

        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody className="mx-auto max-w-[700px] space-y-4 py-4">
            <FormSection>
              <FormRow>
                <Select
                  label="Pass type"
                  {...register("passType", {
                    required: "Pass type is required!",
                  })}
                  error={errors.passType}
                >
                  <option value="Dependant's Pass">Dependant&#39;s Pass</option>
                  <option value="Long-Term Visit Pass">
                    Long-Term Visit Pass (LTVP)
                  </option>
                </Select>

                <Select
                  label="Relationship"
                  {...register("relationship", {
                    required: "Relationship is required!",
                  })}
                  error={errors.relationship}
                >
                  <option value=""></option>
                  <option value="Wife">Wife</option>
                  <option value="Husband">Husband</option>
                  <option value="Child">Child</option>
                  {isLtvp && (
                    <>
                      <option value="Father">Father</option>
                      <option value="Mother">Mother</option>
                    </>
                  )}
                </Select>
              </FormRow>

              <FormRow>
                <Input
                  label="Name"
                  {...register("name", { required: "Name is required!" })}
                  error={errors.name}
                />
                <WorkPassAppTypeSelect
                  {...register("applicationType", {
                    required: "Application type is required!",
                  })}
                />
              </FormRow>
            </FormSection>
          </FormBody>

          <DialogFooterWithCTA label="Add" pending={pending} />
        </Form>
      </DialogContent>
    </Dialog>
  );
}
