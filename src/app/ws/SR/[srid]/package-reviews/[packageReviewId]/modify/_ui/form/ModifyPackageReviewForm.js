"use client";

import { useForm } from "react-hook-form";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { isEmpty } from "lodash";
import { startTransition, useRef, useState } from "react";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Form, formatDateForDatePicker, showFormErrors } from "@/ui/Form";
import { ProgressBarLink, useProgressBar } from "@/ui/ProgressBar";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import CurrentContractDetails from "@/app/ws/SR/[srid]/package-reviews/_ui/form/CurrentContractDetails";
import BaseErrorAlertCard from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BaseErrorAlertCard";
import PackageReviewFormPreviewDialog from "@/app/ws/SR/[srid]/package-reviews/_ui/form/PackageReviewFormPreviewDialog";
import PackageReviewForm from "@/app/ws/SR/[srid]/package-reviews/_ui/form/PackageReviewForm";

const MUTATION = `
  mutation modifyPackageReview($employmentId: ID!, $id: ID!, $input: PackageReviewInput!) {
    execute: modifyPackageReview(employmentId: $employmentId, id: $id, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on PackageReview {
          id
          status
          staffResourceUrl
        }
      }
    }
  }
`;

function getFormDefaultValues(packageReview) {
  const { employment } = packageReview;

  return {
    changeBasicSalary: packageReview.changeBasicSalary ? "Yes" : "No",
    changeBonuses: packageReview.changeBonuses ? "Yes" : "No",
    changeDesignation: packageReview.changeDesignation ? "Yes" : "No",
    changeEnts: packageReview.changeEnts ? "Yes" : "No",
    newBasicSalary: packageReview.newBasicSalary,
    newBasicSalaryPayKind: packageReview.newBasicSalaryPayKind,
    newBasicSalaryEffectiveFromDate: formatDateForDatePicker(
      packageReview.newBasicSalaryEffectiveFromDate,
    ),
    bonuses: !isEmpty(packageReview.bonuses)
      ? packageReview.bonuses.map((bonus) => ({
          chargeDescription: bonus.chargeDescription,
          charges: bonus.charges,
          effectiveFromDate: formatDateForDatePicker(bonus.effectiveFromDate),
        }))
      : [
          {
            chargeDescription: "Annual Wage Supplement (AWS)",
            effectiveFromDate: null,
            charges: null,
          },
        ],
    newDesignation: packageReview.newDesignation,
    newDesignationEffectiveFromDate: formatDateForDatePicker(
      packageReview.newDesignationEffectiveFromDate,
    ),
    ents: !isEmpty(packageReview.ents)
      ? packageReview.ents.map((ent) => ({
          ...ent,
          validFromDate: formatDateForDatePicker(ent.validFromDate),
          validTillDate: formatDateForDatePicker(ent.validTillDate),
        }))
      : [
          {
            subject: "",
            action: "",
            validFromDate: null,
            validTillDate: employment.activeAgreement
              ? formatDateForDatePicker(employment.activeAgreement.endDate)
              : null,
            content: "",
          },
        ],
    confirmation: false,
  };
}

export default function ModifyPackageReviewForm({ packageReview }) {
  const { employment } = packageReview;
  const submitButtonRef = useRef(null);
  const router = useRouter();
  const progress = useProgressBar();
  const { srid, packageReviewId } = useParams();
  const [preview, setPreview] = useState(false);
  const [baseError, setBaseError] = useState(null);
  const form = useForm({ defaultValues: getFormDefaultValues(packageReview) });
  const {
    setValue,
    setError,
    formState: { errors },
  } = form;
  const hasFormErrors = !isEmpty(errors);

  const [execute, submitting] = useAction(boundAction(MUTATION), {
    onSuccess: ({ record }) => {
      setBaseError(null);
      toast.success("Package review modified!");
      router.push(record.staffResourceUrl);
    },
    onError: ({ userErrors }) => {
      toast.error("Something went wrong!");
      showFormErrors({ userErrors, setError, setBaseError });
      setValue("confirmation", false, {
        shouldValidate: false,
        shouldTouch: true,
        shouldDirty: true,
      });
      setPreview(false);
      // window.scrollTo({ top: -250, behavior: "smooth" });
    },
  });

  const onSubmit = (input) => {
    const {
      changeBasicSalary: changeBasicSalaryPrimitive,
      changeBonuses: changeBonusesPrimitive,
      changeDesignation: changeDesignationPrimitive,
      changeEnts: changeEntsPrimitive,
    } = input;

    const changeBasicSalary = changeBasicSalaryPrimitive === "Yes";
    const changeBonuses = changeBonusesPrimitive === "Yes";
    const changeDesignation = changeDesignationPrimitive === "Yes";
    const changeEnts = changeEntsPrimitive === "Yes";
    const hasChanges =
      changeBasicSalary || changeBonuses || changeDesignation || changeEnts;

    const newInput = {
      ...input,
      changeBasicSalary,
      changeBonuses,
      changeDesignation,
      changeEnts,
    };

    if (hasChanges) {
      if (!preview) {
        setPreview(true);
        return;
      }

      if (!input.confirmation) {
        toast.error("Please check the confirmation!");
        setError("confirmation", { message: " " });
        return;
      }

      progress.start();

      startTransition(() => {
        execute({ employmentId: srid, id: packageReviewId, input: newInput });
        progress.done();
      });
    } else {
      toast.error("Please submit at least one change!");
    }
  };

  return (
    <div className="mx-auto max-w-[800px] space-y-4">
      <CurrentContractDetails employment={employment} />

      <Form className="space-y-4" form={form} onSubmit={onSubmit}>
        <BaseErrorAlertCard baseError={baseError} />

        <PackageReviewForm employment={employment} />

        <HStack className="justify-end gap-4">
          <Button variant="ghost" className="mr-auto" disabled={submitting}>
            <ProgressBarLink href={packageReview.staffResourceUrl}>
              Cancel
            </ProgressBarLink>
          </Button>

          <Button
            ref={submitButtonRef}
            type="submit"
            variant={!hasFormErrors ? "success" : "danger"}
            disabled={submitting}
          >
            {!hasFormErrors
              ? preview
                ? "Previewing..."
                : "Preview"
              : "Please review form"}
          </Button>

          <PackageReviewFormPreviewDialog
            submitButtonRef={submitButtonRef}
            employment={employment}
            preview={preview}
            setPreview={setPreview}
            submitting={submitting}
            isModify
          />
        </HStack>
      </Form>
    </div>
  );
}
