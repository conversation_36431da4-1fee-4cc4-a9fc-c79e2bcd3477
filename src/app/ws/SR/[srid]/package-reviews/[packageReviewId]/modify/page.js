import apiQuery from "@/lib/apiQuery";
import isEligibleToPackageReview from "@/lib/package-review/isEligibleToPackageReview";
import Card from "@/ui/Card";
import { HandIcon } from "lucide-react";
import { notFound } from "next/navigation";
import ModifyPackageReviewForm from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/modify/_ui/form/ModifyPackageReviewForm";

const QUERY = `
  query packageReview($id: ID!, $employmentId: ID!) {
    packageReview(id: $id, employmentId: $employmentId) {
      id
      fmId
      employmentId
      status

      changeBasicSalary
      newBasicSalary
      newBasicSalaryPayKind
      newBasicSalaryEffectiveFromDate
      prevBasicSalary
      prevBasicSalaryPayKind

      changeBonuses
      bonuses {
        chargeDescription
        charges
        payKind
        effectiveFromDate
      }

      changeDesignation
      newDesignation
      newDesignationEffectiveFromDate
      prevDesignation

      changeEnts
      ents {
        action
        subject
        content
        validFromDate
        validTillDate
      }

      canModify

      staffResourceUrl

      employment {
        id
        fmId
        contractorName
        designation
        hiredThrough
        status

        commencementDate
        expiredDate
        extensionDate
        lastDayDate
        displayPeriod

        currentBasicSalary {
          chargeType
          chargeDescription
          status
          amountAfterRate
          payKind
          effectiveFromDate
          effectiveEndDate
        }

        bonusesDue {
          chargeType
          chargeDescription
          status
          amountAfterRate
          payKind
          effectiveFromDate
          effectiveEndDate
        }

        activeAgreement {
          fmId
          startDate
          endDate
        }

        contractor {
          staffResourceUrl
        }

        company {
          fmId
          name

          staffResourceUrl
        }

        project {
          fmId
          name
          staffResourceUrl
        }

        ent {
          fmId
          description
          staffResourceUrl
        }
      }
    }
  }
`;

export async function generateMetadata({ params }) {
  const { srid, packageReviewId } = await params;

  const res = await apiQuery(QUERY, {
    id: packageReviewId,
    employmentId: srid,
  });
  const employment = res.data.packageReview.employment;

  return {
    title: `${employment.fmId}: ${employment?.contractorName} - Modify Package Review`,
  };
}

export default async function Page({ params }) {
  const { srid, packageReviewId } = await params;
  const res = await apiQuery(QUERY, {
    id: packageReviewId,
    employmentId: srid,
  });
  const packageReview = res.data.packageReview;
  const employment = packageReview.employment;
  if (employment === null) notFound();

  const { eligible, error } = isEligibleToPackageReview(employment);

  if (!eligible)
    return (
      <div className="flex justify-center overflow-auto p-4 sm:min-w-[500px]">
        <Card className="text-muted flex min-w-[400px] flex-col items-center gap-2">
          <HandIcon size={40} strokeWidth={1.25} className="stroke-gray-400" />
          <p>Unable to modify package review!</p>
          <p>{error}</p>
        </Card>
      </div>
    );

  return <ModifyPackageReviewForm packageReview={packageReview} />;
}
