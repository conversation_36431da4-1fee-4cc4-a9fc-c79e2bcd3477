"use client";

import { toFormalDateTime } from "@/formatters/date";
import cn from "@/lib/cn";
import AlertCard from "@/ui/AlertCard";
import Button from "@/ui/Button";
import Card from "@/ui/Card";
import { DataGrid } from "@/ui/DataGrid";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import HStack from "@/ui/HStack";
import { useProgressBarLink } from "@/ui/ProgressBar";
import RawHtml from "@/ui/RawHtml";
import Spinner from "@/ui/Spinner";
import StatusText from "@/ui/StatusText";
import VStack from "@/ui/VStack";
import { camelCase, isEmpty, mapKeys } from "lodash";
import { CheckCircleIcon, ListIcon, TableIcon } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { useQuery } from "urql";

const QUERY = `
  query changeHistories($recordId: ID!) {
    changeHistories(recordId: $recordId, recordType: "Emp::PackageReview", diff: true, includeInitial: true) {
      version
      createdAt
      changes
      userName
    }
  }
`;

export default function TrackProgressCard({
  packageReview,
  defaultView = "list",
}) {
  const handleProgressBarLink = useProgressBarLink();
  const [view, setView] = useState(defaultView);
  const [res] = useQuery({
    query: QUERY,
    variables: { recordId: packageReview.id },
  });

  const { data, fetching } = res;
  const changeHistories = data?.changeHistories || [];

  const handleVersionClick = (v, e) => {
    handleProgressBarLink(`${packageReview.staffResourceUrl}?v=${v}`)(e);
  };

  // We only track status changes for now
  const trackableHistories = changeHistories.reduce((acc, h) => {
    if (Object.keys(h.changes).includes("status")) {
      acc.push({
        ...h,
        changes: mapKeys(h.changes, (_, key) => camelCase(key)),
      });
    }

    return acc;
  }, []);

  return (
    <Card className="w-full space-y-4 overflow-x-scroll">
      <HStack className="justify-between">
        <h2 className="font-bold">Track Progress</h2>

        <HStack>
          <Button
            className="px-[0.3125rem]"
            variant={view === "list" ? "secondary" : "ghost"}
            prefix={<ListIcon size={20} strokeWidth={1.5} />}
            onClick={() => setView("list")}
          />

          <Button
            className="px-[0.3125rem]"
            variant={view === "table" ? "secondary" : "ghost"}
            prefix={<TableIcon size={20} strokeWidth={1.5} />}
            onClick={() => setView("table")}
          />
        </HStack>
      </HStack>

      <p className="text-muted text-xs">Version: {trackableHistories.length}</p>

      {fetching ? (
        <Spinner />
      ) : view === "list" ? (
        <TimelineTree
          trackableHistories={trackableHistories}
          handleVersionClick={handleVersionClick}
        />
      ) : (
        <TimelineTable
          trackableHistories={trackableHistories}
          handleVersionClick={handleVersionClick}
        />
      )}
    </Card>
  );
}

function TimelineTree({ trackableHistories, handleVersionClick }) {
  if (isEmpty(trackableHistories))
    return (
      <AlertCard variant="info">
        <p className="text-muted text-sm">No changes yet.</p>
      </AlertCard>
    );

  return (
    <VStack className="max-h-[500px] flex-nowrap overflow-y-scroll">
      {trackableHistories.map((h) => (
        <TimelineTreeNode
          key={h.version}
          node={h}
          handleVersionClick={handleVersionClick}
        />
      ))}
    </VStack>
  );
}

function TimelineTreeNode({ node, handleVersionClick }) {
  const searchParams = useSearchParams();
  const v = searchParams.get("v");
  const { changes, createdAt } = node;
  const { status, declineReason } = changes;
  const completed = status !== "FUTURE";

  return (
    <div
      className={cn(
        "grid grid-cols-[auto_1fr] gap-2 rounded-md p-1 duration-300 hover:bg-gray-50",
        {
          "bg-amber-50 hover:bg-amber-50": v === node.version.toString(),
        },
      )}
    >
      <VStack>
        <span
          className={cn("text-muted", {
            "text-success": completed,
          })}
        >
          <CheckCircleIcon size={22} weight="fill" />
        </span>

        <div
          className={cn("mx-auto w-[2px] flex-1 rounded-full bg-gray-300", {
            "bg-success": completed,
          })}
        />
      </VStack>

      <VStack className="pb-6">
        <p className="text-muted mt-[1px] text-xs">
          {toFormalDateTime(createdAt)}
        </p>
        <p className="font-semibold">{status}</p>
        {status === "DECLINED" && (
          <RawHtml className="mb-2 rounded-lg bg-gray-100 p-2 text-sm whitespace-pre-wrap shadow-md">
            {declineReason}
          </RawHtml>
        )}

        <p
          className="text-underline-link w-fit cursor-pointer text-sm"
          onClick={(e) => handleVersionClick(node.version, e)}
        >
          View this version
        </p>
      </VStack>
    </div>
  );
}

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Date",
    columnWidth: 220,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
  {
    columnName: "Outcome",
    columnWidth: 180,
    propertyName: "changes",
    visible: true,
    cellComponent: OutcomeCell,
  },
  {
    columnName: "Who",
    columnWidth: 150,
    propertyName: "userName",
    visible: true,
  },
  // {
  //   columnName: "Changes",
  //   columnWidth: 500,
  //   propertyName: "changes",
  //   visible: true,
  //   cellComponent: ChangeCell,
  // },
];

function TimelineTable({ trackableHistories }) {
  if (isEmpty(trackableHistories))
    return (
      <AlertCard variant="info">
        <p className="text-muted text-sm">No changes yet.</p>
      </AlertCard>
    );

  return (
    <DataGrid.Content>
      <DataGridTable
        numRows={trackableHistories.length}
        data={trackableHistories}
        tableColumns={DEFAULT_TABLE_COLUMNS}
        defaultTableColumns={DEFAULT_TABLE_COLUMNS}
      />
    </DataGrid.Content>
  );
}

function OutcomeCell({ rowIndex, data, propertyName }) {
  if (!data[rowIndex]) return null;

  const status = data[rowIndex][propertyName].status;
  if (!status) return null;

  return <StatusText status={status} className="text-base" />;
}

function ChangeCell({ rowIndex, data, propertyName }) {
  if (!data[rowIndex]) return null;

  const changes = data[rowIndex][propertyName];
  if (isEmpty(changes)) return null;

  return (
    <VStack className="gap-1 py-1">
      {Object.keys(changes).map((k) => {
        const change = changes[k];

        return (
          <div
            key={k}
            className="w-fit max-w-full overflow-hidden rounded-full bg-amber-200 px-1.5 py-0.5 font-mono text-xs font-semibold text-ellipsis"
          >
            {k}={change}
          </div>
        );
      })}
    </VStack>
  );
}
