"use client";

import {
  BasicPropertyTable,
  Row,
} from "@/components/table-variants/BasicPropertyTable";
import { toFormalDate } from "@/formatters/date";
import { currency } from "@/formatters/numeric";
import cn from "@/lib/cn";
import { isEmpty } from "lodash";

export default function BasicSalaryPropertyTable({
  packageReview,
  packageReviewVersion,
}) {
  return (
    <>
      <h2 className="font-bold">1. Basic Salary</h2>

      <BasicPropertyTable className="[&>tbody>tr:last-child>td]:!border-b-0">
        <CurrentBasicSalaryRow
          packageReview={packageReview}
          packageReviewVersion={packageReviewVersion}
        />
        <NewBasicSalaryRow
          packageReview={packageReview}
          packageReviewVersion={packageReviewVersion}
        />
        <EffectiveFromDateRow
          packageReview={packageReview}
          packageReviewVersion={packageReviewVersion}
        />
      </BasicPropertyTable>
    </>
  );
}

const VALUE_DIFFERENT_CLASSNAME = "bg-amber-200";

function CurrentBasicSalaryRow({ packageReview, packageReviewVersion }) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    (packageReview.prevBasicSalary !== packageReviewVersion.prevBasicSalary ||
      packageReview.prevBasicSalaryPayKind !==
        packageReviewVersion.prevBasicSalaryPayKind);

  const value = `${currency(packageReview.prevBasicSalary)} ${packageReview.prevBasicSalaryPayKind}`;

  return (
    <Row
      property="Current"
      propertyClassName="text-sm text-muted font-normal py-1"
      value={value}
      valueClassName={cn("p-1 text-sm", {
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    />
  );
}

function NewBasicSalaryRow({ packageReview, packageReviewVersion }) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    (packageReview.changeBasicSalary !==
      packageReviewVersion.changeBasicSalary ||
      packageReview.newBasicSalary !== packageReviewVersion.newBasicSalary ||
      packageReview.newBasicSalaryPayKind !==
        packageReviewVersion.newBasicSalaryPayKind);

  const value = packageReview.changeBasicSalary
    ? `${currency(packageReview.newBasicSalary)} ${packageReview.newBasicSalaryPayKind}`
    : "No changes";

  return (
    <Row
      property="New"
      propertyClassName="text-sm text-muted font-normal py-1"
      value={value}
      valueClassName={cn("p-1 text-sm", {
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
        "text-muted": !packageReview.changeBasicSalary,
      })}
    />
  );
}

function EffectiveFromDateRow({ packageReview, packageReviewVersion }) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    (packageReview.changeBasicSalary !==
      packageReviewVersion.changeBasicSalary ||
      packageReview.newBasicSalaryEffectiveFromDate !==
        packageReviewVersion.newBasicSalaryEffectiveFromDate);

  const value = packageReview.changeBasicSalary
    ? toFormalDate(packageReview.newBasicSalaryEffectiveFromDate)
    : "No changes";

  return (
    <Row
      property="Effective from"
      propertyClassName="text-sm text-muted font-normal py-1"
      value={value}
      valueClassName={cn("p-1 text-sm", {
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
        "text-muted": !packageReview.changeBasicSalary,
      })}
    />
  );
}
