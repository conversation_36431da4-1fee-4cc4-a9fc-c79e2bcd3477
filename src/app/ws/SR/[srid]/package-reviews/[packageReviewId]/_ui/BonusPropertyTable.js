"use client";

import { toFormalDate } from "@/formatters/date";
import { currency } from "@/formatters/numeric";
import cn from "@/lib/cn";
import { isEmpty } from "lodash";
import { useSearchParams } from "next/navigation";

export default function BonusPropertyTable({
  packageReview,
  packageReviewVersion,
}) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    packageReview.changeBonuses !== packageReviewVersion.changeBonuses;

  return (
    <>
      <h2 className="font-bold">2. Bonuses</h2>

      {packageReview.changeBonuses ? (
        <table className="onboarding-table max-w-[600px] text-sm">
          <thead>
            <tr>
              <th className="text-left">Type</th>
              <th className="text-left">Due Date</th>
              <th className="!text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            {packageReview.bonuses.map((bonus, index) => {
              const bonusVersion = packageReviewVersion?.bonuses?.[index];

              return (
                <Row
                  key={`bonus-preview-${index}`}
                  index={index}
                  bonus={bonus}
                  bonusVersion={bonusVersion}
                />
              );
            })}
          </tbody>
        </table>
      ) : (
        <p className="text-muted text-sm">No changes</p>
      )}
    </>
  );
}

function Row({ index, bonus, bonusVersion }) {
  const searchParams = useSearchParams();
  const v = searchParams.get("v");
  const isVersioning = !isEmpty(v);

  return (
    <tr
      className={cn("duration-300", {
        "bg-amber-50": isVersioning,
      })}
    >
      <TypeCell index={index} bonus={bonus} bonusVersion={bonusVersion} />
      <DueDateCell bonus={bonus} bonusVersion={bonusVersion} />
      <AmountCell bonus={bonus} bonusVersion={bonusVersion} />
    </tr>
  );
}

const VALUE_DIFFERENT_CLASSNAME = "bg-amber-200";

function TypeCell({ index, bonus, bonusVersion }) {
  const isDifferent =
    !isEmpty(bonusVersion) &&
    bonus.chargeDescription !== bonusVersion?.chargeDescription;

  return (
    <td
      className={cn({
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      <span className="text-muted text-xs">{index + 1}.</span>{" "}
      <span>{bonus.chargeDescription}</span>
    </td>
  );
}

function DueDateCell({ bonus, bonusVersion }) {
  const isDifferent =
    !isEmpty(bonusVersion) &&
    bonus.effectiveFromDate !== bonusVersion?.effectiveFromDate;

  return (
    <td
      className={cn({
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      {toFormalDate(bonus.effectiveFromDate)}
    </td>
  );
}

function AmountCell({ bonus, bonusVersion }) {
  const isDifferent =
    !isEmpty(bonusVersion) && bonus.charges !== bonusVersion?.charges;

  return (
    <td
      className={cn("text-right", {
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      {currency(bonus.charges)}
    </td>
  );
}
