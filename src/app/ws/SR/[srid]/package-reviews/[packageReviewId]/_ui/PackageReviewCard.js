"use client";

import { toFormalDateTime } from "@/formatters/date";
import cn from "@/lib/cn";
import Card from "@/ui/Card";
import HStack from "@/ui/HStack";
import { ProgressBarLink } from "@/ui/ProgressBar";
import StatusText from "@/ui/StatusText";
import { isEmpty } from "lodash";
import { UndoIcon } from "lucide-react";
import { useSearchParams } from "next/navigation";
import PackageReviewActionMenu from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/PackageReviewActionMenu";
import BasicSalaryPropertyTable from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/BasicSalaryPropertyTable";
import BonusPropertyTable from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/BonusPropertyTable";
import DesignationPropertyTable from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/DesignationPropertyTable";
import BenefitPropertyTable from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/BenefitPropertyTable";

export default function PackageReviewCard({
  packageReview,
  packageReviewVersion,
}) {
  const searchParams = useSearchParams();
  const v = searchParams.get("v");
  const isVersioning = !isEmpty(v);

  return (
    <Card
      className={cn("h-fit space-y-4 duration-300", {
        "bg-amber-50": isVersioning,
      })}
    >
      <HStack>
        {isVersioning && (
          <HStack>
            <ProgressBarLink href={packageReview.staffResourceUrl}>
              <UndoIcon size={20} strokeWidth={2} />
            </ProgressBarLink>

            <HStack>
              <StatusText
                className="text-lg font-semibold"
                status={packageReview.status}
              />

              <span className="text-muted mt-[3px] text-sm">
                as of {toFormalDateTime(packageReview.statusUpdatedAt)}
              </span>
            </HStack>
          </HStack>
        )}

        <PackageReviewActionMenu packageReview={packageReview} />
      </HStack>

      <BasicSalaryPropertyTable
        packageReview={packageReview}
        packageReviewVersion={packageReviewVersion}
      />

      <BonusPropertyTable
        packageReview={packageReview}
        packageReviewVersion={packageReviewVersion}
      />

      <DesignationPropertyTable
        packageReview={packageReview}
        packageReviewVersion={packageReviewVersion}
      />

      <BenefitPropertyTable
        packageReview={packageReview}
        packageReviewVersion={packageReviewVersion}
      />
    </Card>
  );
}
