"use client";

import Card from "@/ui/Card";
import { List, PropertyList } from "@/ui/PropertyList";

export default function ContractDetailsAndOutcomeCard({ packageReview }) {
  return (
    <Card className="space-y-4">
      <PropertyList title="Contract Details">
        <List.Link
          label="Company"
          href={packageReview.employment.company.staffResourceUrl}
        >
          {packageReview.employment.company.fmId}:{" "}
          {packageReview.employment.company.name}
        </List.Link>
        <List.Link
          label="Project"
          href={packageReview.employment.project.staffResourceUrl}
        >
          {packageReview.employment.project.fmId}:{" "}
          {packageReview.employment.project.name}
        </List.Link>
        <List.Link label="Ent" href={packageReview.employment.ent}>
          {packageReview.employment.ent.fmId}:{" "}
          {packageReview.employment.ent.description}
        </List.Link>
        <List label="Current period">
          {packageReview.employment.displayPeriod}
        </List>
      </PropertyList>

      <PropertyList title="Package Outcome">
        <List.Status label="Status">{packageReview.status}</List.Status>
        <List.Timestamp label="Updated at">
          {packageReview.statusUpdatedAt}
        </List.Timestamp>
        <List label="Submitted by">{packageReview.creator?.name}</List>
        {packageReview.status === "WITHDRAWN" && (
          <List label="Withdrawn by">{packageReview.withdrawer?.name}</List>
        )}
      </PropertyList>
    </Card>
  );
}
