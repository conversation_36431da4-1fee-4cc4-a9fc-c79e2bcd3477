"use client";

import {
  BasicPropertyTable,
  Row,
} from "@/components/table-variants/BasicPropertyTable";
import { toFormalDate } from "@/formatters/date";
import cn from "@/lib/cn";
import { isEmpty } from "lodash";

export default function DesignationPropertyTable({
  packageReview,
  packageReviewVersion,
}) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    (packageReview.changeDesignation !==
      packageReviewVersion.changeDesignation ||
      packageReview.newDesignation !== packageReviewVersion.newDesignation);

  return (
    <>
      <h2 className="font-bold">3. Designation</h2>

      <BasicPropertyTable className="[&>tbody>tr:last-child>td]:!border-b-0">
        <CurrentDesignationRow
          packageReview={packageReview}
          packageReviewVersion={packageReviewVersion}
        />

        <NewDesignationRow
          packageReview={packageReview}
          packageReviewVersion={packageReviewVersion}
        />

        <EffectiveFromDateRow
          packageReview={packageReview}
          packageReviewVersion={packageReviewVersion}
        />
      </BasicPropertyTable>
    </>
  );
}

const VALUE_DIFFERENT_CLASSNAME = "bg-amber-200";

function CurrentDesignationRow({ packageReview, packageReviewVersion }) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    packageReview.prevDesignation !== packageReviewVersion.prevDesignation;

  return (
    <Row
      property="Current"
      propertyClassName="text-sm text-muted font-normal py-1"
      value={packageReview.prevDesignation}
      valueClassName={cn("p-1 text-sm", {
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    />
  );
}

function NewDesignationRow({ packageReview, packageReviewVersion }) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    (packageReview.changeDesignation !==
      packageReviewVersion.changeDesignation ||
      packageReview.newDesignation !== packageReviewVersion.newDesignation);

  const value = packageReview.changeDesignation
    ? packageReview.newDesignation
    : "No changes";

  return (
    <Row
      property="New"
      propertyClassName="text-sm text-muted font-normal py-1"
      value={value}
      valueClassName={cn("p-1 text-sm", {
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
        "text-muted": !packageReview.changeDesignation,
      })}
    />
  );
}

function EffectiveFromDateRow({ packageReview, packageReviewVersion }) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    (packageReview.changeDesignation !==
      packageReviewVersion.changeDesignation ||
      packageReview.newDesignationEffectiveFromDate !==
        packageReviewVersion.newDesignationEffectiveFromDate);

  const value = packageReview.changeDesignation
    ? toFormalDate(packageReview.newDesignationEffectiveFromDate)
    : "No changes";

  return (
    <Row
      property="Effective from"
      propertyClassName="text-sm text-muted font-normal py-1"
      value={value}
      valueClassName={cn("p-1 text-sm", {
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
        "text-muted": !packageReview.changeDesignation,
      })}
    />
  );
}
