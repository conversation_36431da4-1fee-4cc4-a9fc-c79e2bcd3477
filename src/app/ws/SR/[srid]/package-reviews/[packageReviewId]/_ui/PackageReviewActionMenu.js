"use client";

import AlertCard from "@/ui/AlertCard";
import Button from "@/ui/Button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import HStack from "@/ui/HStack";
import { isEmpty } from "lodash";
import {
  DownloadIcon,
  EllipsisIcon,
  PackageMinusIcon,
  PackagePlusIcon,
} from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import WithdrawPackageDialog from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/WithdrawPackageDialog";

export default function PackageReviewActionMenu({ packageReview }) {
  const searchParams = useSearchParams();
  const v = searchParams.get("v");
  const isVersioning = !isEmpty(v);
  const [openWithdrawPackageReviewDialog, setOpenWithdrawPackageReviewDialog] =
    useState(false);

  return (
    <HStack className="ml-auto min-h-[28px]">
      <WithdrawPackageDialog
        packageReview={packageReview}
        open={openWithdrawPackageReviewDialog}
        setOpen={setOpenWithdrawPackageReviewDialog}
      />

      <DropdownMenu>
        <DropdownMenuTrigger>
          <Button
            variant="plainIcon"
            className="gap-0"
            prefix={<EllipsisIcon size={24} />}
          />
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end">
          {!isVersioning ? (
            <>
              <DropdownMenuItem
                href={`${packageReview.staffResourceUrl}/modify`}
                disabled={!packageReview.canModify}
              >
                <PackagePlusIcon
                  size={20}
                  strokeWidth={1.5}
                  className="stroke-success"
                />
                Modify
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setOpenWithdrawPackageReviewDialog(true)}
                disabled={!packageReview.canWithdraw}
              >
                <PackageMinusIcon
                  size={20}
                  strokeWidth={1.5}
                  className="stroke-danger"
                />
                Withdraw
              </DropdownMenuItem>
            </>
          ) : (
            <DropdownMenuItem>
              <AlertCard variant="alert" containerClassName="rounded-md">
                Exit versioning mode!
              </AlertCard>
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuItem>
            <DownloadIcon size={20} strokeWidth={1.5} />
            Download PDF
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </HStack>
  );
}
