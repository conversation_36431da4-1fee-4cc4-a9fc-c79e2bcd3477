"use client";

import { toFormalDate } from "@/formatters/date";
import cn from "@/lib/cn";
import RawHtml from "@/ui/RawHtml";
import { isEmpty } from "lodash";
import { useSearchParams } from "next/navigation";

export default function BenefitPropertyTable({
  packageReview,
  packageReviewVersion,
}) {
  const isDifferent =
    !isEmpty(packageReviewVersion) &&
    packageReview.changeEnts !== packageReviewVersion.changeEnts;

  return (
    <>
      <h2 className="font-bold">4. Benefit Entitlements</h2>

      {packageReview.changeEnts ? (
        <table className="onboarding-table text-sm">
          <thead>
            <tr>
              <th>Action</th>
              <th>Subject</th>
              <th>Content</th>
              <th>Valid From</th>
              <th>Valid Till</th>
            </tr>
          </thead>
          <tbody>
            {packageReview.ents.map((ent, index) => {
              const entVersion = packageReviewVersion?.ents?.[index];

              return (
                <Row
                  key={`benefit-entitlement-${index}`}
                  index={index}
                  ent={ent}
                  entVersion={entVersion}
                />
              );
            })}
          </tbody>
        </table>
      ) : (
        <p className="text-muted text-sm">No changes</p>
      )}
    </>
  );
}

function Row({ index, ent, entVersion }) {
  const searchParams = useSearchParams();
  const v = searchParams.get("v");
  const isVersioning = !isEmpty(v);

  return (
    <tr
      className={cn("duration-300", {
        "bg-amber-50": isVersioning,
      })}
    >
      <ActionCell index={index} ent={ent} entVersion={entVersion} />
      <SubjectCell ent={ent} entVersion={entVersion} />
      <ContentCell ent={ent} entVersion={entVersion} />
      <ValidFromCell ent={ent} entVersion={entVersion} />
      <ValidTillCell ent={ent} entVersion={entVersion} />
    </tr>
  );
}

const VALUE_DIFFERENT_CLASSNAME = "bg-amber-200";

function ActionCell({ index, ent, entVersion }) {
  const isDifferent = !isEmpty(entVersion) && ent.action !== entVersion?.action;

  return (
    <td
      className={cn({
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      <span className="text-muted text-xs">{index + 1}.</span>{" "}
      <span>{ent.action}</span>
    </td>
  );
}

function SubjectCell({ ent, entVersion }) {
  const isDifferent =
    !isEmpty(entVersion) && ent.subject !== entVersion?.subject;

  return (
    <td
      className={cn({
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      {ent.subject}
    </td>
  );
}

function ContentCell({ ent, entVersion }) {
  const isDifferent =
    !isEmpty(entVersion) && ent.content !== entVersion?.content;

  return (
    <td
      className={cn({
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      <RawHtml className="whitespace-pre-line">{ent.content}</RawHtml>
    </td>
  );
}

function ValidFromCell({ ent, entVersion }) {
  const isDifferent =
    !isEmpty(entVersion) && ent.validFromDate !== entVersion?.validFromDate;

  return (
    <td
      className={cn({
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      {toFormalDate(ent.validFromDate)}
    </td>
  );
}

function ValidTillCell({ ent, entVersion }) {
  const isDifferent =
    !isEmpty(entVersion) && ent.validTillDate !== entVersion?.validTillDate;

  return (
    <td
      className={cn({
        [VALUE_DIFFERENT_CLASSNAME]: isDifferent,
      })}
    >
      {toFormalDate(ent.validTillDate)}
    </td>
  );
}
