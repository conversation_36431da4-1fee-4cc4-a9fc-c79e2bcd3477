"use client";

import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Dialog, DialogContent, DialogFooter, DialogHeader } from "@/ui/Dialog";
import AlertCard from "@/ui/AlertCard";
import Button from "@/ui/Button";

const MUTATION = `
  mutation withdrawPackageReview($employmentId: ID!, $id: ID!) {
    execute:withdrawPackageReview(employmentId: $employmentId, id: $id) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on PackageReview {
          id
          status
        }
      }
    }
  }
`;

export default function WithdrawPackageDialog({
  packageReview,
  open,
  setOpen,
}) {
  const router = useRouter();
  const { srid, packageReviewId } = useParams();

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Package review withdrawn successfully");
      router.refresh();
      setOpen(false);
    },
    onError: ({ userErrors }) => {
      console.warn(userErrors);
      toast.error("Something went wrong!");
    },
  });

  const withdraw = () => {
    execute({
      employmentId: srid,
      id: packageReviewId,
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          Withdraw {packageReview.employment.contractorName}&apos;s package
          review
        </DialogHeader>

        <div className="mx-auto max-w-[800px] space-y-4 p-4">
          <AlertCard variant="error" contentClassName="text-sm" center>
            <p>Upon withdrawal,</p>

            <ul className="ml-8 list-outside list-disc">
              <li>
                Notify {packageReview.employment.contractorName} that this
                package review has been withdrawn.
              </li>
              <li>
                All existing terms remain unchanged, no further action is
                required.
              </li>
              <li>
                If the package review still valid, you will need to submit a new
                package review.
              </li>
            </ul>
          </AlertCard>
        </div>
        <DialogFooter className="justify-center gap-4">
          <Button
            variant="secondary"
            disabled={pending}
            onClick={() => setOpen(false)}
            outline
          >
            No, keep it
          </Button>
          <Button variant="danger" loading={pending} onClick={withdraw}>
            Confirm withdrawal
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
