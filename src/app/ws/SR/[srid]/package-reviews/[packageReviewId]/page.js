import { notFound } from "next/navigation";
import { toNumber } from "lodash";
import apiQuery from "@/lib/apiQuery";
import { H1 } from "@/ui/Typography";
import VStack from "@/ui/VStack";
import PackageReviewCard from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/PackageReviewCard";
import ContractDetailsAndOutcomeCard from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/ContractDetailsAndOutcomeCard";
import TrackProgressCard from "@/app/ws/SR/[srid]/package-reviews/[packageReviewId]/_ui/TrackProgressCard";

const QUERY = `
  query packageReview($employmentId: ID!, $id: ID!) {
    packageReview(employmentId: $employmentId, id: $id) {
      id
      fmId
      employmentId
      status

      submittedAt
      modifiedAt
      acknowledgedAt
      withdrawnAt
      expirationScheduledAt
      expiredAt
      statusUpdatedAt

      changeBasicSalary
      newBasicSalary
      newBasicSalaryPayKind
      newBasicSalaryEffectiveFromDate
      prevBasicSalary
      prevBasicSalaryPayKind

      changeBonuses
      bonuses {
        chargeDescription
        charges
        payKind
        effectiveFromDate
      }

      changeDesignation
      newDesignation
      newDesignationEffectiveFromDate
      prevDesignation

      changeEnts
      ents {
        action
        subject
        content
        validFromDate
        validTillDate
      }

      canWithdraw
      canModify

      creator {
        isClient
        fmId
        name
        email
      }

      modifier {
        isClient
        fmId
        name
        email
      }

      withdrawer {
        isClient
        fmId
        name
        email
      }

      employment {
        fmId
        contractorName
        designation

        commencementDate
        expiredDate
        extensionDate
        lastDayDate
        displayPeriod

        company {
          fmId
          name
          staffResourceUrl
        }

        project {
          fmId
          name
          staffResourceUrl
        }

        ent {
          fmId
          description
          staffResourceUrl
        }
      }

      agreement {
        fmId
        startDate
        endDate

        contractorResourceUrl
        staffResourceUrl
      }

      staffResourceUrl
      contractorResourceUrl
    }
  }
`;

const VERSION_QUERY = `
  query packageReviewStatusTransition($id: ID!, $version: Int!) {
    packageReviewStatusTransition(id: $id, version: $version) {
      packageReview:currentVersion {
        logVersion
        id
        fmId
        employmentId
        status

        submittedAt
        modifiedAt
        acknowledgedAt
        withdrawnAt
        expirationScheduledAt
        expiredAt
        statusUpdatedAt

        changeBasicSalary
        newBasicSalary
        newBasicSalaryPayKind
        newBasicSalaryEffectiveFromDate
        prevBasicSalary
        prevBasicSalaryPayKind

        changeBonuses
        bonuses {
          chargeDescription
          charges
          payKind
          effectiveFromDate
        }

        changeDesignation
        newDesignation
        newDesignationEffectiveFromDate
        prevDesignation

        changeEnts
        ents {
          action
          subject
          content
          validFromDate
          validTillDate
        }

        canWithdraw
        canModify

        creator {
          isClient
          fmId
          name
          email
        }

        modifier {
          isClient
          fmId
          name
          email
        }

        withdrawer {
          isClient
          fmId
          name
          email
        }

        employment {
          fmId
          contractorName
          designation

          commencementDate
          expiredDate
          extensionDate
          lastDayDate
          displayPeriod

          company {
            fmId
            name
            staffResourceUrl
          }

          project {
            fmId
            name
            staffResourceUrl
          }

          ent {
            fmId
            description
            staffResourceUrl
          }
        }

        agreement {
          fmId
          startDate
          endDate

          contractorResourceUrl
          staffResourceUrl
        }

        staffResourceUrl
        contractorResourceUrl
      }

      packageReviewVersion:lastVersion {
        logVersion
        id
        fmId
        employmentId
        status

        submittedAt
        modifiedAt
        acknowledgedAt
        withdrawnAt
        expirationScheduledAt
        expiredAt
        statusUpdatedAt

        changeBasicSalary
        newBasicSalary
        newBasicSalaryPayKind
        newBasicSalaryEffectiveFromDate
        prevBasicSalary
        prevBasicSalaryPayKind

        changeBonuses
        bonuses {
          chargeDescription
          charges
          payKind
          effectiveFromDate
        }

        changeDesignation
        newDesignation
        newDesignationEffectiveFromDate
        prevDesignation

        changeEnts
        ents {
          action
          subject
          content
          validFromDate
          validTillDate
        }

        canWithdraw
        canModify

        creator {
          isClient
          fmId
          name
          email
        }

        modifier {
          isClient
          fmId
          name
          email
        }

        withdrawer {
          isClient
          fmId
          name
          email
        }

        employment {
          fmId
          contractorName
          designation

          commencementDate
          expiredDate
          extensionDate
          lastDayDate
          displayPeriod

          company {
            fmId
            name
            staffResourceUrl
          }

          project {
            fmId
            name
            staffResourceUrl
          }

          ent {
            fmId
            description
            staffResourceUrl
          }
        }

        agreement {
          fmId
          startDate
          endDate

          contractorResourceUrl
          staffResourceUrl
        }

        staffResourceUrl
        contractorResourceUrl
      }
    }
  }
`;

export async function generateMetadata({ params }) {
  const { srid, packageReviewId } = await params;
  const res = await apiQuery(QUERY, {
    employmentId: srid,
    id: packageReviewId,
  });
  const packageReview = res.data.packageReview;

  return {
    title: `${packageReview?.fmId}: ${packageReview?.employment?.contractorName} - Package Review`,
  };
}

export default async function Page({ params, searchParams }) {
  const { srid, packageReviewId } = await params;
  const { v } = await searchParams;
  const res = await apiQuery(
    v ? VERSION_QUERY : QUERY,
    v
      ? {
          id: packageReviewId,
          version: v ? toNumber(v) : null,
        }
      : { employmentId: srid, id: packageReviewId },
  );

  const result = v ? res.data.packageReviewStatusTransition : res.data;
  const packageReview = result?.packageReview;
  const packageReviewVersion = result.packageReviewVersion;
  if (packageReview === null) notFound();

  return (
    <div
      className="dark:bg-background h-screen space-y-4 overflow-auto bg-[#F3F6F7] p-4"
      style={{ height: "calc(100% - 114px)" }}
    >
      <H1 className="font-bold">Package Review</H1>

      <div className="mx-auto max-w-[1000px] space-y-4">
        <div className="grid grid-cols-1 gap-2 sm:grid-cols-[65%_35%]">
          <VStack>
            <PackageReviewCard
              packageReview={packageReview}
              packageReviewVersion={packageReviewVersion}
            />
          </VStack>

          <VStack>
            <ContractDetailsAndOutcomeCard packageReview={packageReview} />
            <TrackProgressCard packageReview={packageReview} />
          </VStack>
        </div>
      </div>
    </div>
  );
}
