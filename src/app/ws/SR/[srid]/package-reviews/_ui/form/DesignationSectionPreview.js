"use client";

import { useFormContext } from "react-hook-form";
import { FormRow, FormSection } from "@/ui/Form";
import VStack from "@/ui/VStack";
import PreviewValue from "@/components/FormPreview/PreviewValue";

export default function DesignationSectionPreview({ setPreview }) {
  const {
    watch,
    formState: { errors },
  } = useFormContext();

  const changeDesignation = watch("changeDesignation");
  const newDesignation = watch("newDesignation");
  const newDesignationEffectiveFromDate = watch(
    "newDesignationEffectiveFromDate",
  );
  const hasChanges = changeDesignation === "Yes";

  return (
    <div data-form="section" className="space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2 dark:bg-gray-700 dark:text-neutral-100">
        <p className="text-lg font-bold">3.</p>
        <h2 className="text-lg font-bold">Designation</h2>
      </div>

      <FormSection className="ml-9">
        {hasChanges ? (
          <FormRow>
            <VStack className="gap-0">
              <p className="text-muted text-sm">New designation</p>
              <PreviewValue
                setPreview={setPreview}
                error={errors.newDesignation}
              >
                {newDesignation}
              </PreviewValue>
            </VStack>

            <VStack className="gap-0">
              <p className="text-muted text-sm">Effective from</p>
              <PreviewValue
                setPreview={setPreview}
                error={errors.newDesignationEffectiveFromDate}
              >
                {newDesignationEffectiveFromDate}
              </PreviewValue>
            </VStack>
          </FormRow>
        ) : (
          <FormRow>
            <PreviewValue setPreview={setPreview}>No changes</PreviewValue>
          </FormRow>
        )}
      </FormSection>
    </div>
  );
}
