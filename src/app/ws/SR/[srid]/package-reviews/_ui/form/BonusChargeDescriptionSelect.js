"use client";

import { useFormContext } from "react-hook-form";
import Select from "@/ui/Select";

export default function BonusChargeDescriptionSelect({ index }) {
  const {
    register,
    watch,
    formState: { errors },
  } = useFormContext();
  const changeBonuses = watch("changeBonuses");

  return (
    <Select
      label="Type"
      {...register(`bonuses.${index}.chargeDescription`, {
        required: changeBonuses && "Bonus type is required!",
      })}
      error={errors.bonuses?.[index]?.chargeDescription}
    >
      <option value="Annual Wage Supplement (AWS)">
        Annual Wage Supplement (AWS)
      </option>
      <option value="Completion">Completion</option>
      <option value="Contract Incentive">Contract Incentive</option>
      <option value="Ex-gratia Payment">Ex-gratia Payment</option>
      <option value="Joining">Joining</option>
      <option value="Performance">Performance</option>
      <option value="Retention">Retention</option>
      <option value="Sign-On">Sign-On</option>
      <option value="Variable">Variable</option>
      <option value="Project">Project</option>
      <option value="Special">Special</option>
      <option value="Discretionary">Discretionary</option>
    </Select>
  );
}
