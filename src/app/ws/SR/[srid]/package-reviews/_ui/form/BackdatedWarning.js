import { isBefore, startOfToday } from "date-fns";
import { parseDateFromDatePicker } from "@/ui/Form";
import cn from "@/lib/cn";
import { Info } from "@phosphor-icons/react";

export default function BackdatedWarning({
  date,
  className,
  label = "effective from",
}) {
  const today = startOfToday();
  const parsedDate = parseDateFromDatePicker(date);
  const isSalaryBackdated = isBefore(parsedDate, today);
  const finalClassName = cn(
    "mt-1 flex items-start gap-0.5 text-sm leading-tight text-gray-500",
    className,
  );

  if (!isSalaryBackdated) return null;

  return (
    <div className={finalClassName}>
      <Info className="mt-[0.5px]" size={16} weight="bold" />
      The {label} is in the past. Please ensure your intention.
    </div>
  );
}
