import { addDays, startOfToday } from "date-fns";
import { toFormalDate } from "@/formatters/date";
import AlertCard from "@/ui/AlertCard";

export default function SubmitPackageReviewStepsAlertCard({
  employment,
  isModify = false,
}) {
  const autoWithdrawalDate = toFormalDate(addDays(startOfToday(), 11));

  return (
    <AlertCard variant="pause" containerClassName="rounded-md mt-8">
      <p>
        By {isModify ? "modifying" : "submitting"} this package review, we will:
      </p>

      <ul className="ml-8 list-outside list-disc">
        <li>
          Notify{" "}
          <span className="font-semibold">{employment.contractorName}</span> to
          acknowledge this{isModify ? " modified" : ""} package.
        </li>
        <li>
          Upon acknowledgement, you will be notified and <PERSON><PERSON> will process
          the package in due time.
        </li>
      </ul>

      <p>
        <span className="font-semibold">Note:</span> This package will be
        automatically <span className="font-semibold">withdrawn</span> if there
        is no acknowledgement by{" "}
        <span className="font-semibold">{autoWithdrawalDate}</span>. You will
        then have to re-submit a new package review, if it is still applicable.
        Existing terms remain unchanged.
      </p>
    </AlertCard>
  );
}
