"use client";

import { useFormContext } from "react-hook-form";
import Checkbox from "@/ui/Checkbox";

export default function ConfirmationCheckbox() {
  const { register } = useFormContext();

  return (
    <div className="leading-tight">
      <Checkbox
        {...register("confirmation")}
        suffix={
          <span>
            I confirm that the information provided is accurate and authorized{" "}
            <span className="font-semibold">Jobline Resources Pte Ltd</span> to
            proceed with the package review.
          </span>
        }
      />
    </div>
  );
}
