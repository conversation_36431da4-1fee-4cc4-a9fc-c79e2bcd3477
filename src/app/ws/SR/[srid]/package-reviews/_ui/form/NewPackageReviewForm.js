"use client";

import { useForm } from "react-hook-form";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { isEmpty } from "lodash";
import { startTransition, useRef, useState } from "react";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Form, formatDateForDatePicker, showFormErrors } from "@/ui/Form";
import { ProgressBarLink, useProgressBar } from "@/ui/ProgressBar";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import CurrentContractDetails from "@/app/ws/SR/[srid]/package-reviews/_ui/form/CurrentContractDetails";
import BaseErrorAlertCard from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BaseErrorAlertCard";
import PackageReviewFormPreviewDialog from "@/app/ws/SR/[srid]/package-reviews/_ui/form/PackageReviewFormPreviewDialog";
import PackageReviewForm from "@/app/ws/SR/[srid]/package-reviews/_ui/form/PackageReviewForm";

const MUTATION = `
  mutation createPackageReview($employmentId: ID!, $input: PackageReviewInput!) {
    execute: createPackageReview(employmentId: $employmentId, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on PackageReview {
          id
          status
          staffResourceUrl
        }
      }
    }
  }
`;

function getFormDefaultValues(employment) {
  return {
    changeBasicSalary: null, // Yes | No
    changeBonuses: null, // Yes | No
    changeDesignation: null, // Yes | No
    changeEnts: null, // Yes | No
    newBasicSalary: null,
    newBasicSalaryPayKind: "Monthly",
    newBasicSalaryEffectiveFromDate: null,
    bonuses: !isEmpty(employment.bonusesDue)
      ? employment.bonusesDue.map(
          ({ chargeDescription, effectiveFromDate }) => ({
            chargeDescription,
            effectiveFromDate: formatDateForDatePicker(effectiveFromDate),
            charges: null,
          }),
        )
      : [
          {
            chargeDescription: "Annual Wage Supplement (AWS)",
            effectiveFromDate: null,
            charges: null,
          },
        ],
    newDesignation: null,
    newDesignationEffectiveFromDate: null,
    ents: [
      {
        subject: "",
        action: "",
        validFromDate: null,
        validTillDate: employment.activeAgreement
          ? formatDateForDatePicker(employment.activeAgreement.endDate)
          : null,
        content: "",
      },
    ],
    confirmation: false,
  };
}

export default function NewPackageReviewForm({ employment }) {
  const submitButtonRef = useRef(null);
  const router = useRouter();
  const progress = useProgressBar();
  const { srid } = useParams();
  const [preview, setPreview] = useState(false);
  const [baseError, setBaseError] = useState(null);
  const form = useForm({ defaultValues: getFormDefaultValues(employment) });
  const {
    setValue,
    setError,
    formState: { errors },
  } = form;
  const hasFormErrors = !isEmpty(errors);

  const [execute, submitting] = useAction(boundAction(MUTATION), {
    onSuccess: ({ record }) => {
      setBaseError(null);
      toast.success("Package review submitted!");
      router.push(record.staffResourceUrl);
    },
    onError: ({ userErrors }) => {
      toast.error("Something went wrong!");
      showFormErrors({ userErrors, setError, setBaseError });
      setValue("confirmation", false, {
        shouldValidate: false,
        shouldTouch: true,
        shouldDirty: true,
      });
      setPreview(false);
      // window.scrollTo({ top: -250, behavior: "smooth" });
    },
  });

  const onSubmit = (input) => {
    const {
      changeBasicSalary: changeBasicSalaryPrimitive,
      changeBonuses: changeBonusesPrimitive,
      changeDesignation: changeDesignationPrimitive,
      changeEnts: changeEntsPrimitive,
    } = input;

    const changeBasicSalary = changeBasicSalaryPrimitive === "Yes";
    const changeBonuses = changeBonusesPrimitive === "Yes";
    const changeDesignation = changeDesignationPrimitive === "Yes";
    const changeEnts = changeEntsPrimitive === "Yes";
    const hasChanges =
      changeBasicSalary || changeBonuses || changeDesignation || changeEnts;

    const newInput = {
      ...input,
      changeBasicSalary,
      changeBonuses,
      changeDesignation,
      changeEnts,
    };

    if (hasChanges) {
      if (!preview) {
        setPreview(true);
        return;
      }

      if (!input.confirmation) {
        toast.error("Please check the confirmation!");
        setError("confirmation", { message: " " });
        return;
      }

      progress.start();

      startTransition(() => {
        execute({ employmentId: srid, input: newInput });
        progress.done();
      });
    } else {
      toast.error("Please submit at least one change!");
    }
  };

  return (
    <div className="mx-auto max-w-[800px] space-y-4">
      <CurrentContractDetails employment={employment} />

      <Form className="space-y-4" form={form} onSubmit={onSubmit}>
        <BaseErrorAlertCard baseError={baseError} />

        <PackageReviewForm employment={employment} />

        <HStack className="justify-end gap-4">
          <Button variant="ghost" className="mr-auto" disabled={submitting}>
            <ProgressBarLink href={`/ws/SR/${srid}/package-reviews`}>
              Cancel
            </ProgressBarLink>
          </Button>

          <Button
            ref={submitButtonRef}
            type="submit"
            variant={!hasFormErrors ? "success" : "danger"}
            disabled={submitting}
          >
            {!hasFormErrors
              ? preview
                ? "Previewing..."
                : "Preview"
              : "Please review form"}
          </Button>

          <PackageReviewFormPreviewDialog
            submitButtonRef={submitButtonRef}
            employment={employment}
            preview={preview}
            setPreview={setPreview}
            submitting={submitting}
          />
        </HStack>
      </Form>
    </div>
  );
}
