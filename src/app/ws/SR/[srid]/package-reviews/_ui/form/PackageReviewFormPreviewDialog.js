"use client";

import { useFormContext } from "react-hook-form";
import { isEmpty } from "lodash";
import { Dialog, DialogContent, DialogFooter, DialogHeader } from "@/ui/Dialog";
import Button from "@/ui/Button";
import PackageReviewForm from "@/app/ws/SR/[srid]/package-reviews/_ui/form/PackageReviewForm";

export default function PackageReviewFormPreviewDialog({
  submitButtonRef,
  employment,
  preview,
  setPreview,
  submitting,
  isModify = false,
}) {
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();
  const hasConfirmed = watch("confirmation");
  const hasFormErrors = !isEmpty(errors);

  const handleClose = () => {
    setValue("confirmation", false, {
      shouldValidate: false,
      shouldTouch: true,
      shouldDirty: true,
    });
    setPreview(false);
  };

  return (
    <Dialog open={preview} onOpenChange={handleClose}>
      <DialogContent
        className="space-y-4"
        onEscapeKeyDown={(e) => {
          e.preventDefault();
        }}
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader removeClose>
          <span className="text-amber-700 dark:text-amber-500">
            Preview package details
          </span>
        </DialogHeader>

        <PackageReviewForm
          employment={employment}
          setPreview={setPreview}
          isModify={isModify}
          preview
        />

        <DialogFooter center>
          <Button
            variant="secondary"
            disabled={submitting}
            onClick={handleClose}
            outline
          >
            Back to edit
          </Button>
          <Button
            onClick={() => {
              hasConfirmed && !hasFormErrors && submitButtonRef.current.click();
            }}
            variant={!hasFormErrors ? "success" : "danger"}
            loading={submitting}
            disabled={!hasConfirmed}
          >
            {!hasFormErrors
              ? submitting
                ? "Submitting..."
                : "Submit package review"
              : "Please review form"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
