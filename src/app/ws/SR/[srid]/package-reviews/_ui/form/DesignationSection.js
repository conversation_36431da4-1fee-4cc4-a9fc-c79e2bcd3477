"use client";

import { useFormContext } from "react-hook-form";
import { FormRow, FormSection } from "@/ui/Form";
import VStack from "@/ui/VStack";
import Input from "@/ui/Input";
import YesNoRadioButtons from "@/components/YesNoRadioButton";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";
import BackdatedWarning from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BackdatedWarning";

export default function DesignationSection({ employment }) {
  const { designation } = employment;
  const {
    watch,
    register,
    formState: { errors },
  } = useFormContext();

  const changeDesignation = watch("changeDesignation");
  const newDesignationEffectiveFromDate = watch(
    "newDesignationEffectiveFromDate",
  );
  const hasChanges = changeDesignation === "Yes";

  return (
    <div data-form="section" className="space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2 dark:bg-gray-700 dark:text-neutral-100">
        <p className="text-lg font-bold">3.</p>
        <h2 className="text-lg font-bold">Designation</h2>
      </div>

      <FormSection className="ml-9">
        <FormRow>
          <p>
            The current designation is{" "}
            <span className="font-medium text-blue-500">{designation}</span>.
            Would you like to change it?
          </p>
        </FormRow>

        <FormRow>
          <YesNoRadioButtons
            name="changeDesignation"
            deps={["newDesignation", "newDesignationEffectiveFromDate"]}
          />
        </FormRow>

        {hasChanges && (
          <FormRow>
            <Input
              label="New designation"
              {...register("newDesignation", {
                required: hasChanges && "New designation is required!",
              })}
              error={errors.newDesignation}
            />

            <VStack className="gap-0">
              <FutureDatePickerInput
                label="Effective from"
                name="newDesignationEffectiveFromDate"
                shortcuts={false}
                rules={{
                  required: hasChanges && "Effective from is required!",
                }}
                allowStartOfPreviousMonth
              />

              <BackdatedWarning date={newDesignationEffectiveFromDate} />
            </VStack>
          </FormRow>
        )}
      </FormSection>
    </div>
  );
}
