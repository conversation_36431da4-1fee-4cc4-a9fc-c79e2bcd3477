"use client";

import { useState } from "react";
import { isEmpty } from "lodash";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/ui/Accordion";
import { ChevronDownIcon } from "lucide-react";
import { List, PropertyList } from "@/ui/PropertyList";
import Divider from "@/ui/Divider";
import RawHtml from "@/ui/RawHtml";

export default function CurrentContractDetails({
  employment,
  packageReview = null,
}) {
  const [open, setOpen] = useState(true);
  const period = employment.displayPeriod;
  let designation = "N/A";
  let basicSalary = "N/A";
  let basicSalaryPayKind = "N/A";
  if (!isEmpty(packageReview)) {
    designation = packageReview.prevDesignation;
    basicSalary = packageReview.prevBasicSalary;
    basicSalaryPayKind = packageReview.prevBasicSalaryPayKind;
  } else {
    designation = employment.designation;
    basicSalary = employment.currentBasicSalary?.amountAfterRate;
    basicSalaryPayKind = employment.currentBasicSalary?.payKind;
  }

  return (
    <Accordion
      type="multiple"
      defaultValue={["current-contract-details"]}
      onValueChange={(values) => {
        console.log(values);
        setOpen(() => values.includes("current-contract-details"));
      }}
    >
      <AccordionItem
        className="rounded-[0_2rem] border bg-gradient-to-br from-cyan-50 via-white to-cyan-50 p-4 dark:from-cyan-100 dark:via-white dark:to-cyan-100"
        value="current-contract-details"
        outline={false}
      >
        <AccordionTrigger arrow={false}>
          <h2 className="font-medium dark:text-neutral-800">
            Current Contract Details{!open && `: ${period}`}
          </h2>
          <ChevronDownIcon
            size={20}
            className="accordion-arrow shrink-0 transition-transform duration-300 dark:stroke-neutral-800"
          />
        </AccordionTrigger>
        <AccordionContent>
          <div className="mt-2 grid grid-cols-1 gap-x-10 sm:grid-cols-2">
            <PropertyList>
              <List label="SRID">{employment.fmId}</List>
              <List label="Period">{period}</List>
              <List label="Designation">{designation}</List>
              <List.Currency label="Basic salary" suffix={basicSalaryPayKind}>
                {basicSalary}
              </List.Currency>
            </PropertyList>

            <div className="sm:hidden">
              <Divider />
            </div>

            <PropertyList>
              <List.Link
                label="Contractor"
                href={employment.contractor.staffResourceUrl}
              >
                {employment.contractorName}
              </List.Link>
              <List.Link
                label="Company"
                href={employment.company.staffResourceUrl}
              >
                {employment.company.fmId}: {employment.company.name}
              </List.Link>
              <List.Link
                label="Project"
                href={employment.project.staffResourceUrl}
              >
                {employment.project.fmId}: {employment.project.name}
              </List.Link>
              <List.Link label="ENT" href={employment.ent.staffResourceUrl}>
                {employment.ent.fmId}:{" "}
                <RawHtml className="whitespace-pre-wrap">
                  {employment.ent.description}
                </RawHtml>
              </List.Link>
            </PropertyList>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
