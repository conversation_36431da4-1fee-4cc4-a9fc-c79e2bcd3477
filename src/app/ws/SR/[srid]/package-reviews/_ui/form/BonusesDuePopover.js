"use client";

import { useFormContext } from "react-hook-form";
import { format, parseISO } from "date-fns";
import { formatDateForDatePicker } from "@/ui/Form";
import { currency } from "@/formatters/numeric";
import cn from "@/lib/cn";
import pluralize from "pluralize";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import { CircleAlertIcon } from "lucide-react";
import Button from "@/ui/Button";

export default function BonusesDuePopover({ bonusesDue, add }) {
  const { watch } = useFormContext();
  const changeBonuses = watch("changeBonuses");
  const bonuses = watch("bonuses");
  const hasChanges = changeBonuses === "Yes";
  const hasAddFunction = typeof add === "function";

  const gridColClassName = cn("grid grid-cols-[20px_2fr_1fr_1fr] gap-2", {
    "grid-cols-[20px_2fr_1fr_1fr_0.75fr]": hasChanges && hasAddFunction,
  });

  return (
    <Popover>
      <PopoverTrigger>
        <Button
          size="sm"
          variant="warning"
          prefix={<CircleAlertIcon size={14} strokeWidth={2.5} />}
          className="py-0.25 pl-1 font-normal"
          pill
        >
          {pluralize("bonus", bonusesDue.length, true)} due
        </Button>
      </PopoverTrigger>

      <PopoverContent
        align="start"
        sideOffset={5}
        className="overflow-hidden border-none"
      >
        <div className="text-sm">
          <div className={cn("bg-orange-100", gridColClassName)}>
            <span className="p-2 font-semibold">#</span>
            <span className="p-2 font-semibold">Bonus Type</span>
            <span className="p-2 font-semibold">Due Date</span>
            <span className="p-2 text-right font-semibold">Amount</span>
            {hasChanges && hasAddFunction && (
              <span className="p-2 font-semibold"></span>
            )}
          </div>

          {bonusesDue.map(
            (
              { chargeDescription, effectiveFromDate, amountAfterRate },
              index,
            ) => {
              const hasAdded = bonuses.some(
                (bonus) => bonus.chargeDescription === chargeDescription,
              );

              return (
                <div
                  key={`bonus-due-${index}`}
                  className={cn("odd:bg-orange-50", gridColClassName)}
                >
                  <span className="p-2">{index + 1}.</span>
                  <span className="p-2">{chargeDescription}</span>
                  <span className="p-2">
                    {format(parseISO(effectiveFromDate), "d MMM yyyy")}
                  </span>
                  <span className="p-2 text-right tabular-nums">
                    {currency(amountAfterRate)}
                  </span>
                  {hasChanges &&
                    hasAddFunction &&
                    (hasAdded ? (
                      <span className="text-muted p-2">Added</span>
                    ) : (
                      <span
                        className="cursor-pointer p-2 text-blue-500"
                        onClick={(e) =>
                          add(
                            {
                              chargeDescription,
                              effectiveFromDate:
                                formatDateForDatePicker(effectiveFromDate),
                              charges: null,
                            },
                            { shouldFocus: false, shouldDirty: true },
                          )
                        }
                      >
                        Add
                      </span>
                    ))}
                </div>
              );
            },
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
