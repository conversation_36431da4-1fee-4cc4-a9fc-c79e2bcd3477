"use client";

import { FormBody, FormSection } from "@/ui/Form";
import BasicSalarySection from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BasicSalarySection";
import BonusSection from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BonusSection";
import DesignationSection from "@/app/ws/SR/[srid]/package-reviews/_ui/form/DesignationSection";
import BenefitSection from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BenefitSection";
import BasicSalarySectionPreview from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BasicSalarySectionPreview";
import BonusSectionPreview from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BonusSectionPreview";
import DesignationSectionPreview from "@/app/ws/SR/[srid]/package-reviews/_ui/form/DesignationSectionPreview";
import BenefitSectionPreview from "@/app/ws/SR/[srid]/package-reviews/_ui/form/BenefitSectionPreview";
import SubmitPackageReviewStepsAlertCard from "@/app/ws/SR/[srid]/package-reviews/_ui/form/SubmitPackageReviewStepsAlertCard";
import ConfirmationCheckbox from "@/app/ws/SR/[srid]/package-reviews/_ui/form/ConfirmationCheckbox";

export default function PackageReviewForm({
  employment,
  setPreview,
  isModify = false,
  preview = false,
}) {
  if (preview)
    return (
      <FormBody>
        <BasicSalarySectionPreview setPreview={setPreview} />
        <BonusSectionPreview setPreview={setPreview} />
        <DesignationSectionPreview setPreview={setPreview} />
        <BenefitSectionPreview setPreview={setPreview} />

        <FormSection>
          <SubmitPackageReviewStepsAlertCard
            employment={employment}
            isModify={isModify}
          />
          <ConfirmationCheckbox />
        </FormSection>
      </FormBody>
    );

  return (
    <FormBody>
      <BasicSalarySection employment={employment} />
      <BonusSection employment={employment} />
      <DesignationSection employment={employment} />
      <BenefitSection employment={employment} />
    </FormBody>
  );
}
