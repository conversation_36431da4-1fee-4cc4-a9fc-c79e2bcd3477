import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import LeaveTermSegmentedControl from "@/app/ws/SR/[srid]/payroll/leave-terms/[ccid]/LeaveTermSegmentedControl";
import Card from "@/ui/Card";

const QUERY = `
  query leaveTerm($id: ID!) {
    leaveTerm(id: $id) {
      id
      fmId
      startDate
      endDate
      status

      remark

      leaveEntitlements {
        id
        leaveType
        shortLeaveType
        entitled
        used
        balance
      }

      agreement {
        id
        fmId
      }

      creator {
        id
        name
      }
    }
  }
`;

export default async function Layout({ params, children }) {
  const { ccid } = await params;
  const res = await apiQuery(QUERY, { id: ccid });
  const leaveTerm = res.data.leaveTerm;

  if (leaveTerm === null) notFound();

  return (
    <div className="space-y-2 py-4">
      <div className="space-y-2 px-4">
        <h1 className="text-xl font-bold">
          {leaveTerm.fmId}: {leaveTerm.status}
        </h1>

        <Card className="mx-auto mt-4 max-w-[1000px] space-y-2 border-gray-200 bg-gradient-to-bl from-sky-50 via-white via-70% to-amber-50 p-2">
          Property List here...
          <p>
            Lorem ipsum dolor sit amet, consectetur adipisicing elit.
            Blanditiis, doloremque facere impedit inventore libero molestiae
            mollitia repellat sequi tenetur veniam! A accusamus earum eveniet
            incidunt itaque, iusto numquam qui reiciendis.
          </p>
        </Card>

        <div className="text-center">
          <LeaveTermSegmentedControl />
        </div>
      </div>

      {children}
    </div>
  );
}
