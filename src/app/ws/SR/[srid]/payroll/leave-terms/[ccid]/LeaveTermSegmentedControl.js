"use client";

import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";
import { useParams, usePathname } from "next/navigation";
import HStack from "@/ui/HStack";

export default function LeaveTermSegmentedControl() {
  const { srid, ccid } = useParams();
  const pathname = usePathname();
  const baseUrl = `/ws/SR/${srid}/payroll/leave-terms/${ccid}`;

  return (
    <SegmentedControlRoot
      value={pathname}
      style={{ "--segmented-control-border-radius": "9999px" }}
    >
      <SegmentedControlItem href={baseUrl}>
        <HStack>Leaves</HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/government-claims`}>
        <HStack>Gov Claims</HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/ent`}>
        <HStack>ENT</HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/claim-limits`}>
        <HStack>Claim Limits</HStack>
      </SegmentedControlItem>
    </SegmentedControlRoot>
  );
}
