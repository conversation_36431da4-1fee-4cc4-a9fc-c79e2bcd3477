"use client";

import LeaveTermTable from "@/app/ws/SR/[srid]/payroll/leave-terms/LeaveTermTable";
import VStack from "@/ui/VStack";
import Button from "@/ui/Button";
import LeaveEntitlementTable from "@/app/ws/SR/[srid]/payroll/leave-terms/LeaveEntitlementTable";
import ContainerAccordion from "@/app/ws/SR/[srid]/payroll/leave-terms/ContainerAccordion";
import {
  CirclePlusIcon,
  ArrowLeftRightIcon,
  QuoteIcon,
  ListTodoIcon,
  BotMessageSquareIcon,
} from "lucide-react";
import QuotedRemark from "@/components/QuotedRemark";

export default function CurrentLeaveTermAccordion({ leaveTerm }) {
  return (
    <ContainerAccordion title="Current term">
      <div className="space-y-4">
        <LeaveTermTable leaveTerms={[leaveTerm]} />

        <div className="grid grid-cols-1 space-y-4 md:grid-cols-[400px_1fr] md:space-y-0">
          <LeaveEntitlementTable
            leaveEntitlements={leaveTerm.leaveEntitlements}
          />

          <VStack className="md:border-l">
            <div className="mx-auto min-w-[220px]">
              <VStack>
                <Button
                  outline
                  square
                  variant="primary"
                  prefix={<CirclePlusIcon size={20} />}
                >
                  Credit Leaves
                </Button>
                <Button
                  outline
                  square
                  variant="primary"
                  prefix={<ArrowLeftRightIcon size={20} />}
                >
                  Swap Approvers
                </Button>
                <Button
                  outline
                  square
                  variant="primary"
                  prefix={<QuoteIcon size={20} />}
                >
                  Remark
                </Button>
                <Button
                  outline
                  square
                  variant="primary"
                  prefix={<ListTodoIcon size={20} />}
                >
                  Follow Up
                </Button>
                <Button
                  outline
                  square
                  variant="primary"
                  prefix={<BotMessageSquareIcon size={20} />}
                >
                  Ask AI
                </Button>
              </VStack>
            </div>
            <QuotedRemark title="Remark" bg={false}>
              {leaveTerm.remark}
            </QuotedRemark>
          </VStack>
        </div>
      </div>
    </ContainerAccordion>
  );
}
