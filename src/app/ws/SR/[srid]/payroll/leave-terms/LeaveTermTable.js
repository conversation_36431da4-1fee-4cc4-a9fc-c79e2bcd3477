import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import DateCell from "@/ui/DataGrid/cells/DateCell";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "CCID",
    columnWidth: 100,
    propertyName: "fmId",
    href: "/ws/SR/${params.srid}/payroll/leave-terms/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Start",
    columnWidth: 160,
    propertyName: "startDate",
    cellComponent: DateCell,
  },
  {
    columnName: "End",
    columnWidth: 160,
    propertyName: "endDate",
    cellComponent: DateCell,
  },
  {
    columnName: "Status",
    columnWidth: 120,
    propertyName: "status",
  },
  {
    columnName: "CAGID",
    columnWidth: 120,
    propertyName: "agreement.fmId",
  },
  {
    columnName: "Creator",
    columnWidth: 150,
    propertyName: "creator.name",
  },
];

export default function LeaveTermTable({ leaveTerms }) {
  return (
    <DataGrid.Root className="space-y-4">
      <DataGrid.Content>
        <DataGridTable
          numRows={leaveTerms.length}
          data={leaveTerms}
          tableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
}
