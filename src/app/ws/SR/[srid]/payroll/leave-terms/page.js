import apiQuery from "@/lib/apiQuery";
import CurrentLeaveTermAccordion from "@/app/ws/SR/[srid]/payroll/leave-terms/CurrentLeaveTermAccordion";
import CompletedLeaveTermsAccordion from "@/app/ws/SR/[srid]/payroll/leave-terms/CompletedLeaveTermsAccordion";
import CancelledLeaveTermsAccordion from "@/app/ws/SR/[srid]/payroll/leave-terms/CancelledLeaveTermsAccordion";

const QUERY = `
  query leaveTerms($employmentId: ID!) {
    leaveTerms(employmentId: $employmentId) {
      id
      fmId
      startDate
      endDate
      status

      remark

      leaveEntitlements {
        id
        leaveType
        shortLeaveType
        entitled
        used
        balance
      }

      agreement {
        id
        fmId
      }

      creator {
        id
        name
      }
    }
  }
`;

export default async function Page({ params }) {
  const { srid } = await params;

  const res = await apiQuery(QUERY, { employmentId: srid });
  const leaveTerms = res.data.leaveTerms;

  const currentLeaveTerms = leaveTerms.filter(
    (term) => term.status === "ACTIVE",
  );
  const completedLeaveTerms = leaveTerms.filter(
    (term) => term.status === "COMPLETED",
  );

  return (
    <div className="mx-auto max-w-[1500px]">
      <h1 className="p-4 text-xl font-bold">Leave terms</h1>

      {currentLeaveTerms.map((leaveTerm) => (
        <CurrentLeaveTermAccordion key={leaveTerm.id} leaveTerm={leaveTerm} />
      ))}

      <div className="border-t pt-4">
        <CompletedLeaveTermsAccordion leaveTerms={completedLeaveTerms} />
      </div>

      <div className="border-t pt-4">
        <CancelledLeaveTermsAccordion />
      </div>
    </div>
  );
}
