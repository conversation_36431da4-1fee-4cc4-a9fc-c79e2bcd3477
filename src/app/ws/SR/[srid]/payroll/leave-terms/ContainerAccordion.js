import { useState } from "react";
import { Accordion } from "@/ui/Accordion";
import { Item } from "@/components/accordion-variants/left-arrow-basic";
import SolidCaretRightIcon from "@/icons/SolidCaretRightIcon";

export default function ContainerAccordion({
  title,
  children,
  defaultAccordionValues = ["1"],
}) {
  const allAccordionValues = ["1"];
  const [accordionValues, setAccordionValues] = useState(
    defaultAccordionValues,
  );

  return (
    <div className="px-4">
      <Accordion
        type="multiple"
        value={accordionValues}
        onValueChange={(values) => setAccordionValues(values)}
      >
        <Item
          title={title}
          value="1"
          borderless
          triggerIcon={
            <div className="accordion-arrow transition-transform duration-150">
              <SolidCaretRightIcon />
            </div>
          }
        >
          {children}
        </Item>
      </Accordion>
    </div>
  );
}
