export default function LeaveEntitlementTable({ leaveEntitlements }) {
  return (
    <div className="text-sm">
      <div className="grid w-fit grid-cols-[200px_45px_50px_45px] px-2 py-1 text-right font-bold tabular-nums">
        <div></div>
        <div>Ent.</div>
        <div>Used</div>
        <div>Bal.</div>
      </div>
      {leaveEntitlements.map((e) => (
        <div
          key={e.id}
          className="grid w-fit grid-cols-[200px_45px_50px_45px] px-2 py-1 tabular-nums odd:rounded-md odd:bg-gray-100"
        >
          <div>
            {e.shortLeaveType} ({e.leaveType})
          </div>
          <div className="text-right font-mono">{e.entitled}</div>
          <div className="text-right font-mono">{e.used}</div>
          <div className="text-right font-mono">{e.balance}</div>
        </div>
      ))}
    </div>
  );
}
