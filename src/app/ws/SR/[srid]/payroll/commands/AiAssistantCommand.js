"use client";

import { Generate } from "@blueprintjs/icons";
import { useParams } from "next/navigation";
import { ProgressBarLink } from "@/ui/ProgressBar";

export default function AiAssistantCommand() {
  const { srid } = useParams();
  const baseUrl = `/ws/SR/${srid}/payroll/ask-ai`;

  return (
    <ProgressBarLink href={baseUrl} className="w-full">
      <div className="flex items-center justify-center gap-1 p-2 sm:gap-2">
        <Generate color="purple" size={20} className="hidden sm:block" />
        <span className="hidden sm:hidden md:block">Ask AI</span>
        <span className="sm:block md:hidden lg:hidden">AI</span>
      </div>
    </ProgressBarLink>
  );
}
