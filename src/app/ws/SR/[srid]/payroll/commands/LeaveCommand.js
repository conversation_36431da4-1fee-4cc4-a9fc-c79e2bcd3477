"use client";

import { Popover, PopoverTrigger, PopoverContent } from "@/ui/Popover";
import { CaretDown, Mountain, Th } from "@blueprintjs/icons";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
  CommandItem,
} from "@/ui/Command";
import { CirclePlusIcon, Search } from "lucide-react";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import { ProgressBarLink } from "@/ui/ProgressBar";
import { useParams } from "next/navigation";
import { useState } from "react";

export default function LeaveCommand() {
  const [open, setOpen] = useState(false);
  const { srid } = useParams();
  const baseUrl = `/ws/SR/${srid}/payroll/leaves`;
  const leaveTermsBaseUrl = `/ws/SR/${srid}/payroll/leave-terms`;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger>
        <HStack className="cursor-pointer items-center justify-center gap-1 p-2 sm:gap-2">
          <Mountain color="orange" size={20} className="hidden sm:block" />
          <span className="hidden sm:hidden md:block">Leaves</span>
          <span className="sm:block md:hidden lg:hidden">LA</span>
          <CaretDown />
        </HStack>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="w-[calc(100vw-32px)] lg:w-[calc(100vw-272px)]"
      >
        <Command className="w-full">
          <div className="border-b p-2">
            <CommandInput
              placeholder="Filter actions..."
              prefix={<Search className="ml-0" size={20} />}
              size="sm"
              borderless
            />
          </div>

          <CommandList className="p-2">
            <CommandEmpty>No actions found.</CommandEmpty>

            <CommandItem onClick={() => setOpen(false)}>
              <ProgressBarLink href={`${baseUrl}`} className="w-full">
                <VStack className="gap-0">
                  <HStack>
                    <Th size={20} />
                    <h1 className="font-semibold">Leaves database</h1>
                  </HStack>
                </VStack>
              </ProgressBarLink>
            </CommandItem>

            <CommandItem>
              <ProgressBarLink href={leaveTermsBaseUrl} className="w-full">
                <VStack className="gap-0">
                  <h1 className="font-semibold">Leave terms overview</h1>
                  <p className="text-muted text-sm">
                    Check leave term period allocation and their leave
                    entitlements.
                  </p>
                </VStack>
              </ProgressBarLink>
            </CommandItem>

            <CommandItem>
              <ProgressBarLink href={`${baseUrl}/new`} className="w-full">
                <HStack className="flex-nowrap justify-between">
                  <VStack className="gap-0">
                    <h1 className="font-semibold">Next leave term</h1>
                    <p className="text-muted text-sm">
                      Manually create a new term. Useful for taking advance
                      leaves (i.e. reservist, maternity).
                    </p>
                  </VStack>

                  <div className="px-4">
                    <CirclePlusIcon color="gray" size={20} />
                  </div>
                </HStack>
              </ProgressBarLink>
            </CommandItem>

            <CommandItem>
              <button
                type="button"
                className="w-full text-left"
                onClick={() => setOpen(false)}
              >
                <HStack className="flex-nowrap justify-between">
                  <VStack className="gap-0">
                    <h1 className="font-semibold">Credit leaves</h1>
                    <p className="text-muted text-sm">
                      Give additional leaves to contractor (e.g. Replacement,
                      birthday leaves, etc.).
                    </p>
                  </VStack>

                  <div className="px-4">
                    <CirclePlusIcon color="gray" size={20} />
                  </div>
                </HStack>
              </button>
            </CommandItem>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
