"use client";

import { useState } from "react";
import { useQuery } from "urql";
import { toFormalDate } from "@/formatters/date";
import pluralize from "pluralize";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  Di<PERSON><PERSON>rigger,
} from "@/ui/Dialog";
import HStack from "@/ui/HStack";
import Divider from "@/ui/Divider";
import Button from "@/ui/Button";
import RawHtml from "@/ui/RawHtml";

const QUERY = `
  query employment($id: ID!) {
    employment(id: $id) {
      fmId
      terminationEntitlement
    }
  }
`;

export default function ViewTerminationNoticeDialog({
  employment,
  terminationSnapshot,
}) {
  const { id } = employment;
  const [open, setOpen] = useState(false);

  const [result] = useQuery({
    query: QUERY,
    variables: { id },
    pause: !open,
  });

  const { data, fetching } = result;
  const { terminationEntitlement } = data?.employment || {};

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button
          variant="plainIcon"
          className="text-underline-link self-start p-0 text-xs"
          size="sm"
        >
          View termination notice
        </Button>
      </DialogTrigger>

      <DialogContent className="space-y-4">
        <DialogHeader>
          {employment.fmId}: {employment.contractorName} - Termination notice
        </DialogHeader>

        <HStack className="gap-8">
          <HStack>
            <p className="text-muted">Start date</p>
            <p className="font-semibold">
              {toFormalDate(employment.startDate)}
            </p>
          </HStack>
          <HStack>
            <p className="text-muted">Notice period</p>
            <p className="font-semibold">
              {pluralize("day", employment.effectiveNoticeDays, true)}
            </p>
          </HStack>
        </HStack>

        <Divider />

        <RawHtml className="text-sm whitespace-pre-line">
          {terminationSnapshot || terminationEntitlement}
        </RawHtml>

        <DialogFooter center>
          <DialogDismissButton loading={fetching} />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
