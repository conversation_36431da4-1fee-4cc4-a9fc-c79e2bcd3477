import apiQuery from "@/lib/apiQuery";
import NewClosureForm from "@/app/ws/SR/[srid]/closure/new/_ui/NewClosureForm";

const QUERY = `
  query employment($id: ID!) {
    employment(id: $id) {
      id
      fmId
      status
      startDate
      effectiveNoticeDays
      expiredDate
      lastDayDate
      contractorName

      currentBasicSalary {
        amountAfterRate
        payKind
      }

      currentLeaveTerm {
        fmId

        annualEntitlement {
          balance
          unpaid
        }
      }

      bonusesForRenewal {
        chargeDescription
        amountAfterRate
      }

      owner {
        fmId
        name
      }
    }
  }
`;

export async function generateMetadata({ params }) {
  const { srid } = await params;

  const res = await apiQuery(QUERY, { id: srid });
  const employment = res.data.employment;

  return {
    title: `${employment.fmId}: ${employment?.contractorName} - New Closure`,
  };
}

export default async function Page({ params }) {
  const { srid } = await params;

  const res = await apiQuery(QUERY, { id: srid });
  const employment = res.data.employment;

  return (
    <div className="space-y-4 p-4 pt-0">
      <h1 className="text-xl font-bold">New closure</h1>

      <div className="mx-auto max-w-[800px] space-y-4">
        <NewClosureForm employment={employment} />
      </div>
    </div>
  );
}
