"use client";

import { useFormContext, useWatch } from "react-hook-form";
import { FormRow } from "@/ui/Form";
import { default as AlInstructionSelectPrimitive } from "@/components/select/AlInstructionSelect";

export default function AlInstructionSelect() {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const alBalanceDays = useWatch({ name: "alBalanceDays" });
  if (alBalanceDays === 0) return null;

  return (
    <FormRow colsCount={2}>
      <AlInstructionSelectPrimitive
        {...register("alInstruction", {
          required: "AL instruction is required!",
        })}
        error={errors.alInstruction}
        includeOffsetOption
      />
    </FormRow>
  );
}
