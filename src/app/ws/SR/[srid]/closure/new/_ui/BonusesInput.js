"use client";

import React from "react";
import { useWatch } from "react-hook-form";
import { isEmpty } from "lodash";
import HStack from "@/ui/HStack";
import BonusAmountInput from "@/app/ws/SR/[srid]/closure/new/_ui/BonusAmountInput";

export default function BonusesInput() {
  const bonuses = useWatch({
    name: "bonuses",
  });
  if (isEmpty(bonuses)) return null;

  return (
    <>
      <h2 className="font-bold">Bonuses</h2>
      <div className="inline-grid grid-cols-2 items-center-safe gap-4 gap-y-3 sm:grid-cols-[auto_minmax(100px,300px)]">
        {bonuses.map(({ chargeDescription }, index) => {
          return (
            <React.Fragment key={`renewal-bonus-${index}`}>
              <label
                htmlFor={`bonuses.${index}.charges`}
                className="cursor-pointer leading-tight"
              >
                <HStack className="flex-nowrap items-start">
                  <p>{index + 1}.</p>

                  <p>{chargeDescription}</p>
                </HStack>
              </label>

              <BonusAmountInput index={index} />
            </React.Fragment>
          );
        })}
      </div>
    </>
  );
}
