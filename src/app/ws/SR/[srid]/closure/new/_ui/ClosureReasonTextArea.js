"use client";

import { FormRow } from "@/ui/Form";
import TextArea from "@/ui/TextArea";
import { useFormContext } from "react-hook-form";

export default function ClosureReasonTextArea() {
  const { register } = useFormContext();

  return (
    <FormRow>
      <TextArea
        label="Client closure reason"
        helpText="The reason will not be shown to the contractor."
        {...register("clientReason")}
      />
    </FormRow>
  );
}
