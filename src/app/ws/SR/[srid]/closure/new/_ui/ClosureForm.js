"use client";

import { useWatch } from "react-hook-form";
import { isEmpty } from "lodash";
import ClosureTypeFormSection from "@/app/ws/SR/[srid]/closure/new/_ui/ClosureTypeFormSection";
import DispatchModeFormSection from "@/app/ws/SR/[srid]/closure/new/_ui/DispatchModeFormSection";
import EndOfContractForm from "@/app/ws/SR/[srid]/closure/new/_ui/EndOfContractForm";
import ConversionForm from "@/app/ws/SR/[srid]/closure/new/_ui/ConversionForm";
import TerminationForm from "@/app/ws/SR/[srid]/closure/new/_ui/TerminationForm";
import ResignationForm from "@/app/ws/SR/[srid]/closure/new/_ui/ResignationForm";

export default function ClosureForm({ employment }) {
  return (
    <>
      <ClosureTypeFormSection employment={employment} />
      <DispatchModeFormSection />
      <RenderForm employment={employment} />
    </>
  );
}

function RenderForm({ employment }) {
  const closureType = useWatch({ name: "closureType" });
  const dispatchMode = useWatch({ name: "dispatchMode" });
  if (closureType !== "Resignation" && isEmpty(dispatchMode)) return null;

  switch (closureType) {
    case "End of Contract":
      return <EndOfContractForm employment={employment} />;
    case "Conversion":
      return <ConversionForm employment={employment} />;
    case "Termination":
      return <TerminationForm employment={employment} />;
    case "Resignation":
      return <ResignationForm />;
    default:
      return null;
  }
}
