import { useFormContext } from "react-hook-form";
import { toFormalDate } from "@/formatters/date";
import AlertCard from "@/ui/AlertCard";

export default function ConversionStepsAlertCard({ employment }) {
  const { watch } = useFormContext();
  const dispatchMode = watch("dispatchMode");

  return (
    <AlertCard variant="pause" ring={false}>
      <p>
        By submitting this <span className="font-semibold">Conversion</span>{" "}
        closure, the system will
      </p>

      {dispatchMode === "Standard" && <StandardList employment={employment} />}

      {dispatchMode === "Manual" && <ManualList employment={employment} />}
    </AlertCard>
  );
}

function StandardList({ employment }) {
  return (
    <ul className="list-outside list-disc pl-8">
      <li>
        Notify <span className="font-semibold">{employment.owner.name}</span> to
        agree and provide the closure details.
      </li>
      <li>Update {employment.fmId}&apos;s last day and closure reason.</li>
      <li>
        Pro-rate the leave record if the official last day is before{" "}
        {employment.fmId}&apos;s expiry date,{" "}
        {toFormalDate(employment.expiredDate)}.
      </li>
      <li>Cancel any leaves beyond the official last day.</li>
      <li>
        Notify{" "}
        <span className="font-semibold">{employment.contractorName}</span> to
        agree to this closure.
      </li>
    </ul>
  );
}

function ManualList({ employment }) {
  return (
    <ul className="list-outside list-disc pl-8">
      <li>Update {employment.fmId}&apos;s last day and closure reason.</li>
      <li>
        Pro-rate the leave record if the official last day is before{" "}
        {employment.fmId}&apos;s expiry date,{" "}
        {toFormalDate(employment.expiredDate)}.
      </li>
      <li>Cancel any leaves beyond the official last day.</li>
      <li>
        Notify{" "}
        <span className="font-semibold">{employment.contractorName}</span> to
        agree to this closure.
      </li>
    </ul>
  );
}
