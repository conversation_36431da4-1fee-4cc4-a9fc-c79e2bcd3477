"use client";

import { useFormContext } from "react-hook-form";
import { isEmpty } from "lodash";
import { FormSection } from "@/ui/Form";
import DispatchClientInformationSection from "@/app/ws/SR/[srid]/closure/new/_ui/DispatchClientInformationSection";
import EmploymentClosureSectionForConversion from "@/app/ws/SR/[srid]/closure/new/_ui/EmploymentClosureSectionForConversion";
import ConversionStepsAlertCard from "@/app/ws/SR/[srid]/closure/new/_ui/ConversionStepsAlertCard";

export default function ConversionForm({ employment }) {
  const { watch } = useFormContext();
  const dispatchMode = watch("dispatchMode");

  if (isEmpty(dispatchMode)) return null;

  return (
    <FormSection>
      {dispatchMode === "Standard" && <DispatchClientInformationSection />}

      {dispatchMode === "Manual" && (
        <EmploymentClosureSectionForConversion employment={employment} />
      )}

      <ConversionStepsAlertCard employment={employment} />
    </FormSection>
  );
}
