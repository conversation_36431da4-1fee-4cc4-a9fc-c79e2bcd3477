"use client";

import { useFormContext } from "react-hook-form";
import { FormRow } from "@/ui/Form";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";

export default function LastDayDatePicker() {
  const { watch } = useFormContext();
  const closureType = watch("closureType");

  return (
    <FormRow className="items-center-safe" colsCount={2}>
      <FutureDatePickerInput
        label="Official last day"
        name="lastDayDate"
        shortcuts={false}
        rules={{
          required: `Official last day is required!`,
        }}
        disabled={closureType === "End of Contract"}
      />
    </FormRow>
  );
}
