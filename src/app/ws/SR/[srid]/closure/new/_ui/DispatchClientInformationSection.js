"use client";

import { useWatch } from "react-hook-form";
import { toFormalDate } from "@/formatters/date";
import { currency } from "@/formatters/numeric";
import pluralize from "pluralize";
import { FormRow, FormSection, parseDateFromDatePicker } from "@/ui/Form";
import { Na } from "@/ui/Typography";
import VStack from "@/ui/VStack";

export default function DispatchClientInformationSection() {
  const [
    alBalanceDays,
    alInstruction,
    alUnpaidTakenDays,
    prevBasicSalary,
    prevBasicSalaryPayKind,
    bonuses,
    closureType,
    lastDayDate,
  ] = useWatch({
    name: [
      "alBalanceDays",
      "alInstruction",
      "alUnpaidTakenDays",
      "prevBasicSalary",
      "prevBasicSalaryPayKind",
      "bonuses",
      "closureType",
      "lastDayDate",
    ],
  });

  return (
    <FormSection title="Client will receive these information...">
      <FormRow>
        <VStack className="gap-0">
          <p className="text-muted">AL balance</p>
          <p>{pluralize("day", alBalanceDays, true)}</p>
        </VStack>

        <VStack className="gap-0">
          <p className="text-muted">AL instruction</p>
          <p>{alInstruction}</p>
        </VStack>
      </FormRow>

      {closureType === "End of Contract" && (
        <>
          <FormRow>
            <VStack className="gap-0">
              <p className="text-muted">AL unpaid taken</p>
              <p>{pluralize("day", alUnpaidTakenDays, true)}</p>
            </VStack>

            <VStack className="gap-0">
              <p className="text-muted">Current basic salary</p>
              <p>
                {currency(prevBasicSalary)} {prevBasicSalaryPayKind}
              </p>
            </VStack>
          </FormRow>

          <FormRow>
            <VStack className="gap-0">
              <p className="text-muted">Bonuses</p>
              {bonuses.map(({ chargeDescription, charges }, index) => (
                <p key={`closure-bonus-${index}`}>
                  {chargeDescription}:{" "}
                  {charges ? (
                    <span className="font-semibold">{currency(charges)}</span>
                  ) : (
                    <Na />
                  )}
                </p>
              ))}
            </VStack>
          </FormRow>
        </>
      )}

      <FormRow>
        <VStack className="gap-0">
          <p className="text-muted">Closure type</p>
          <p>{closureType}</p>
        </VStack>

        <VStack className="gap-0">
          <p className="text-muted">Official last day</p>
          <p>{toFormalDate(parseDateFromDatePicker(lastDayDate))}</p>
        </VStack>
      </FormRow>
    </FormSection>
  );
}
