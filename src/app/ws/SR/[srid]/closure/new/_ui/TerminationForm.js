"use client";

import { useFormContext } from "react-hook-form";
import { isEmpty } from "lodash";
import { FormSection } from "@/ui/Form";
import DispatchClientInformationSection from "@/app/ws/SR/[srid]/closure/new/_ui/DispatchClientInformationSection";
import EmploymentClosureSectionForTermination from "@/app/ws/SR/[srid]/closure/new/_ui/EmploymentClosureSectionForTermination";
import TerminationStepsAlertCard from "@/app/ws/SR/[srid]/closure/new/_ui/TerminationStepsAlertCard";

export default function TerminationForm({ employment }) {
  const { watch } = useFormContext();
  const dispatchMode = watch("dispatchMode");

  if (isEmpty(dispatchMode)) return null;

  return (
    <FormSection>
      {dispatchMode === "Standard" && <DispatchClientInformationSection />}

      {dispatchMode === "Manual" && (
        <EmploymentClosureSectionForTermination employment={employment} />
      )}

      <TerminationStepsAlertCard employment={employment} />
    </FormSection>
  );
}
