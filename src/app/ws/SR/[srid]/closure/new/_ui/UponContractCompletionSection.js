"use client";

import React from "react";
import { FormSection } from "@/ui/Form";
import AlSalaryNoticePeriodInfo from "@/app/ws/SR/[srid]/closure/new/_ui/AlSalaryNoticePeriodInfo";
import AlInstructionSelect from "@/app/ws/SR/[srid]/closure/new/_ui/AlInstructionSelect";
import BonusesInput from "@/app/ws/SR/[srid]/closure/new/_ui/BonusesInput";

export default function UponContractCompletionSection({ employment }) {
  return (
    <div data-form="section" className="space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2 dark:bg-gray-700 dark:text-neutral-100">
        <p className="text-lg font-bold">1.</p>
        <h2 className="text-lg font-bold">Upon Contract Completion</h2>
        <p className="col-start-2 text-sm text-muted">
          Manage how this contract should be completed before closure.
        </p>
      </div>

      <FormSection className="ml-9">
        <AlSalaryNoticePeriodInfo employment={employment} />
        <AlInstructionSelect />
        <BonusesInput />
      </FormSection>
    </div>
  );
}
