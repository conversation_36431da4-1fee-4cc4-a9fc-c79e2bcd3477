"use client";

import { startTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { useProgressBar } from "@/ui/ProgressBar";
import { addMonths, startOfToday } from "date-fns";
import { isEmpty } from "lodash";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Form, formatDateForDatePicker, showFormErrors } from "@/ui/Form";
import ClosureForm from "@/app/ws/SR/[srid]/closure/new/_ui/ClosureForm";
import CreateButton from "@/app/ws/SR/[srid]/closure/new/_ui/CreateButton";

const MUTATION = `
  mutation createClosure($employmentId: ID!, $input: ClosureInput!) {
    execute:createClosure(employmentId: $employmentId, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on EndOfContract {
          id
          staffResourceUrl
        }

        ... on Conversion {
          id
          staffResourceUrl
        }

        ... on Termination {
          id
          staffResourceUrl
        }
      }
    }
  }
`;

function getFormDefaultValues(employment, searchParams) {
  const type = searchParams.get("type");
  const mode = searchParams.get("mode");

  return {
    closureType: type || "",
    dispatchMode: mode || "",
    alBalanceDays: employment.currentLeaveTerm?.annualEntitlement?.balance || 0,
    alInstruction: "Clear",
    prevBasicSalary: employment.currentBasicSalary?.amountAfterRate,
    prevBasicSalaryPayKind: employment.currentBasicSalary?.payKind,
    alUnpaidTakenDays:
      employment.currentLeaveTerm?.annualEntitlement?.unpaid || 0,
    bonuses: !isEmpty(employment.bonusesForRenewal)
      ? employment.bonusesForRenewal.map(
          ({ chargeDescription, amountAfterRate }) => ({
            chargeDescription,
            charges: amountAfterRate,
          }),
        )
      : [],
    lastDayDate: formatDateForDatePicker(addMonths(startOfToday(), 1)),
    clientReason: "",
  };
}

export default function NewClosureForm({ employment }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const progress = useProgressBar();
  const form = useForm({
    defaultValues: getFormDefaultValues(employment, searchParams),
  });
  const { setError } = form;
  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: ({ record }) => {
      toast.success("Closure created successfully!");
      router.push(record.staffResourceUrl);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    progress.start();

    startTransition(() => {
      execute({ employmentId: employment.id, input });
      progress.done();
    });
  };

  return (
    <Form className="space-y-4" form={form} onSubmit={onSubmit}>
      <ClosureForm employment={employment} />

      <CreateButton pending={pending} />
    </Form>
  );
}
