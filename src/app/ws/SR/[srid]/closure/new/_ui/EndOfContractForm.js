"use client";

import { useFormContext } from "react-hook-form";
import { isEmpty } from "lodash";
import { FormSection } from "@/ui/Form";
import DispatchClientInformationSection from "@/app/ws/SR/[srid]/closure/new/_ui/DispatchClientInformationSection";
import UponContractCompletionSection from "@/app/ws/SR/[srid]/closure/new/_ui/UponContractCompletionSection";
import EmploymentClosureSection from "@/app/ws/SR/[srid]/closure/new/_ui/EmploymentClosureSection";
import EndOfContractStepsAlertCard from "@/app/ws/SR/[srid]/closure/new/_ui/EndOfContractStepsAlertCard";

export default function EndOfContractForm({ employment }) {
  const { watch } = useFormContext();
  const dispatchMode = watch("dispatchMode");

  if (isEmpty(dispatchMode)) return null;

  return (
    <FormSection>
      {dispatchMode === "Standard" && <DispatchClientInformationSection />}

      {dispatchMode === "Manual" && (
        <>
          <UponContractCompletionSection employment={employment} />

          <EmploymentClosureSection />
        </>
      )}

      <EndOfContractStepsAlertCard employment={employment} />
    </FormSection>
  );
}
