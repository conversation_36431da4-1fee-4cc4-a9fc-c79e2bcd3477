"use client";

import { useFormContext } from "react-hook-form";
import RadioBox from "@/components/RadioBox";

const DISPATCH_MODES = [
  {
    label: "Standard",
    value: "Standard",
    description:
      "Requires client to provide closure details and acknowledgement before dispatching to contractor.",
  },
  {
    label: "Manual",
    value: "Manual",
    description:
      "Directly dispatch to contractor with the provided closure details for acknowledgement.",
  },
];

export default function DispatchModeRadioBox() {
  const {
    watch,
    register,
    setValue,
    formState: { errors },
  } = useFormContext();
  const closureType = watch("closureType");
  const dispatchMode = watch("dispatchMode");
  const isResignation = closureType === "Resignation";

  register("dispatchMode", {
    required: !isResignation && "Dispatch mode is required!",
  });

  return (
    <RadioBox
      defaultValue={dispatchMode}
      setFormValue={(value) => {
        setValue("dispatchMode", value, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });
      }}
      items={DISPATCH_MODES}
      error={errors.dispatchMode}
    />
  );
}
