"use client";

import { useFormContext } from "react-hook-form";
import SalaryInput from "@/ui/SalaryInput";

export default function BonusAmountInput({ index }) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <SalaryInput
      labelId={`bonuses.${index}.charges`}
      {...register(`bonuses.${index}.charges`, {
        valueAsNumber: true,
      })}
      placeholder="Leave blank if undecided"
      error={errors.bonuses?.[index]}
    />
  );
}
