"use client";

import { FormRow, FormSection } from "@/ui/Form";
import HStack from "@/ui/HStack";
import ViewTerminationNoticeDialog from "@/app/ws/SR/[srid]/closure/_ui/ViewTerminationNoticeDialog";
import ClosureTypeRadioBox from "@/app/ws/SR/[srid]/closure/new/_ui/ClosureTypeRadioBox";

export default function ClosureTypeFormSection({ employment }) {
  return (
    <FormSection title={<Title employment={employment} />}>
      <FormRow>
        <ClosureTypeRadioBox name="closureType" />
      </FormRow>
    </FormSection>
  );
}

function Title({ employment }) {
  return (
    <HStack className="gap-4">
      <p>Choose a closure type</p>

      <div className="ml-auto">
        <ViewTerminationNoticeDialog employment={employment} />
      </div>
    </HStack>
  );
}
