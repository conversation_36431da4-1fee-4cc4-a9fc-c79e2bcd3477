"use client";

import { isEmpty } from "lodash";
import { useWatch } from "react-hook-form";
import { FormRow, FormSection } from "@/ui/Form";
import DispatchModeRadioBox from "@/app/ws/SR/[srid]/closure/new/_ui/DispatchModeRadioBox";

export default function DispatchModeFormSection() {
  const closureType = useWatch({ name: "closureType" });
  if (isEmpty(closureType)) return null;
  if (closureType === "Resignation") return null;

  return (
    <FormSection title="Choose a dispatch mode">
      <FormRow>
        <DispatchModeRadioBox />
      </FormRow>
    </FormSection>
  );
}
