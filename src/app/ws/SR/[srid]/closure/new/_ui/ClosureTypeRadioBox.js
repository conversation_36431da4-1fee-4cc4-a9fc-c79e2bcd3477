"use client";

import { useFormContext, useWatch } from "react-hook-form";
import RadioBox from "@/components/RadioBox";

const CLOSURE_TYPES = [
  {
    label: "End of Contract",
    value: "End of Contract",
    description: "Auto closure or client proceeds with non-renewal.",
  },
  {
    label: "Conversion",
    value: "Conversion",
    description: "Change of entity.",
  },
  { label: "Termination", value: "Termination", description: "???" },
  {
    label: "Resignation",
    value: "Resignation",
    description: "Contractor requests a resignation.",
  },
];

export default function ClosureTypeRadioBox() {
  const {
    register,
    setValue,
    formState: { errors },
  } = useFormContext();
  const closureType = useWatch({ name: "closureType" });

  register("closureType", { required: "Closure type is required!" });

  return (
    <RadioBox
      defaultValue={closureType}
      setFormValue={(value) => {
        setValue("closureType", value, {
          shouldTouch: true,
          shouldDirty: true,
          shouldValidate: true,
        });
      }}
      items={CLOSURE_TYPES}
      error={errors.closureType}
    />
  );
}
