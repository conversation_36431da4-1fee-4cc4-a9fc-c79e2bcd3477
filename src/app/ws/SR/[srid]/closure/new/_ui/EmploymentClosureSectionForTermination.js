"use client";

import { FormSection } from "@/ui/Form";
import LastDayDatePicker from "@/app/ws/SR/[srid]/closure/new/_ui/LastDayDatePicker";
import AlInstructionSelect from "@/app/ws/SR/[srid]/closure/new/_ui/AlInstructionSelect";
import ClosureReasonTextArea from "@/app/ws/SR/[srid]/closure/new/_ui/ClosureReasonTextArea";
import AlSalaryNoticePeriodInfo from "@/app/ws/SR/[srid]/closure/new/_ui/AlSalaryNoticePeriodInfo";

export default function EmploymentClosureSectionForTermination({ employment }) {
  return (
    <div data-form="section" className="space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2 dark:bg-gray-700 dark:text-neutral-100">
        <p className="text-lg font-bold"></p>
        <h2 className="text-lg font-bold">Employment Closure</h2>
        <p className="col-start-2 text-sm text-muted">
          Provide termination details.
        </p>
      </div>

      <FormSection className="ml-9">
        <AlSalaryNoticePeriodInfo employment={employment} />
        <AlInstructionSelect />
        <LastDayDatePicker />
        <ClosureReasonTextArea />
      </FormSection>
    </div>
  );
}
