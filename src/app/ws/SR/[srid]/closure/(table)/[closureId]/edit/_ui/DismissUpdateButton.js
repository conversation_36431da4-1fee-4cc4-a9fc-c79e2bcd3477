"use client";

import { ProgressBarLink } from "@/ui/ProgressBar";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";

export default function DismissUpdateButton({ pending, closure }) {
  const { staffResourceUrl } = closure;

  return (
    <HStack className="justify-end-safe gap-4">
      <ProgressBarLink href={staffResourceUrl}>
        <Button variant="secondary" outline disabled={pending}>
          Dismiss
        </Button>
      </ProgressBarLink>
      <Button type="submit" loading={pending}>
        Update
      </Button>
    </HStack>
  );
}
