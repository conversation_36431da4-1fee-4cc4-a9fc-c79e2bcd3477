"use client";

import { parseISO } from "date-fns";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";

export default function ResignationEffectiveDatePicker({ closure }) {
  const { employment } = closure;

  return (
    <FutureDatePickerInput
      label="Resignation effective date"
      name="effectiveFromDate"
      shortcuts={false}
      rules={{
        required: "Resignation effective date is required!",
      }}
      maxDate={parseISO(employment.expiredDate)}
      allowStartOfPreviousMonth
    />
  );
}
