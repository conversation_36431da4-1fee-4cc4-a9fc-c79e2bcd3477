"use client";

import AlertCard from "@/ui/AlertCard";
import Card from "@/ui/Card";
import { List, PropertyList } from "@/ui/PropertyList";
import RawHtml from "@/ui/RawHtml";
import { isEmpty } from "lodash";

export default function LetterOfUndertakingForPrPropertyCard({ closure }) {
  if (isEmpty(closure.louForm)) return null;

  return (
    <Card className="space-y-4">
      <h1 className="text-base font-bold">Letter of Undertaking (IRAS)</h1>

      <AlertCard variant="info" contentClassName="text-sm" ring={false} center>
        {closure.employment.contractorName} has declared that{" "}
        {closure.contractor.pronoun}{" "}
        <span className="font-semibold">
          {closure.leavingSingaporePermanently ? "WILL" : "WILL NOT"}
        </span>{" "}
        be leaving Singapore permanently after the cessation of employment.
      </AlertCard>

      <PropertyList>
        <List.Date label="Declaration date">
          {closure.louForm.declarationDate}
        </List.Date>
        <List label="Name">{closure.louForm.name}</List>
        <List label="NRIC">{closure.louForm.nric}</List>
        <List label="Contact number">{closure.louForm.contactNumber}</List>
        <List label="Residential address">
          <RawHtml className="whitespace-pre-line">
            {closure.louForm.residentialAddress}
          </RawHtml>
        </List>
      </PropertyList>
    </Card>
  );
}
