"use client";

import { ProgressBarLink } from "@/ui/ProgressBar";
import { ChevronLeftIcon } from "lucide-react";
import HStack from "@/ui/HStack";

export default function Heading({ closure }) {
  return (
    <HStack className="flex-nowrap items-center-safe">
      <ProgressBarLink href={closure.staffResourceUrl}>
        <ChevronLeftIcon size={22} className="stroke-3" />
      </ProgressBarLink>

      <h1 className="text-xl font-bold">
        Edit {closure.fmId}: {closure.closureType}
      </h1>
    </HStack>
  );
}
