"use client";

import Card from "@/ui/Card";
import QuotedRemark from "@/components/QuotedRemark";
import UponContractCompletionPropertyList from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/end-of-contract/UponContractCompletionPropertyList";
import EmploymentClosurePropertyList from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/end-of-contract/EmploymentClosurePropertyList";

export default function ClosureDetailsPropertyCard({ closure }) {
  return (
    <Card className="space-y-4">
      <UponContractCompletionPropertyList closure={closure} />

      <EmploymentClosurePropertyList closure={closure} />

      <QuotedRemark title="Closure reason" contentClassName="text-sm">
        {closure.clientReason}
      </QuotedRemark>
    </Card>
  );
}
