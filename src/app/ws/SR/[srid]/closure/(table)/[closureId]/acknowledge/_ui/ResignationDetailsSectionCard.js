"use client";

import QuotedRemark from "@/components/QuotedRemark";
import { toFormalDate } from "@/formatters/date";
import Card from "@/ui/Card";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";
import { FormRow, FormSection } from "@/ui/Form";
import VStack from "@/ui/VStack";
import { parseISO } from "date-fns";

export default function ResignationDetailsSectionCard({ closure }) {
  const { employment } = closure;

  return (
    <Card className="space-y-4">
      <h2 className="text-lg font-bold">1. Resignation Details</h2>

      <FormSection>
        <FormRow>
          <VStack className="gap-0">
            <p className="text-sm text-muted">Resignation effective date</p>
            <p>{toFormalDate(closure.effectiveFromDate)}</p>
          </VStack>

          <VStack className="gap-0">
            <p className="text-sm text-muted">Official last day</p>
            <p>{toFormalDate(closure.lastDayDate)}</p>
          </VStack>
        </FormRow>

        <FormRow>
          <QuotedRemark title="Reason for leaving" contentClassName="text-sm">
            {closure.contractorReason}
          </QuotedRemark>
        </FormRow>

        {closure.specialRequest && (
          <>
            <VStack className="gap-0 border-b pb-2">
              <h2 className="font-semibold">Special Request</h2>
              <p className="text-sm text-muted">
                Review the special request and decide the final approved last
                day.
              </p>
            </VStack>
            <FormRow>
              <VStack className="gap-0">
                <p className="text-sm text-muted">Requested for</p>
                <p>{closure.specialRequestType}</p>
              </VStack>

              <VStack className="gap-0">
                <p className="text-sm text-muted">Requested last day</p>
                <p>{toFormalDate(closure.requestedLastDayDate)}</p>
              </VStack>
            </FormRow>

            <FormRow>
              <QuotedRemark
                title="Special request reason"
                contentClassName="text-sm"
              >
                {closure.specialRequestReason}
              </QuotedRemark>
            </FormRow>

            <FormRow colsCount={2}>
              <FutureDatePickerInput
                label="Approved last day"
                name="approvedLastDayDate"
                shortcuts={false}
                rules={{
                  required: "Approved last day is required!",
                }}
                maxDate={parseISO(employment.expiredDate)}
              />
            </FormRow>
          </>
        )}
      </FormSection>
    </Card>
  );
}
