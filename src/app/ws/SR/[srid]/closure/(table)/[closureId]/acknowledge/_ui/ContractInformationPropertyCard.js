"use client";

import pluralize from "pluralize";
import { List, PropertyList } from "@/ui/PropertyList";
import Card from "@/ui/Card";
import RawHtml from "@/ui/RawHtml";

export default function ContractInformationPropertyCard({ closure }) {
  return (
    <Card>
      <PropertyList title="Contract Details" accordion>
        <List label="Company">{closure.employment.company.name}</List>
        <List label="Project">
          <RawHtml>{closure.employment.project.name}</RawHtml>
        </List>
        <List label="Designation">{closure.prevDesignation}</List>
        <List label="Period">{closure.employment.displayPeriod}</List>
        <List label="Notice days">
          {pluralize("day", closure.employment.effectiveNoticeDays, true)}
        </List>
      </PropertyList>
    </Card>
  );
}
