import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import Banner from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/Banner";
import AcknowledgeClosureForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AcknowledgeClosureForm";

const QUERY = `
  query closure($employmentId: ID!, $id: ID!) {
    closure(employmentId: $employmentId, id: $id) {
      slug
      id
      fmId
      closureType
      dispatchMode
      status
      statusUpdatedAt

      alBalanceDays
      alUnpaidTakenDays
      prevBasicSalary
      prevBasicSalaryPayKind
      prevDesignation
      terminationSnapshot

      alInstruction
      effectiveFromDate
      lastDayDate

      bonuses {
        chargeDescription
        charges
      }

      contractorReason
      clientReason

      specialRequest
      specialRequestType
      specialRequestReason
      requestedLastDayDate
      approvedLastDayDate

      contractorAgreedAt
      clientAgreedAt
      loeDispatchDate
      loeDispatchedAt

      employment {
        id
        slug
        fmId
        startDate
        expiredDate
        contractorName
        displayPeriod
        effectiveNoticeDays

        company {
          fmId
          name
          staffResourceUrl
        }

        project {
          fmId
          name
          staffResourceUrl
        }

        ent {
          fmId
          description
          staffResourceUrl
        }
      }
    }
  }
`;

export async function generateMetadata({ params }) {
  const { srid, closureId } = await params;
  const res = await apiQuery(QUERY, { employmentId: srid, id: closureId });
  const closure = res.data.closure;

  return {
    title: `${closure.fmId}: ${closure.closureType} - ${closure.employment.contractorName}`,
  };
}

export default async function Page({ params }) {
  const { srid, closureId } = await params;
  const res = await apiQuery(QUERY, { employmentId: srid, id: closureId });
  const closure = res.data.closure;

  if (closure === null) notFound();

  return (
    <div className="border-t pt-4">
      <Banner closure={closure} />
      <div className="mx-auto max-w-[800px] p-4">
        <AcknowledgeClosureForm closure={closure} />
      </div>
    </div>
  );
}
