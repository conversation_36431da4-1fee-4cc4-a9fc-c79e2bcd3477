"use client";

import { useWatch } from "react-hook-form";
import { parseDateFromDatePicker } from "@/ui/Form";
import { isSameDay, parseISO } from "date-fns";
import { toSimpleDate } from "@/formatters/date";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";

export default function ApprovedLastDayDatePicker({ closure }) {
  const { computedLastDayDate: derivedComputedLastDayDate, employment } =
    closure;
  const computedLastDayDate = parseISO(derivedComputedLastDayDate);
  const [specialRequest, approvedLastDayDateFromDatePicker] = useWatch({
    name: ["specialRequest", "approvedLastDayDate"],
  });
  const approvedLastDayDate = parseDateFromDatePicker(
    approvedLastDayDateFromDatePicker,
  );
  const hasSpecialRequest = specialRequest === "Yes";

  const isDifferent =
    approvedLastDayDate && !isSameDay(computedLastDayDate, approvedLastDayDate);

  return (
    <FutureDatePickerInput
      label="Approved last day"
      name="approvedLastDayDate"
      shortcuts={false}
      rules={{
        required: hasSpecialRequest && "Approved last day is required!",
      }}
      maxDate={parseISO(employment.expiredDate)}
      inputProps={{
        helpText: isDifferent
          ? `Last day differs from the notice period end date (${toSimpleDate(computedLastDayDate)}).`
          : null,
      }}
      allowStartOfPreviousMonth
    />
  );
}
