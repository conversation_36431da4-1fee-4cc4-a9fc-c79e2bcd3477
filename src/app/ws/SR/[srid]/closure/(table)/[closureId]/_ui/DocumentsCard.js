"use client";

import isNotEmpty from "@/lib/isNotEmpty";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import Card from "@/ui/Card";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Main Description",
    columnWidth: 200,
    propertyName: "mainDescription",
    visible: true,
  },
  {
    columnName: "Description",
    columnWidth: 280,
    propertyName: "description",
    visible: true,
    href: "${row.fileUrl}",
    hrefTarget: "_blank",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Status",
    columnWidth: 120,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Uploaded",
    columnWidth: 200,
    propertyName: "uploadedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default function DocumentsCard({ closure }) {
  const { employmentFiles } = closure;

  return (
    <Card className="space-y-4">
      <h1 className="font-bold">Documents ({employmentFiles.length})</h1>

      {isNotEmpty(employmentFiles) ? (
        <DataGrid.Root>
          <DataGrid.Content>
            <DataGridTable
              numRows={employmentFiles.length}
              data={employmentFiles}
              tableColumns={DEFAULT_TABLE_COLUMNS}
              defaultTableColumns={DEFAULT_TABLE_COLUMNS}
            />
          </DataGrid.Content>
        </DataGrid.Root>
      ) : (
        <p className="text-sm text-muted">
          No documents found for {closure.fmId}.
        </p>
      )}
    </Card>
  );
}
