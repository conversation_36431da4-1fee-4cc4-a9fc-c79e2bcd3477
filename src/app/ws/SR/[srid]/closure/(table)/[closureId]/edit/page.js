import { notFound } from "next/navigation";
import apiQuery from "@/lib/apiQuery";
import Heading from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/Heading";
import EditClosureForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/EditClosureForm";

const QUERY = `
  query closure($employmentId: ID!, $id: ID!) {
    closure(employmentId: $employmentId, id: $id) {
      id
      fmId
      closureType
      dispatchMode
      status
      statusUpdatedAt
      clientDispatchable
      contractorDispatchable

      alBalanceDays
      alUnpaidTakenDays
      prevBasicSalary
      prevBasicSalaryPayKind
      prevDesignation
      terminationSnapshot

      alInstruction
      effectiveFromDate
      lastDayDate

      contractorReason
      clientReason

      specialRequest
      specialRequestType
      specialRequestReason
      requestedLastDayDate
      approvedLastDayDate
      computedLastDayDate

      contractorAgreedAt
      clientAgreedAt
      loeDispatchDate
      loeDispatchedAt

      staffResourceUrl

      bonuses {
        chargeDescription
        charges
      }

      employment {
        id
        fmId
        startDate
        expiredDate
        contractorName
        displayPeriod
        effectiveNoticeDays

        residencyStatus

        company {
          fmId
          name
          staffResourceUrl
        }

        project {
          fmId
          name
          staffResourceUrl
        }

        ent {
          fmId
          description
          staffResourceUrl
        }

        owner {
          name
        }
      }
    }
  }
`;

export async function generateMetadata({ params }) {
  const { srid, closureId } = await params;
  const res = await apiQuery(QUERY, { employmentId: srid, id: closureId });
  const closure = res.data.closure;

  return {
    title: `Edit ${closure.fmId}: ${closure.closureType} - ${closure.employment.contractorName}`,
  };
}

export default async function Page({ params }) {
  const { srid, closureId } = await params;
  const res = await apiQuery(QUERY, { employmentId: srid, id: closureId });
  const closure = res.data.closure;
  if (closure === null) notFound();

  return (
    <div className="space-y-4 border-t p-4">
      <Heading closure={closure} />

      <div className="mx-auto max-w-[800px] space-y-4">
        <EditClosureForm closure={closure} />
      </div>
    </div>
  );
}
