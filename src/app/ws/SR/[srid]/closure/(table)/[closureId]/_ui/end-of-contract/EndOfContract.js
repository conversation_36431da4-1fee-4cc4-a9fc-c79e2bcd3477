"use client";

import ClosureDetailsPropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/end-of-contract/ClosureDetailsPropertyCard";
import LetterOfUndertakingForPrPropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/LetterOfUndertakingForPrPropertyCard";
import CurrentContractAndOutcomePropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/CurrentContractAndOutcomePropertyCard";
import DocumentsCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/DocumentsCard";

export default function EndOfContract({ closure }) {
  return (
    <div className="mx-auto grid max-w-[1000px] grid-cols-1 gap-2 sm:grid-cols-[60%_40%]">
      <div className="space-y-2">
        <ClosureDetailsPropertyCard closure={closure} />
        <LetterOfUndertakingForPrPropertyCard closure={closure} />
      </div>
      <div className="space-y-2">
        <CurrentContractAndOutcomePropertyCard closure={closure} />
      </div>
      <div className="space-y-2 sm:col-span-2">
        <DocumentsCard closure={closure} />
      </div>
    </div>
  );
}
