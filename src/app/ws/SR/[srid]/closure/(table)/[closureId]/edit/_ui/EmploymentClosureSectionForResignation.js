"use client";

import { FormRow, FormSection } from "@/ui/Form";
import AlInstructionSelect from "@/components/select/AlInstructionSelect";
import ClosureReasonTextArea from "@/app/ws/SR/[srid]/closure/new/_ui/ClosureReasonTextArea";

export default function EmploymentClosureSectionForResignation() {
  return (
    <div data-form="section" className="mt-10 space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2 dark:bg-gray-700 dark:text-neutral-100">
        <p className="text-lg font-bold">2.</p>
        <h2 className="text-lg font-bold">Employment Closure</h2>
        <p className="col-start-2 text-sm text-muted">
          Provide employment closure details.
        </p>
      </div>

      <FormSection className="ml-9">
        <FormRow colsCount={2}>
          <AlInstructionSelect includeOffsetOption />
        </FormRow>

        <ClosureReasonTextArea />
      </FormSection>
    </div>
  );
}
