"use client";

import { useFormContext, useWatch } from "react-hook-form";
import { FormRow } from "@/ui/Form";
import { default as AlInstructionSelectPrimitive } from "@/components/select/AlInstructionSelect";
import VStack from "@/ui/VStack";

export default function AlInstructionSelect() {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const alInstruction = useWatch({ name: "alInstruction" });

  if (alInstruction === "Offset notice period") {
    return (
      <FormRow colsCount={2}>
        <VStack className="gap-0">
          <p className="text-sm text-muted">AL instruction</p>
          <p>{alInstruction}</p>
        </VStack>
      </FormRow>
    );
  }

  return (
    <FormRow colsCount={2}>
      <AlInstructionSelectPrimitive
        {...register("alInstruction", { required: " " })}
        error={errors.alInstruction}
      />
    </FormRow>
  );
}
