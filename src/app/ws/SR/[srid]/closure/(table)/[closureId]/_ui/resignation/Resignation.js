"use client";

import ResignationDetailsPropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/resignation/ResignationDetailsPropertyCard";
import EmploymentClosurePropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/resignation/EmploymentClosurePropertyCard";
import LetterOfUndertakingForPrPropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/LetterOfUndertakingForPrPropertyCard";
import CurrentContractAndOutcomePropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/CurrentContractAndOutcomePropertyCard";
import DocumentsCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/DocumentsCard";

export default function Resignation({ closure }) {
  return (
    <div className="mx-auto grid max-w-[1000px] grid-cols-1 gap-2 sm:grid-cols-[60%_40%]">
      <div className="space-y-2">
        <ResignationDetailsPropertyCard closure={closure} />
        <EmploymentClosurePropertyCard closure={closure} />
        <LetterOfUndertakingForPrPropertyCard closure={closure} />
      </div>
      <div className="space-y-2">
        <CurrentContractAndOutcomePropertyCard closure={closure} />
      </div>
      <div className="space-y-2 sm:col-span-2">
        <DocumentsCard closure={closure} />
      </div>
    </div>
  );
}
