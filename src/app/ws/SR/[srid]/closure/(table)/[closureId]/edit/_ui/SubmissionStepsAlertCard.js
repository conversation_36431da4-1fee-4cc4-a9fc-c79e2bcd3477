"use client";

import { useFormContext, useWatch } from "react-hook-form";
import { toFormalDate } from "@/formatters/date";
import { FormRow, FormSection } from "@/ui/Form";
import VStack from "@/ui/VStack";
import AlertCard from "@/ui/AlertCard";
import Checkbox from "@/ui/Checkbox";

export default function SubmissionStepsAlertCard({ closure }) {
  const { clientDispatchable, contractorDispatchable } = closure;

  return (
    <AlertCard variant="pause" ring={false}>
      <FormSection>
        {(clientDispatchable || contractorDispatchable) && (
          <VStack className="gap-1">
            <DispatchToClCheckbox closure={closure} />
            <DispatchToCaCheckbox closure={closure} />
          </VStack>
        )}

        <p>By updating this closure, the system will:</p>

        <StepsList closure={closure} />
      </FormSection>
    </AlertCard>
  );
}

function DispatchToClCheckbox({ closure }) {
  const { employment, clientDispatchable } = closure;
  const { register } = useFormContext();
  const dispatchToContractor = useWatch({ name: "dispatchToContractor" });
  if (!clientDispatchable) return null;

  return (
    <FormRow className="leading-tight select-none">
      <Checkbox
        {...register("dispatchToClient")}
        suffix={
          <span>
            Dispatch this closure to{" "}
            <span className="font-semibold">
              {employment.company.fmId}: {employment.company.name}
            </span>
            .
          </span>
        }
        disabled={dispatchToContractor}
      />
    </FormRow>
  );
}

function DispatchToCaCheckbox({ closure }) {
  const { employment, contractorDispatchable } = closure;
  const { register } = useFormContext();
  const dispatchToClient = useWatch({ name: "dispatchToClient" });
  if (!contractorDispatchable) return null;

  return (
    <FormRow className="leading-tight select-none">
      <Checkbox
        {...register("dispatchToContractor")}
        suffix={
          <span>
            Dispatch this closure to{" "}
            <span className="font-semibold">{employment.contractorName}</span>{" "}
            on behalf of{" "}
            <span className="font-semibold">
              {employment.company.fmId}: {employment.company.name}
            </span>
            .
          </span>
        }
        disabled={dispatchToClient}
      />
    </FormRow>
  );
}

function StepsList({ closure }) {
  const { employment } = closure;
  const [dispatchToClient, dispatchToContractor] = useWatch({
    name: ["dispatchToClient", "dispatchToContractor"],
  });

  return (
    <ul className="list-outside list-disc pl-8">
      <li>Update {employment.fmId}&apos;s last day and closure reason.</li>
      <li>
        Pro-rate the leave record if the official last day is before{" "}
        {employment.fmId}&apos;s expiry date,{" "}
        {toFormalDate(employment.expiredDate)}.
      </li>
      <li>Cancel any leaves beyond the official last day.</li>
      {dispatchToClient && (
        <li>
          Notify <span className="font-semibold">{employment.owner.name}</span>{" "}
          to agree and provide the closure details.
        </li>
      )}
      {dispatchToContractor && (
        <li>
          Notify{" "}
          <span className="font-semibold">{employment.contractorName}</span> to
          agree to this closure.
        </li>
      )}
    </ul>
  );
}
