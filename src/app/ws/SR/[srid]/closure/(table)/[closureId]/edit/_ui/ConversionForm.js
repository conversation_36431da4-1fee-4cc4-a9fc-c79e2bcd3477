"use client";

import { FormSection } from "@/ui/Form";
import BeforeUpdateAlertCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/BeforeUpdateAlertCard";
import EmploymentClosureSectionForConversion from "@/app/ws/SR/[srid]/closure/new/_ui/EmploymentClosureSectionForConversion";

export default function ConversionForm({ closure }) {
  return (
    <FormSection>
      <BeforeUpdateAlertCard closure={closure} />

      <EmploymentClosureSectionForConversion employment={closure.employment} />
    </FormSection>
  );
}
