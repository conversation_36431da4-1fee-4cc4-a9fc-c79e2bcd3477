"use client";

import { useFormContext } from "react-hook-form";
import toSentence from "@/formatters/toSentence";
import Checkbox from "@/ui/Checkbox";
import JoblineCompanyName from "@/ui/JoblineCompanyName";

export default function ConfirmationCheckbox({ closure }) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <div className="leading-tight select-none">
      <Checkbox
        {...register("confirmation")}
        suffix={<CheckboxSuffix closure={closure} />}
        error={errors.confirmation}
      />
    </div>
  );
}

function CheckboxSuffix({ closure }) {
  const { employment } = closure;
  const sections = [];

  if (closure.closureType === "End of Contract")
    sections.push("Contract Completion");
  if (closure.closureType === "Resignation")
    sections.push("Resignation Details");

  sections.push("Employment Closure");

  return (
    <span>
      I confirm that the details provided are accurate and hereby submit my
      official request to <JoblineCompanyName /> to proceed with the{" "}
      <span className="font-semibold">{toSentence(sections)}</span> to{" "}
      <span className="font-semibold text-blue-700 dark:text-blue-400">
        {employment.contractorName}
      </span>{" "}
      on behalf of{" "}
      <span className="font-semibold text-blue-700 dark:text-blue-400">
        {employment.company.name}
      </span>
      .
    </span>
  );
}
