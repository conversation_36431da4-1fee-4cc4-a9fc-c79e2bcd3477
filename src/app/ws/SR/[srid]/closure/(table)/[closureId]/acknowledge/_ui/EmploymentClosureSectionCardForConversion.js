"use client";

import { useFormContext } from "react-hook-form";
import { toFormalDate } from "@/formatters/date";
import { FormRow, FormSection } from "@/ui/Form";
import VStack from "@/ui/VStack";
import Card from "@/ui/Card";
import TextArea from "@/ui/TextArea";
import AlSalaryNoticePeriodInfo from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AlSalaryNoticePeriodInfo";
import AlInstructionSelect from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AlInstructionSelect";

export default function EmploymentClosureSectionCardForConversion({ closure }) {
  const { register } = useFormContext();

  return (
    <Card className="space-y-4">
      <h2 className="text-lg font-bold">Employment Closure</h2>

      <AlSalaryNoticePeriodInfo closure={closure} />

      <FormSection>
        <AlInstructionSelect />

        <FormRow>
          <VStack className="gap-0">
            <p className="text-sm text-muted">Type</p>
            <p>{closure.closureType}</p>
          </VStack>

          <VStack className="gap-0">
            <p className="text-sm text-muted">Official last day</p>
            <p>{toFormalDate(closure.lastDayDate)}</p>
          </VStack>
        </FormRow>

        <FormRow>
          <TextArea
            label="Closure reason"
            helpText="The reason will not be shown to the contractor."
            {...register("clientReason")}
          />
        </FormRow>
      </FormSection>
    </Card>
  );
}
