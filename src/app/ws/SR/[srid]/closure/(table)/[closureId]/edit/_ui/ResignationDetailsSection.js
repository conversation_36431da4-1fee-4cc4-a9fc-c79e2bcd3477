"use client";

import { FormRow, FormSection } from "@/ui/Form";
import AlSalaryNoticePeriodInfo from "@/app/ws/SR/[srid]/closure/new/_ui/AlSalaryNoticePeriodInfo";
import ResignationEffectiveDatePicker from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/ResignationEffectiveDatePicker";
import OfficialLastDayDatePicker from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/OfficialLastDayDatePicker";
import ReasonForLeavingTextArea from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/ReasonForLeavingTextArea";
import SpecialRequestSection from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/SpecialRequestSection";

export default function ResignationDetailsSection({ closure }) {
  return (
    <div data-form="section" className="space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2 dark:bg-gray-700 dark:text-neutral-100">
        <p className="text-lg font-bold">1.</p>
        <h2 className="text-lg font-bold">Resignation Details</h2>
        <p className="col-start-2 text-sm text-muted">
          Manage how this contract should be closed before resignation.
        </p>
      </div>

      <FormSection className="ml-9">
        <AlSalaryNoticePeriodInfo employment={closure.employment} />

        <FormRow>
          <ResignationEffectiveDatePicker closure={closure} />
          <OfficialLastDayDatePicker closure={closure} />
        </FormRow>

        <FormRow>
          <ReasonForLeavingTextArea />
        </FormRow>

        <SpecialRequestSection closure={closure} />
      </FormSection>
    </div>
  );
}
