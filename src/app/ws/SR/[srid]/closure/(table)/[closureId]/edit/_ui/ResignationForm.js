"use client";

import { FormSection } from "@/ui/Form";
import BeforeUpdateAlertCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/BeforeUpdateAlertCard";
import ResignationDetailsSection from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/ResignationDetailsSection";
import EmploymentClosureSectionForResignation from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/EmploymentClosureSectionForResignation";

export default function ResignationForm({ closure }) {
  return (
    <FormSection>
      <BeforeUpdateAlertCard closure={closure} />

      <ResignationDetailsSection closure={closure} />

      <EmploymentClosureSectionForResignation />
    </FormSection>
  );
}
