"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { useProgressBar } from "@/ui/ProgressBar";
import { startTransition } from "react";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Form, formatDateForDatePicker, showFormErrors } from "@/ui/Form";
import EndOfContractForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/EndOfContractForm";
import ConversionForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/ConversionForm";
import ResignationForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/ResignationForm";
import TerminationForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/TerminationForm";
import SubmissionStepsAlertCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/SubmissionStepsAlertCard";
import DismissUpdateButton from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/DismissUpdateButton";

const MUTATION = `
  mutation updateClosure($id: ID!, $input: ClosureInput!) {
    execute:updateClosure(id: $id, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on EndOfContract {
          id
          staffResourceUrl
        }

        ... on Conversion {
          id
          staffResourceUrl
        }

        ... on Resignation {
          id
          staffResourceUrl
        }

        ... on Termination {
          id
          staffResourceUrl
        }
      }
    }
  }
`;

function getFormDefaultValues(closure) {
  return {
    closureType: closure.closureType,
    alBalanceDays: closure.alBalanceDays,
    alUnpaidTakenDays: closure.alUnpaidTakenDays,
    prevBasicSalary: closure.prevBasicSalary,
    prevBasicSalaryPayKind: closure.prevBasicSalaryPayKind,
    alInstruction: closure.alInstruction,
    bonuses: closure.bonuses,
    lastDayDate: formatDateForDatePicker(closure.lastDayDate),
    contractorReason: closure.contractorReason,
    effectiveFromDate: formatDateForDatePicker(closure.effectiveFromDate),
    specialRequest: closure.specialRequest ? "Yes" : "No",
    specialRequestType: closure.specialRequestType,
    specialRequestReason: closure.specialRequestReason,
    requestedLastDayDate: formatDateForDatePicker(closure.requestedLastDayDate),
    approvedLastDayDate: formatDateForDatePicker(
      closure.approvedLastDayDate ||
        closure.requestedLastDayDate ||
        closure.lastDayDate,
    ),
    clientReason: closure.clientReason,
    dispatchToClient: false,
    dispatchToContractor: false,
  };
}

export default function EditClosureForm({ closure }) {
  const router = useRouter();
  const progress = useProgressBar();
  const form = useForm({ defaultValues: getFormDefaultValues(closure) });
  const { setError } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: ({ record }) => {
      toast.success("Closure updated successfully!");
      router.push(record.staffResourceUrl);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    progress.start();

    const newInput = {
      ...input,
      specialRequest: input.specialRequest === "Yes",
    };

    startTransition(() => {
      execute({ id: closure.id, input: newInput });
      progress.done();
    });
  };

  return (
    <Form className="mb-10 space-y-4" form={form} onSubmit={onSubmit}>
      <RenderForm closure={closure} />

      <SubmissionStepsAlertCard closure={closure} />

      <DismissUpdateButton pending={pending} closure={closure} />
    </Form>
  );
}

function RenderForm({ closure }) {
  switch (closure.closureType) {
    case "End of Contract":
      return <EndOfContractForm closure={closure} />;
    case "Conversion":
      return <ConversionForm closure={closure} />;
    case "Resignation":
      return <ResignationForm closure={closure} />;
    case "Termination":
      return <TerminationForm closure={closure} />;
    default:
      return null;
  }
}
