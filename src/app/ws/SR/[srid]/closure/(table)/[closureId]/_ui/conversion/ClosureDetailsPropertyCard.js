"use client";

import Card from "@/ui/Card";
import EmploymentClosurePropertyList from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/conversion/EmploymentClosurePropertyList";
import QuotedRemark from "@/components/QuotedRemark";

export default function ClosureDetailsPropertyCard({ closure }) {
  return (
    <Card className="space-y-4">
      <EmploymentClosurePropertyList closure={closure} />

      <QuotedRemark title="Reason for conversion" contentClassName="text-sm">
        {closure.clientReason}
      </QuotedRemark>
    </Card>
  );
}
