"use client";

import { useFormContext, useWatch } from "react-hook-form";
import TextArea from "@/ui/TextArea";

export default function SpecialRequestReasonTextArea() {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const [specialRequest] = useWatch({
    name: ["specialRequest", "specialRequestType"],
  });
  const hasSpecialRequest = specialRequest === "Yes";

  return (
    <TextArea
      label="Reason of special request"
      placeholder="Provide any supporting details for the special request"
      {...register("specialRequestReason", {
        required: hasSpecialRequest && "Reason is required!",
      })}
      error={errors.specialRequestReason}
    />
  );
}
