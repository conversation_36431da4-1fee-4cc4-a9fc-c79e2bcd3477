"use client";

import { startTransition } from "react";
import { useProgressBar } from "@/ui/ProgressBar";
import { useForm } from "react-hook-form";
import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Form, formatDateForDatePicker, showFormErrors } from "@/ui/Form";
import ContractInformationPropertyCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/ContractInformationPropertyCard";
import EndOfContractForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/EndOfContractForm";
import ConversionForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/ConversionForm";
import ConfirmationCheckbox from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/ConfirmationCheckbox";
import ResignationForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/ResignationForm";
import TerminationForm from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/TerminationForm";
import AcknowledgeButton from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AcknowledgeButton";

const MUTATION = `
  mutation clientAcknowledgeClosure($employmentId: ID!, $id: ID!, $input: ClosureInput!) {
    execute:clientAcknowledgeClosure(employmentId: $employmentId, id: $id, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on EndOfContract {
          id
          staffResourceUrl
          clientResourceUrl
        }

        ... on Conversion {
          id
          staffResourceUrl
          clientResourceUrl
        }

        ... on Resignation {
          id
          staffResourceUrl
          clientResourceUrl
        }

        ... on Termination {
          id
          staffResourceUrl
          clientResourceUrl
        }
      }
    }
  }
`;

function getFormDefaultValues(closure) {
  return {
    alInstruction: closure.alInstruction,
    bonuses: closure.bonuses.map(({ chargeDescription, charges }) => ({
      chargeDescription,
      charges,
    })),
    approvedLastDayDate: closure.specialRequest
      ? formatDateForDatePicker(
          closure.approvedLastDayDate || closure.requestedLastDayDate,
        )
      : null,
    clientReason: "",
    confirmation: false,
  };
}

export default function AcknowledgeClosureForm({ closure }) {
  const { srid, closureId } = useParams();
  const progress = useProgressBar();
  const router = useRouter();
  const form = useForm({
    defaultValues: getFormDefaultValues(closure),
  });
  const { setError } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: ({ record }) => {
      toast.success("Closure acknowledged!");
      router.push(record.staffResourceUrl);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    progress.start();

    startTransition(() => {
      execute({
        id: closure.slug,
        employmentId: closure.employment.slug,
        input,
      });
      progress.done();
    });
  };

  return (
    <Form form={form} onSubmit={onSubmit} className="mb-10 space-y-4">
      <ContractInformationPropertyCard closure={closure} />

      <RenderForm closure={closure} />

      <ConfirmationCheckbox closure={closure} />

      <AcknowledgeButton closure={closure} pending={pending} />
    </Form>
  );
}

function RenderForm({ closure }) {
  switch (closure.closureType) {
    case "End of Contract":
      return <EndOfContractForm closure={closure} />;
    case "Conversion":
      return <ConversionForm closure={closure} />;
    case "Resignation":
      return <ResignationForm closure={closure} />;
    case "Termination":
      return <TerminationForm closure={closure} />;
    default:
      return null;
  }
}
