"use client";

import Card from "@/ui/Card";
import QuotedRemark from "@/components/QuotedRemark";
import ResignationDetailsPropertyList from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/resignation/ResignationDetailsPropertyList";
import SpecialRequestPropertyList from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/resignation/SpecialRequestPropertyList";

export default function ResignationDetailsPropertyCard({ closure }) {
  return (
    <Card className="space-y-4">
      <ResignationDetailsPropertyList closure={closure} />

      <QuotedRemark title="Reason for leaving" contentClassName="text-sm">
        {closure.contractorReason}
      </QuotedRemark>

      <SpecialRequestPropertyList closure={closure} />

      {closure.specialRequest && (
        <QuotedRemark title="Special request reason" contentClassName="text-sm">
          {closure.specialRequestReason}
        </QuotedRemark>
      )}
    </Card>
  );
}
