"use client";

import React from "react";
import { useFormContext } from "react-hook-form";
import { isEmpty } from "lodash";
import { FormBody, FormRow, FormSection } from "@/ui/Form";
import HStack from "@/ui/HStack";
import Card from "@/ui/Card";
import SalaryInput from "@/ui/SalaryInput";
import AlSalaryNoticePeriodInfo from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AlSalaryNoticePeriodInfo";
import AlInstructionSelect from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AlInstructionSelect";

export default function UponContractCompletionSectionCard({ closure }) {
  const {
    register,
    getValues,
    formState: { errors },
  } = useFormContext();
  const bonuses = getValues("bonuses");

  return (
    <Card className="space-y-4">
      <h1 className="text-lg font-bold">1. Upon Contract Completion</h1>

      <AlSalaryNoticePeriodInfo closure={closure} />

      <FormBody>
        <FormSection title="Annual Leave">
          <AlInstructionSelect />
        </FormSection>

        {!isEmpty(closure.bonuses) && (
          <FormSection title="Bonuses">
            <div className="inline-grid grid-cols-2 items-center-safe gap-4 gap-y-3 sm:grid-cols-[auto_minmax(100px,300px)]">
              {bonuses.map(({ chargeDescription }, index) => {
                return (
                  <React.Fragment key={`closure-bonus-${index}`}>
                    <label
                      htmlFor={`bonuses.${index}.charges`}
                      className="cursor-pointer leading-tight"
                    >
                      <HStack className="flex-nowrap items-start">
                        <p>{index + 1}.</p>

                        <p>{chargeDescription}</p>
                      </HStack>
                    </label>

                    <SalaryInput
                      labelId={`bonuses.${index}.charges`}
                      {...register(`bonuses.${index}.charges`, {
                        valueAsNumber: true,
                      })}
                      placeholder="Leave blank if undecided"
                      error={errors.bonuses?.[index]}
                    />
                  </React.Fragment>
                );
              })}
            </div>
          </FormSection>
        )}
      </FormBody>
    </Card>
  );
}
