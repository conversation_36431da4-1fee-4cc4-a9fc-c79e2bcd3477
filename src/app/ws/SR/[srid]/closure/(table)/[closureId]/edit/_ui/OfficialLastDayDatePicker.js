"use client";

import { useWatch } from "react-hook-form";
import { parseDateFromDatePicker } from "@/ui/Form";
import { isSameDay, parseISO } from "date-fns";
import { toSimpleDate } from "@/formatters/date";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";

export default function OfficialLastDayDatePicker({ closure }) {
  const { computedLastDayDate: derivedComputedLastDayDate, employment } =
    closure;
  const computedLastDayDate = parseISO(derivedComputedLastDayDate);
  const lastDayDate = parseDateFromDatePicker(
    useWatch({ name: "lastDayDate" }),
  );

  const isDifferent =
    lastDayDate && !isSameDay(computedLastDayDate, lastDayDate);

  return (
    <FutureDatePickerInput
      label="Official last day"
      name="lastDayDate"
      shortcuts={false}
      rules={{
        required: "Official last day is required!",
      }}
      maxDate={parseISO(employment.expiredDate)}
      inputProps={{
        helpText: isDifferent
          ? `Last day differs from the notice period end date (${toSimpleDate(computedLastDayDate)}).`
          : null,
      }}
      allowStartOfPreviousMonth
    />
  );
}
