"use client";

import { FormSection } from "@/ui/Form";
import BeforeUpdateAlertCard from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/BeforeUpdateAlertCard";
import UponContractCompletionSection from "@/app/ws/SR/[srid]/closure/new/_ui/UponContractCompletionSection";
import EmploymentClosureSection from "@/app/ws/SR/[srid]/closure/new/_ui/EmploymentClosureSection";

export default function EndOfContractForm({ closure }) {
  return (
    <FormSection>
      <BeforeUpdateAlertCard closure={closure} />

      <UponContractCompletionSection employment={closure.employment} />

      <EmploymentClosureSection />
    </FormSection>
  );
}
