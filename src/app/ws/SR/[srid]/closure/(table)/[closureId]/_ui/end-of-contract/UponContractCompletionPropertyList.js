import { currency } from "@/formatters/numeric";
import pluralize from "pluralize";
import isNotEmpty from "@/lib/isNotEmpty";
import { List, PropertyList } from "@/ui/PropertyList";
import { Na } from "@/ui/Typography";
import HStack from "@/ui/HStack";

export default function UponContractCompletionPropertyList({ closure }) {
  return (
    <PropertyList title="Upon Contract Completion">
      <List label="AL balance">
        {pluralize("day", closure.alBalanceDays, true)}
      </List>
      <List label="AL instruction">{closure.alInstruction}</List>
      <List label="AL unpaid">
        {pluralize("day", closure.alUnpaidTakenDays, true)}
      </List>
      <List.Currency
        label="Basic salary"
        suffix={closure.prevBasicSalaryPayKind}
      >
        {closure.prevBasicSalary}
      </List.Currency>
      {isNotEmpty(closure.bonuses) ? (
        <List label="Bonuses">
          {closure.bonuses.map(({ chargeDescription, charges }, index) => {
            return (
              <HStack
                className="justify-end-safe gap-x-4 gap-y-0"
                key={`bonus-${index}`}
              >
                <p>{chargeDescription}</p>

                <p className="font-semibold">
                  {charges ? currency(charges) : <Na />}
                </p>
              </HStack>
            );
          })}
        </List>
      ) : (
        <List label="Bonuses">
          <Na />
        </List>
      )}
    </PropertyList>
  );
}
