"use client";

import { useFormContext } from "react-hook-form";
import TextArea from "@/ui/TextArea";

export default function ReasonForLeavingTextArea() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <TextArea
      label="Reason for leaving"
      {...register("contractorReason", {
        required: "Reason for leaving is required!",
      })}
      error={errors.contractorReason}
    />
  );
}
