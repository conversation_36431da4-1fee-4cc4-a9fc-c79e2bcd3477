"use client";

import { useWatch } from "react-hook-form";
import { parseISO } from "date-fns";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";

export default function RequestedLastDayDatePicker({ closure }) {
  const { employment } = closure;
  const specialRequest = useWatch({ name: "specialRequest" });
  const hasSpecialRequest = specialRequest === "Yes";

  return (
    <FutureDatePickerInput
      label="Requested last day"
      name="requestedLastDayDate"
      shortcuts={false}
      rules={{
        required: hasSpecialRequest && "Requested last day is required!",
      }}
      maxDate={parseISO(employment.expiredDate)}
    />
  );
}
