"use client";

import { FormRow, FormSection } from "@/ui/Form";
import { useWatch } from "react-hook-form";
import Card from "@/ui/Card";
import SpecialRequestRadio from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/SpecialRequestRadio";
import SpecialRequestTypeSelect from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/SpecialRequestTypeSelect";
import RequestedLastDayDatePicker from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/RequestedLastDayDatePicker";
import SpecialRequestReasonTextArea from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/SpecialRequestReasonTextArea";
import ApprovedLastDayDatePicker from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/edit/_ui/ApprovedLastDayDatePicker";

export default function SpecialRequestSection({ closure }) {
  const specialRequest = useWatch({ name: "specialRequest" });
  const hasSpecialRequest = specialRequest === "Yes";

  return (
    <Card variant="transparent" className="bg-gray-50">
      <FormSection title="Special Request">
        <FormRow>
          <SpecialRequestRadio />
        </FormRow>

        {hasSpecialRequest && (
          <Card variant="transparent" className="rounded-md">
            <FormSection>
              <FormRow>
                <SpecialRequestTypeSelect />
                <RequestedLastDayDatePicker closure={closure} />
                <ApprovedLastDayDatePicker closure={closure} />
              </FormRow>

              <FormRow>
                <SpecialRequestReasonTextArea />
              </FormRow>
            </FormSection>
          </Card>
        )}
      </FormSection>
    </Card>
  );
}
