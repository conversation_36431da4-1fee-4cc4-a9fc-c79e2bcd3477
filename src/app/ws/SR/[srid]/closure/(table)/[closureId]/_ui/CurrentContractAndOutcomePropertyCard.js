"use client";

import { List, PropertyList } from "@/ui/PropertyList";
import Card from "@/ui/Card";
import RawHtml from "@/ui/RawHtml";
import ViewTerminationNoticeDialog from "@/app/ws/SR/[srid]/closure/_ui/ViewTerminationNoticeDialog";

export default function CurrentContractAndOutcomePropertyCard({ closure }) {
  return (
    <Card className="space-y-4">
      <PropertyList title="Current Contract">
        <List.Link
          label="Company"
          href={closure.employment.company.staffResourceUrl}
        >
          {closure.employment.company.fmId}: {closure.employment.company.name}
        </List.Link>
        <List.Link
          label="Project"
          href={closure.employment.project.staffResourceUrl}
        >
          {closure.employment.project.fmId}: {closure.employment.project.name}
        </List.Link>
        <List.Link label="ENT" href={closure.employment.ent?.staffResourceUrl}>
          {closure.employment.ent?.fmId}:{" "}
          <RawHtml>{closure.employment.ent?.description}</RawHtml>
        </List.Link>
        <List label="Period">{closure.employment.displayPeriod}</List>
        <List label="Termination">
          <ViewTerminationNoticeDialog
            employment={closure.employment}
            terminationSnapshot={closure.terminationSnapshot}
          />
        </List>
      </PropertyList>

      <PropertyList title="Closure Outcome">
        <List label="Dispatch mode">{closure.dispatchMode}</List>
        <List.Timestamp label="Submitted">{closure.createdAt}</List.Timestamp>
        <List.Status label="Status">{closure.status}</List.Status>
        <List.Timestamp label="Updated at">
          {closure.statusUpdatedAt}
        </List.Timestamp>
        <List.Timestamp label="CL agreed">
          {closure.clientAgreedAt}
        </List.Timestamp>
        <List.Timestamp label="CA agreed">
          {closure.contractorAgreedAt}
        </List.Timestamp>
        <List.Date label="LOE Dispatch">{closure.loeDispatchDate}</List.Date>
      </PropertyList>
    </Card>
  );
}
