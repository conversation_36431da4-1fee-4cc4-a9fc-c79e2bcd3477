import { toSimpleDateTime } from "@/formatters/date";
import AlertCard from "@/ui/AlertCard";

export default function BeforeUpdateAlertCard({ closure }) {
  const { clientAgreedAt, contractorAgreedAt, status } = closure;
  const hasClientAgreed =
    ["CONFIRMED", "AGREED"].includes(status) && clientAgreedAt !== null;
  const hasContractorAgreed =
    ["AGREED"].includes(status) && contractorAgreedAt !== null;

  return (
    <AlertCard variant="pause" ring={false} center>
      {hasClientAgreed && (
        <p>
          Client has agreed this closure at {toSimpleDateTime(clientAgreedAt)}.
        </p>
      )}

      {hasContractorAgreed && (
        <p>
          Contractor has agreed this closure at{" "}
          {toSimpleDateTime(contractorAgreedAt)}.
        </p>
      )}

      <p>
        Please ensure all employment records are accurate before proceeding.
      </p>
    </AlertCard>
  );
}
