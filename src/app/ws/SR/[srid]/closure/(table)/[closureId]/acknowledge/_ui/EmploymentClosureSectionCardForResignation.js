"use client";

import { useFormContext } from "react-hook-form";
import { FormRow, FormSection } from "@/ui/Form";
import Card from "@/ui/Card";
import AlSalaryNoticePeriodInfo from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AlSalaryNoticePeriodInfo";
import TextArea from "@/ui/TextArea";
import AlInstructionSelect from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/acknowledge/_ui/AlInstructionSelect";

export default function EmploymentClosureSectionCardForResignation({
  closure,
}) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <Card className="space-y-4">
      <h2 className="text-lg font-bold">2. Employment Closure</h2>

      <AlSalaryNoticePeriodInfo closure={closure} />

      <FormSection>
        <AlInstructionSelect />

        <FormRow>
          <TextArea
            label="Closure reason"
            helpText="The reason will not be shown to the contractor."
            {...register("clientReason")}
          />
        </FormRow>
      </FormSection>
    </Card>
  );
}
