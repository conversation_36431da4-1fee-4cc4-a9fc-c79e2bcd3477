"use client";

import { List, PropertyList } from "@/ui/PropertyList";
import AlertCard from "@/ui/AlertCard";

export default function SpecialRequestPropertyList({ closure }) {
  if (!closure.specialRequest)
    return (
      <PropertyList title="Special Request">
        <AlertCard variant="muted" ring={false} center>
          <p>No special request for this resignation.</p>
        </AlertCard>
      </PropertyList>
    );

  return (
    <PropertyList title="Special Request">
      <List label="Request for">{closure.specialRequestType}</List>
      <List.Date label="Requested last day">
        {closure.requestedLastDayDate}
      </List.Date>
      <List.Date label="Approved last day">
        {closure.approvedLastDayDate}
      </List.Date>
    </PropertyList>
  );
}
