"use client";

import Card from "@/ui/Card";
import QuotedRemark from "@/components/QuotedRemark";
import EmploymentClosurePropertyList from "@/app/ws/SR/[srid]/closure/(table)/[closureId]/_ui/resignation/EmploymentClosurePropertyList";

export default function EmploymentClosurePropertyCard({ closure }) {
  return (
    <Card className="space-y-4">
      <EmploymentClosurePropertyList closure={closure} />

      <QuotedRemark title="Closure reason" contentClassName="text-sm">
        {closure.clientReason}
      </QuotedRemark>
    </Card>
  );
}
