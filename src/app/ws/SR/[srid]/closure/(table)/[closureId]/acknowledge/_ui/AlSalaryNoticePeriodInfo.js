"use client";

import { currency } from "@/formatters/numeric";
import isNotEmpty from "@/lib/isNotEmpty";
import pluralize from "pluralize";
import { FormRow } from "@/ui/Form";
import { Na } from "@/ui/Typography";

export default function AlSalaryNoticePeriodInfo({ closure }) {
  const {
    alBalanceDays,
    alUnpaidTakenDays,
    prevBasicSalary,
    prevBasicSalaryPayKind,
    bonuses,
    closureType,
    employment,
  } = closure;

  return (
    <FormRow className="items-center-safe gap-y-1" colsCount={2}>
      <p className="text-sm text-muted">
        AL balance:{" "}
        <span className="font-medium text-blue-500">
          {pluralize("day", alBalanceDays, true)}
        </span>
      </p>
      {closureType === "End of Contract" && isNotEmpty(bonuses) && (
        <>
          <p className="text-sm text-muted">
            AL unpaid taken:{" "}
            <span className="font-medium text-blue-500">
              {pluralize("day", alUnpaidTakenDays, true)}
            </span>
          </p>
          <p className="text-sm text-muted">
            Current basic salary:{" "}
            <span className="font-medium text-blue-500">
              {prevBasicSalary ? (
                `${currency(prevBasicSalary)} ${prevBasicSalaryPayKind}`
              ) : (
                <Na />
              )}
            </span>
          </p>
        </>
      )}
      <p className="text-sm text-muted">
        Notice period:{" "}
        <span className="font-medium text-blue-500">
          {pluralize("day", employment.effectiveNoticeDays, true)}
        </span>
      </p>
    </FormRow>
  );
}
