"use client";

import { FormBody, FormSection } from "@/ui/Form";
import UponContractCompletionSection from "@/app/ws/SR/[srid]/renewals/_form2/UponContractCompletionSection";
import UponContractCompletionSectionPreview from "@/app/ws/SR/[srid]/renewals/_form2/UponContractCompletionSectionPreview";
import RenewalDetailsSection from "@/app/ws/SR/[srid]/renewals/_form2/RenewalDetailsSection";
import RenewalDetailsSectionPreview from "@/app/ws/SR/[srid]/renewals/_form2/RenewalDetailsSectionPreview";
import SubmitRenewalStepsAlertCard from "@/app/ws/SR/[srid]/renewals/_form2/SubmitRenewalStepsAlertCard";
import ConfirmationCheckbox from "@/app/ws/SR/[srid]/renewals/_form2/ConfirmationCheckbox";

export default function RenewalForm({
  employment,
  setPreview,
  preview = false,
}) {
  if (preview)
    return (
      <FormBody>
        <UponContractCompletionSectionPreview setPreview={setPreview} />
        <RenewalDetailsSectionPreview setPreview={setPreview} />

        <FormSection>
          <SubmitRenewalStepsAlertCard employment={employment} />
          <ConfirmationCheckbox employment={employment} />
        </FormSection>
      </FormBody>
    );

  return (
    <FormBody>
      <UponContractCompletionSection />
      <RenewalDetailsSection employment={employment} />
    </FormBody>
  );
}
