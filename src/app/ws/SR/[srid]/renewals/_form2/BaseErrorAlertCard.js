"use client";

import { useEffect, useRef } from "react";
import { isEmpty } from "lodash";
import pluralize from "pluralize";
import AlertCard from "@/ui/AlertCard";

export default function BaseErrorAlertCard({ baseError }) {
  const ref = useRef();

  useEffect(() => {
    if (ref.current) {
      // Scroll to the error card
      ref.current.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, [ref, baseError]);
  if (isEmpty(baseError)) return null;

  return (
    <div className="relative" onLoad={() => console.log("Loaded!")}>
      {/* A fake div for scrolling into view purpose when loaded */}
      <div
        ref={ref}
        className="invisible absolute -top-4 left-0 scroll-mt-[999px]"
      />

      <AlertCard variant="error" containerClassName="rounded-md">
        <p>
          Renewal offer submission was unsuccessful. Please review the following{" "}
          {pluralize("error", baseError.length)}:
        </p>
        <ul className="ml-8 list-outside list-disc">
          {baseError.map((error, index) => {
            return <li key={`base-error-${index}`}>{error.message}</li>;
          })}
        </ul>
      </AlertCard>
    </div>
  );
}
