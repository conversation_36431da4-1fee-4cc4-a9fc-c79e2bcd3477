"use client";

import { useFormContext } from "react-hook-form";
import { currency } from "@/formatters/numeric";
import { startCase } from "lodash";
import cn from "@/lib/cn";
import { FormRow, FormSection } from "@/ui/Form";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import Card from "@/ui/Card";
import RawHtml from "@/ui/RawHtml";
import PreviewValue from "@/components/FormPreview/PreviewValue";

export default function RenewalDetailsSectionPreview({ setPreview }) {
  return (
    <div data-form="section" className="space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2">
        <p className="text-lg font-bold">2.</p>
        <h2 className="text-lg font-bold">Renewal Details</h2>
        <p className="text-muted col-start-2 text-sm">
          Provide renewal details.
        </p>
      </div>

      <FormSection className="ml-9">
        <NewPeriod setPreview={setPreview} />
        <DesignationAndBasicSalary setPreview={setPreview} />
      </FormSection>

      <FormSection className="space-y-2">
        <BenefitItem name="Benefits" setPreview={setPreview} isFirst />
        <BenefitItem name="WorkingHours" setPreview={setPreview} />
        <BenefitItem name="Overtime" setPreview={setPreview} />
        <BenefitItem name="NoticePeriod" setPreview={setPreview} isLast />
      </FormSection>
    </div>
  );
}

function NewPeriod({ setPreview }) {
  const {
    watch,
    formState: { errors },
  } = useFormContext();
  const newContractPeriod = watch("newContractPeriod");
  const newStartDate = watch("newStartDate");
  const newEndDate = watch("newEndDate");

  return (
    <FormRow>
      <VStack className="gap-0">
        <p className="text-muted text-sm">How long would you like to renew?</p>
        <PreviewValue setPreview={setPreview} error={errors.newContractPeriod}>
          {newContractPeriod}
        </PreviewValue>
      </VStack>

      <FormRow className="grid-cols-2">
        <VStack className="gap-0">
          <p className="text-muted text-sm">New start date</p>
          <PreviewValue setPreview={setPreview} error={errors.newStartDate}>
            {newStartDate}
          </PreviewValue>
        </VStack>
        <VStack className="gap-0">
          <p className="text-muted text-sm">New end date</p>
          <PreviewValue setPreview={setPreview} error={errors.newEndDate}>
            {newEndDate}
          </PreviewValue>
        </VStack>
      </FormRow>
    </FormRow>
  );
}

function DesignationAndBasicSalary({ setPreview }) {
  const {
    watch,
    formState: { errors },
  } = useFormContext();
  const newDesignation = watch("newDesignation");
  const newBasicSalary = watch("newBasicSalary");
  const newBasicSalaryPayKind = watch("newBasicSalaryPayKind");

  return (
    <FormRow>
      <VStack className="gap-0">
        <p className="text-muted text-sm">New designation</p>
        <PreviewValue setPreview={setPreview} error={errors.newBasicSalary}>
          {newDesignation}
        </PreviewValue>
      </VStack>
      <VStack className="gap-0">
        <p className="text-muted text-sm">New basic salary</p>
        <PreviewValue
          setPreview={setPreview}
          error={errors.newBasicSalary || errors.newBasicSalaryPayKind}
        >
          {currency(newBasicSalary)} {newBasicSalaryPayKind}
        </PreviewValue>
      </VStack>
    </FormRow>
  );
}

function BenefitItem({ name, setPreview, isFirst = false, isLast = false }) {
  const {
    watch,
    formState: { errors },
  } = useFormContext();
  const label = startCase(name);
  const changeInstructionKey = `change${name}Instruction`;
  const newInstructionKey = `new${name}Instruction`;
  const changeInstruction = watch(changeInstructionKey);
  const newInstruction = watch(newInstructionKey);
  const hasChanges = changeInstruction === "Yes";

  return (
    <Card
      className={cn("space-y-4", {
        "rounded-none": !isFirst && !isLast,
        "rounded-b-none": isFirst,
        "rounded-t-none": isLast,
      })}
    >
      <h3 className="font-semibold">{label}</h3>

      <HStack className="gap-y-0">
        <p className="text-muted text-sm">Would you like to change it?</p>
        <PreviewValue
          setPreview={setPreview}
          error={errors[changeInstructionKey]}
        >
          {changeInstruction}
        </PreviewValue>
      </HStack>

      {hasChanges && (
        <PreviewValue
          as="div"
          size="md"
          error={errors[newInstructionKey]}
          disableEdit
        >
          <RawHtml className="whitespace-pre-line">{newInstruction}</RawHtml>
        </PreviewValue>
      )}
    </Card>
  );
}
