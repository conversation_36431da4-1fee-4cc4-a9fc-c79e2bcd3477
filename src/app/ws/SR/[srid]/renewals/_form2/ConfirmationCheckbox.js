"use client";

import { useFormContext } from "react-hook-form";
import Checkbox from "@/ui/Checkbox";
import JoblineCompanyName from "@/ui/JoblineCompanyName";

export default function ConfirmationCheckbox({ employment }) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <div className="leading-tight select-none">
      <Checkbox
        {...register("confirmation")}
        suffix={
          <span>
            I confirm that the details provided are accurate and hereby submit
            my official request for <JoblineCompanyName /> to proceed with the{" "}
            <span className="font-semibold">Contract Completion</span> and{" "}
            <span className="font-semibold">Renewal Details</span> to{" "}
            <span className="font-semibold text-blue-700">
              {employment.contractorName}
            </span>{" "}
            on its behalf.
          </span>
        }
        error={errors.confirmation}
      />
    </div>
  );
}
