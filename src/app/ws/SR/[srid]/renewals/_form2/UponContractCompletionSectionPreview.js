"use client";

import React from "react";
import { useFormContext } from "react-hook-form";
import { currency } from "@/formatters/numeric";
import { isEmpty } from "lodash";
import pluralize from "pluralize";
import { FormRow, FormSection } from "@/ui/Form";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import AlertCard from "@/ui/AlertCard";
import PreviewValue from "@/components/FormPreview/PreviewValue";

export default function UponContractCompletionSectionPreview({ setPreview }) {
  return (
    <div data-form="section" className="space-y-4">
      <div className="grid grid-cols-[16px_1fr] items-start gap-x-1 bg-gray-100 px-4 py-2">
        <p className="text-lg font-bold">1.</p>
        <h2 className="text-lg font-bold">Upon Contract Completion</h2>
        <p className="text-muted col-start-2 text-sm">
          Manage annual leave balance and provide bonus amount for the current
          contract, if applicable.
        </p>
      </div>

      <FormSection className="ml-9">
        <AlBalanceInstruction setPreview={setPreview} />
        <Bonuses setPreview={setPreview} />
      </FormSection>
    </div>
  );
}

function AlBalanceInstruction({ setPreview }) {
  const {
    watch,
    formState: { errors },
  } = useFormContext();
  const alBalanceDays = watch("alBalanceDays");
  const alInstruction = watch("alInstruction");
  const maxCarryForwardDays = watch("maxCarryForwardDays");
  const carryForwardValidMonths = watch("carryForwardValidMonths");
  const maxEncashDays = watch("maxEncashDays");

  if (alBalanceDays === 0)
    return (
      <FormSection>
        <p className="font-semibold">Annual Leave</p>
        <AlertCard
          variant="muted"
          containerClassName="mt-4"
          contentClassName="text-sm"
          iconSize={16}
          ring={false}
          center
        >
          No instruction needed as there is no AL balance.
        </AlertCard>
      </FormSection>
    );

  return (
    <FormSection>
      <p className="font-semibold">Annual Leave</p>
      <FormRow colsCount={3}>
        <VStack className="gap-0">
          <p className="text-muted text-sm">Instruction</p>
          <PreviewValue setPreview={setPreview} error={errors.alInstruction}>
            {alInstruction}
          </PreviewValue>
        </VStack>

        {alInstruction === "Carry forward" && (
          <>
            <VStack className="gap-0">
              <p className="text-muted text-sm">Days to carry forward</p>
              <PreviewValue
                setPreview={setPreview}
                error={errors.maxCarryForwardDays}
              >
                {pluralize("day", maxCarryForwardDays, true)}
              </PreviewValue>
            </VStack>

            <VStack className="gap-0">
              <p className="text-muted text-sm">To use within</p>
              <PreviewValue
                setPreview={setPreview}
                error={errors.carryForwardValidMonths}
              >
                {pluralize("month", carryForwardValidMonths, true)}
              </PreviewValue>
            </VStack>
          </>
        )}

        {alInstruction === "Encash" && (
          <VStack className="gap-0">
            <p className="text-muted text-sm">Days to encash</p>
            <PreviewValue setPreview={setPreview} error={errors.maxEncashDays}>
              {pluralize("day", maxEncashDays, true)}
            </PreviewValue>
          </VStack>
        )}
      </FormRow>
    </FormSection>
  );
}

function Bonuses({ setPreview }) {
  const {
    watch,
    formState: { errors },
  } = useFormContext();
  const bonuses = watch("bonuses");

  if (isEmpty(bonuses))
    return (
      <FormSection>
        <p className="font-semibold">Bonus</p>
        <AlertCard
          variant="muted"
          containerClassName="mt-4"
          contentClassName="text-sm"
          iconSize={16}
          ring={false}
          center
        >
          There are no bonuses as per current contract.
        </AlertCard>
      </FormSection>
    );

  return (
    <FormSection>
      <p className="font-semibold">Bonus</p>

      <div className="inline-grid grid-cols-2 items-center-safe gap-x-4 gap-y-2 sm:grid-cols-[auto_minmax(100px,300px)]">
        {bonuses.map(({ chargeDescription, charges }, index) => {
          return (
            <React.Fragment key={`renewal-bonus-${index}`}>
              <HStack className="text-muted items-start-safe flex-nowrap leading-tight">
                <p>{index + 1}.</p>

                <p>{chargeDescription}</p>
              </HStack>

              <PreviewValue
                setPreview={setPreview}
                error={errors.bonuses?.[index]}
              >
                {currency(charges)}
              </PreviewValue>
            </React.Fragment>
          );
        })}
      </div>
    </FormSection>
  );
}
