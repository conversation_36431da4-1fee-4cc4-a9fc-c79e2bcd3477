import AlertCard from "@/ui/AlertCard";

export default function SubmitRenewalStepsAlertCard({ employment }) {
  return (
    <AlertCard variant="pause" containerClassName="rounded-md mt-6 mx-1">
      <p>By submitting this renewal offer, we will:</p>

      <ul className="ml-8 list-outside list-disc">
        <li>
          Notify{" "}
          <span className="font-semibold">{employment.contractorName}</span> to
          respond to this renewal offer.
        </li>
        <li>
          Upon acceptance or decline, you will be notified and Jobline will
          process the renewal accordingly.
        </li>
        <li>
          If there are any changes required, before acceptance or decline, you
          may withdraw this renewal and submit a new one.
        </li>
      </ul>
    </AlertCard>
  );
}
