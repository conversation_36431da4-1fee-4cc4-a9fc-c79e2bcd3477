"use client";

import { useForm } from "react-hook-form";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { addDays, addYears, parseISO } from "date-fns";
import { isEmpty } from "lodash";
import { startTransition, useState } from "react";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Form, formatDateForDatePicker, showFormErrors } from "@/ui/Form";
import { ProgressBarLink, useProgressBar } from "@/ui/ProgressBar";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import RenewalDecision from "@/app/ws/SR/[srid]/renewals/_form2/RenewalDecision";
import RenewalForm from "@/app/ws/SR/[srid]/renewals/_form2/RenewalForm";
import BaseErrorAlertCard from "@/app/ws/SR/[srid]/renewals/_form2/BaseErrorAlertCard";

function getFormDefaultValues(employment) {
  const newStartDate = addDays(parseISO(employment.activeAgreement.endDate), 1);
  const newEndDate = addDays(addYears(newStartDate, 1), -1);

  return {
    alBalanceDays: employment.currentEntitlementPeriod?.alBalanceDays || 0,
    alUnpaidTakenDays:
      employment.currentEntitlementPeriod?.alUnpaidTakenDays || 0,
    alInstruction: "Clear",
    carryForwardValidMonths: 12,
    maxCarryForwardDays:
      employment.currentEntitlementPeriod?.alBalanceDays || 0,
    maxEncashDays: employment.currentEntitlementPeriod?.alBalanceDays || 0,
    bonuses: !isEmpty(employment.bonusesForRenewal)
      ? employment.bonusesForRenewal.map(
          ({ chargeDescription, amountAfterRate }) => ({
            chargeDescription,
            charges: amountAfterRate,
          }),
        )
      : [],
    newContractPeriod: "1 year",
    newStartDate: formatDateForDatePicker(newStartDate),
    newEndDate: formatDateForDatePicker(newEndDate),
    prevDesignation: employment.designation,
    newDesignation: employment.designation,
    prevBasicSalary: employment.currentBasicSalary?.amountAfterRate,
    prevBasicSalaryPayKind: employment.currentBasicSalary?.payKind,
    newBasicSalary: employment.currentBasicSalary?.amountAfterRate,
    newBasicSalaryPayKind: "Monthly",
    changeBenefitsInstruction: null,
    changeWorkingHoursInstruction: null,
    changeOvertimeInstruction: null,
    changeNoticePeriodInstruction: null,
    newBenefitsInstruction: null,
    newWorkingHoursInstruction: null,
    newOvertimeInstruction: null,
    newNoticePeriodInstruction: null,
    confirmation: false,
  };
}

const MUTATION = `
  mutation createRenewal($employmentId: ID!, $input: RenewalInput!) {
    execute: createRenewal(employmentId: $employmentId, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Renewal {
          id
          status
          staffResourceUrl
        }
      }
    }
  }
`;

export default function NewRenewalForm({ employment }) {
  const router = useRouter();
  const progress = useProgressBar();
  const { srid } = useParams();
  const [preview, setPreview] = useState(false);
  const [renewalDecision, setRenewalDecision] = useState(null);
  const [baseError, setBaseError] = useState(null);
  const form = useForm({ defaultValues: getFormDefaultValues(employment) });
  const {
    watch,
    setValue,
    setError,
    formState: { errors },
  } = form;
  const hasConfirmed = watch("confirmation");
  const hasFormErrors = !isEmpty(errors);

  const [execute, submitting] = useAction(boundAction(MUTATION), {
    onSuccess: ({ record }) => {
      setBaseError(null);
      toast.success("Renewal offer submitted!");
      // router.push(record.staffResourceUrl);
    },
    onError: ({ userErrors }) => {
      toast.error("Something went wrong!");
      showFormErrors({ userErrors, setError, setBaseError });
      // window.scrollTo({ top: -250, behavior: "smooth" });
    },
  });

  const onSubmit = (input) => {
    console.log(input);

    if (!preview) {
      setPreview(true);
      return;
    }

    if (!input.confirmation) {
      toast.error("Please check the confirmation!");
      setError("confirmation", { message: " " });
      return;
    }

    const {
      changeBenefitsInstruction: changeBenefitsInstructionPrimitive,
      changeWorkingHoursInstruction: changeWorkingHoursInstructionPrimitive,
      changeOvertimeInstruction: changeOvertimeInstructionPrimitive,
      changeNoticePeriodInstruction: changeNoticePeriodInstructionPrimitive,
    } = input;

    const changeBenefitsInstruction =
      changeBenefitsInstructionPrimitive === "Yes";
    const changeWorkingHoursInstruction =
      changeWorkingHoursInstructionPrimitive === "Yes";
    const changeOvertimeInstruction =
      changeOvertimeInstructionPrimitive === "Yes";
    const changeNoticePeriodInstruction =
      changeNoticePeriodInstructionPrimitive === "Yes";

    const newInput = {
      ...input,
      changeBenefitsInstruction,
      changeWorkingHoursInstruction,
      changeOvertimeInstruction,
      changeNoticePeriodInstruction,
    };

    progress.start();

    startTransition(() => {
      execute({ employmentId: srid, input: newInput });
      progress.done();
    });
  };

  return (
    <>
      <div className="mx-auto max-w-[800px] space-y-4">
        {renewalDecision === null ? (
          <RenewalDecision setRenewalDecision={setRenewalDecision} />
        ) : (
          <Form className="space-y-4" form={form} onSubmit={onSubmit}>
            <BaseErrorAlertCard baseError={baseError} />

            <RenewalForm
              employment={employment}
              preview={preview}
              setPreview={setPreview}
            />

            <HStack className="justify-end gap-4">
              <Button variant="ghost" className="mr-auto" disabled={submitting}>
                <ProgressBarLink href={`/ws/SR/${srid}/renewals`}>
                  Cancel
                </ProgressBarLink>
              </Button>

              {preview ? (
                <>
                  <Button
                    variant="secondary"
                    disabled={submitting}
                    onClick={() => {
                      setValue("confirmation", false, {
                        shouldValidate: false,
                        shouldTouch: true,
                        shouldDirty: true,
                      });
                      setPreview(false);
                    }}
                    outline
                  >
                    Back
                  </Button>
                  <Button
                    type="submit"
                    variant={!hasFormErrors ? "success" : "danger"}
                    loading={submitting}
                    disabled={!hasConfirmed}
                  >
                    {!hasFormErrors ? "Submit" : "Please review form"}
                  </Button>
                </>
              ) : (
                <Button
                  type="submit"
                  variant={!hasFormErrors ? "success" : "danger"}
                >
                  {!hasFormErrors ? "Next" : "Please review form"}
                </Button>
              )}
            </HStack>
          </Form>
        )}
      </div>
    </>
  );
}
