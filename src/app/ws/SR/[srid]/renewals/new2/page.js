import { notFound } from "next/navigation";
import apiQuery from "@/lib/apiQuery";
import cn from "@/lib/cn";
import isEligibleToRenewal from "@/lib/renewal/isEligibleToRenewal";
import AlertCard from "@/ui/AlertCard";
import NewRenewalForm from "@/app/ws/SR/[srid]/renewals/_form2/NewRenewalForm";

const QUERY = `
  query employment($id: ID!) {
    employment(id: $id) {
      id
      fmId
      contractorName
      designation
      status

      expiredDate
      lastDayDate

      currentBasicSalary {
        chargeType
        chargeDescription
        amountAfterRate
        payKind
      }

      bonusesForRenewal {
        chargeDescription
        amountAfterRate
      }

      currentEntitlementPeriod {
        alBalanceDays
        alUnpaidTakenDays
      }

      activeAgreement {
        fmId
        displayPeriod
        startDate
        endDate
      }
    }
  }
`;

export async function generateMetadata({ params }) {
  const { srid } = await params;

  const res = await apiQuery(QUERY, { id: srid });
  const employment = res.data.employment;

  return {
    title: `${employment.fmId}: ${employment?.contractorName} - New Renewal Offer`,
  };
}

export default async function Page({ params }) {
  const { srid } = await params;
  const res = await apiQuery(QUERY, { id: srid });
  const employment = res.data.employment;
  if (employment === null) notFound();

  const { eligible, errors } = isEligibleToRenewal(employment);

  if (!eligible)
    return (
      <AlertCard variant="error" containerClassName="max-w-[500px] mx-auto">
        <p>Unable to create renewal offer:</p>
        <ul
          className={cn("list-outside list-none", {
            "list-disc pl-8": errors.length > 1,
          })}
        >
          {errors.map((error, index) => {
            return <li key={`renewal-error-${index}`}>{error}</li>;
          })}
        </ul>
      </AlertCard>
    );

  return <NewRenewalForm employment={employment} />;
}
