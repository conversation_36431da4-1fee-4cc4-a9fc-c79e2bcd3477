"use client";

import { useQuery } from "urql";
import { isEmpty } from "lodash";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/ui/AlertDialog";
import { ProgressBarLink } from "@/ui/ProgressBar";
import { BadgeMinusIcon } from "lucide-react";
import Button from "@/ui/Button";
import Spinner from "@/ui/Spinner";
import AlertCard from "@/ui/AlertCard";

const QUERY = `
  query employment($id: ID!) {
    employment(id: $id) {
      id
      fmId
      contractorName
      designation
    }
  }
`;

export default function ProceedWithNonRenewalDialog({
  employmentId,
  trigger = null,
}) {
  const [res] = useQuery({
    query: QUERY,
    variables: { id: employmentId },
    pause: !employmentId,
  });

  const { fetching, data } = res;
  const employment = data?.employment;

  return (
    <AlertDialog>
      <AlertDialogTrigger>
        {trigger ? (
          trigger
        ) : (
          <Button
            variant="danger"
            size="sm"
            prefix={<BadgeMinusIcon size={20} strokeWidth={1.5} />}
            outline
          >
            Proceed with non-renewal
          </Button>
        )}
      </AlertDialogTrigger>

      <AlertDialogContent className="bg-pink-50 dark:bg-pink-100 dark:text-neutral-800">
        <AlertDialogHeader className="text-rose-700 dark:text-pink-500">
          Proceed with non-renewal?
        </AlertDialogHeader>

        <div className="space-y-4">
          {fetching ? (
            <Spinner />
          ) : isEmpty(employment) ? (
            <AlertCard variant="error" center>
              Employment not found.
            </AlertCard>
          ) : (
            <p>
              Are you sure you want to proceed with non-renewal for{" "}
              <span className="font-semibold text-rose-700 dark:text-pink-500">
                {employment.fmId}: {employment.contractorName}
              </span>
              ?
            </p>
          )}

          <AlertDialogFooter className="border-t-0 bg-transparent pt-0">
            <AlertDialogCancel>
              <Button
                variant="ghost"
                className="dark:text-neutral-800 dark:hover:enabled:bg-transparent"
                pill
                outline
              >
                Cancel
              </Button>
            </AlertDialogCancel>

            <ProgressBarLink
              href={`/ws/SR/${employmentId}/closure/end-of-contract/new`}
            >
              <Button
                variant="danger"
                disabled={fetching || isEmpty(employment)}
                pill
              >
                Yes, proceed closure
              </Button>
            </ProgressBarLink>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
