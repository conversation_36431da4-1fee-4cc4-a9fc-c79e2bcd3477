"use client";

import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import { useParams } from "next/navigation";
import { useQuery } from "urql";
import { useState } from "react";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import Pagination from "@/ui/Pagination";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";

const QUERY = `
  query approvers($employmentId: ID!, $sorts: [SortInput!], $page: Int!) {
    approvers(employmentId: $employmentId, sorts: $sorts, page: $page, status: "INACTIVE", perPage: 5) {
      nodes {
        id
        payrollModule
        level
        status
        createdAt
        updatedAt

        approver {
          id
          fmId
          fmIdName
          email
        }

        modifier {
          id
          name
        }
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "STAFF_INACTIVE_APPROVERS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Module",
    columnWidth: 110,
    propertyName: "payrollModule",
  },
  {
    columnName: "Approver",
    columnWidth: 300,
    propertyName: "approver.fmIdName",
  },
  {
    columnName: "Level",
    columnWidth: 70,
    propertyName: "level",
  },
  {
    columnName: "Modifier",
    columnWidth: 150,
    propertyName: "modifier.name",
  },
  {
    columnName: "Updated",
    columnWidth: 200,
    propertyName: "updatedAt",
    cellComponent: DateTimeCell,
  },
];

export default function DecommissionedApproversTable() {
  const { srid } = useParams();
  const [page, setPage] = useState(1);

  const [result] = useQuery({
    query: QUERY,
    variables: { employmentId: srid, page },
  });

  const { data, fetching } = result;

  const { records, totalPages, totalCount, processingTimeMs } =
    getTableMetadata(data?.approvers);

  if (records.length === 0) return null;

  return (
    <DataGrid.Root className="space-y-2">
      <h1 className="font-bold">Decommissioned Approvers</h1>

      <Pagination
        currentPage={page}
        totalPages={totalPages}
        onChange={(page) => setPage(page)}
        maxCells={5}
      />
      <DataGrid.TotalCountAndTime
        totalCount={totalCount}
        processingTimeMs={processingTimeMs}
      />

      <DataGrid.Content>
        <DataGridTable
          numRows={records.length}
          data={records}
          tableColumns={DEFAULT_TABLE_COLUMNS}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          fetching={fetching}
          emptyText={""}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
}
