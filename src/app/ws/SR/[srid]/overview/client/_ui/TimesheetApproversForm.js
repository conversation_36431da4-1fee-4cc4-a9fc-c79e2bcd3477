import HStack from "@/ui/HStack";
import { TimelineEvents } from "@blueprintjs/icons";
import AutocompleteCompanyMember from "@/components/autocomplete/AutocompleteCompanyMember";
import { isEmpty } from "lodash";
import { useWatch } from "react-hook-form";
import Input from "@/ui/Input";

export default function TimesheetApproversForm({ employment }) {
  const { project, approvers } = employment;
  const timesheetApprovers = approvers.find(
    (a) => a.payrollModule === "TIMESHEET",
  );
  const defaultTimesheetApprover1 = [
    timesheetApprovers.approver1?.approver,
  ].filter(Boolean);
  const defaultTimesheetApprover2 = [
    timesheetApprovers.approver2?.approver,
  ].filter(Boolean);
  const watchTimesheetApprover1 = useWatch({
    name: "timesheetApprover1",
    defaultValue: defaultTimesheetApprover1,
  });
  const watchTimesheetApprover2 = useWatch({
    name: "timesheetApprover2",
    defaultValue: defaultTimesheetApprover2,
  });

  if (!(project.timesheetModule || project.timesheetUploadModule)) {
    return (
      <>
        <div className="cursor-not-allowed self-center justify-self-end opacity-50">
          <HStack>
            <TimelineEvents
              size={20}
              className="text-sky-600 dark:text-sky-400"
            />
            Timesheet
          </HStack>
        </div>
        <Input readOnly disabled label="Timesheet approver no.1" />
        <Input readOnly disabled label="Timesheet approver no.2" />
      </>
    );
  }

  return (
    <>
      <div className="self-center justify-self-end">
        <HStack>
          <TimelineEvents
            size={20}
            className="text-sky-600 dark:text-sky-400"
          />
          Timesheet
        </HStack>
      </div>
      <div>
        <AutocompleteCompanyMember
          companyId={employment.companyId}
          name="timesheetApprover1"
          label="Timesheet approver no.1"
          defaultValue={defaultTimesheetApprover1}
          rules={
            isEmpty(watchTimesheetApprover1) &&
            !isEmpty(watchTimesheetApprover2)
              ? {
                  required: "Approver 1 is required since 2 is there!",
                }
              : undefined
          }
        />
      </div>
      <div>
        <AutocompleteCompanyMember
          companyId={employment.companyId}
          name="timesheetApprover2"
          label="Timesheet approver no.2"
          defaultValue={defaultTimesheetApprover2}
          disabled={isEmpty(watchTimesheetApprover1)}
        />
      </div>
    </>
  );
}
