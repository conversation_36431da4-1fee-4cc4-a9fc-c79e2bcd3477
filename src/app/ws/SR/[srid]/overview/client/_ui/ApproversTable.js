import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import Approver1Cell from "@/ui/DataGrid/cells/emp/Approver1Cell";
import Approver2Cell from "@/ui/DataGrid/cells/emp/Approver2Cell";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Module",
    columnWidth: 110,
    propertyName: "payrollModule",
  },
  {
    columnName: "1st Approver",
    columnWidth: 250,
    propertyName: "approver1.approver.fmIdName",
    cellComponent: Approver1Cell,
  },
  {
    columnName: "2nd Approver",
    columnWidth: 250,
    propertyName: "approver2.approver.fmIdName",
    cellComponent: Approver2Cell,
  },
];

export default function ApproversTable({ approvers }) {
  return (
    <DataGrid.Content>
      <DataGridTable
        numRows={approvers.length}
        data={approvers}
        tableColumns={DEFAULT_TABLE_COLUMNS}
        defaultTableColumns={DEFAULT_TABLE_COLUMNS}
      />
    </DataGrid.Content>
  );
}
