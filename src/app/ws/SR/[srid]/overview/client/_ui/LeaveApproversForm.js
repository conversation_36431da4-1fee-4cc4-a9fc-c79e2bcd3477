import HStack from "@/ui/HStack";
import { Mountain } from "@blueprintjs/icons";
import AutocompleteCompanyMember from "@/components/autocomplete/AutocompleteCompanyMember";
import { isEmpty } from "lodash";
import { useWatch } from "react-hook-form";
import Input from "@/ui/Input";

export default function LeaveApproversForm({ employment }) {
  const { project, approvers } = employment;
  const leaveApprovers = approvers.find((a) => a.payrollModule === "LEAVE");
  const defaultLeaveApprover1 = [leaveApprovers.approver1?.approver].filter(
    Boolean,
  );
  const defaultLeaveApprover2 = [leaveApprovers.approver2?.approver].filter(
    Boolean,
  );

  const watchLeaveApprover1 = useWatch({
    name: "leaveApprover1",
    defaultValue: defaultLeaveApprover1,
  });
  const watchLeaveApprover2 = useWatch({
    name: "leaveApprover2",
    defaultValue: defaultLeaveApprover2,
  });

  if (!project.leaveModule) {
    return (
      <>
        <div className="cursor-not-allowed self-center justify-self-end opacity-50">
          <HStack>
            <Mountain color="orange" size={20} />
            Leave
          </HStack>
        </div>
        <Input readOnly disabled label="Leave approver no.1" />
        <Input readOnly disabled label="Leave approver no.2" />
      </>
    );
  }

  return (
    <>
      <div className="self-center justify-self-end">
        <HStack>
          <Mountain color="orange" size={20} />
          Leave
        </HStack>
      </div>
      <div>
        <AutocompleteCompanyMember
          companyId={employment.companyId}
          name="leaveApprover1"
          label="Leave approver no.1"
          defaultValue={defaultLeaveApprover1}
          rules={
            isEmpty(watchLeaveApprover1) && !isEmpty(watchLeaveApprover2)
              ? {
                  required: "Approver 1 is required since 2 is there!",
                }
              : undefined
          }
        />
      </div>
      <div>
        <AutocompleteCompanyMember
          companyId={employment.companyId}
          name="leaveApprover2"
          label="Leave approver no.2"
          defaultValue={defaultLeaveApprover2}
          disabled={isEmpty(watchLeaveApprover1)}
        />
      </div>
    </>
  );
}
