import Card from "@/ui/Card";
import AssignApproversDialog from "@/app/ws/SR/[srid]/overview/client/_ui/AssignApproversDialog";
import HStack from "@/ui/HStack";
import ApproversTable from "@/app/ws/SR/[srid]/overview/client/_ui/ApproversTable";
import DecommissionedApproversTable from "@/app/ws/SR/[srid]/overview/client/_ui/DecommissionedApproversTable";

export default function ApproversCard({ employment }) {
  const { project, approvers } = employment;

  const chosenModules = [
    project.leaveModule ? "LA" : null,
    project.timesheetModule ? "TS" : null,
    project.claimModule ? "CS" : null,
    project.timesheetUploadModule ? "TSU" : null,
  ].filter(Boolean);

  return (
    <Card variant="transparent" className="mx-auto max-w-[800px] space-y-4">
      <HStack className="justify-between">
        <h1 className="font-bold">Approvers</h1>
        <AssignApproversDialog employmentId={employment.id} />
      </HStack>

      <HStack className="justify-center text-lg font-bold">
        <span className="text-base font-normal text-muted">
          Selected modules from{" "}
          <a
            href={project.staffResourceUrl}
            target="_blank"
            className="text-underline-link"
          >
            {project.fmId}
          </a>
          :{" "}
        </span>
        {chosenModules.join(", ")}
      </HStack>

      <ApproversTable approvers={approvers} />

      <DecommissionedApproversTable />
    </Card>
  );
}
