"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  Di<PERSON>Footer<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Trigger,
} from "@/ui/Dialog";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Button from "@/ui/Button";
import { Form, FormBody } from "@/ui/Form";
import { useForm } from "react-hook-form";
import { Edit } from "@blueprintjs/icons";
import Card from "@/ui/Card";
import { isEmpty } from "lodash";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import LeaveApproversForm from "@/app/ws/SR/[srid]/overview/client/_ui/LeaveApproversForm";
import TimesheetApproversForm from "@/app/ws/SR/[srid]/overview/client/_ui/TimesheetApproversForm";
import ClaimApproversForm from "@/app/ws/SR/[srid]/overview/client/_ui/ClaimApproversForm";
import { useQuery } from "urql";
import DelayedSpinner from "@/components/DelayedSpinner";

const QUERY = `
  query employment($id: ID!) {
    employment(id: $id) {
      id
      fmId
      companyId

      project {
        fmId
        fmIdName
        staffResourceUrl
        leaveModule
        timesheetModule
        timesheetUploadModule
        claimModule
      }

      approvers:activeApproversTable {
        payrollModule
        approver1 {
          approver {
            id
            fmId
            fmIdName
            email
          }

          creator {
            id
            name
          }

          modifier {
            id
            name
          }

          updatedAt
        }
        approver2 {
          approver {
            id
            fmId
            fmIdName
            email
          }

          creator {
            id
            name
          }

          modifier {
            id
            name
          }

          updatedAt
        }
      }
    }
  }
`;

const MUTATION = `
  mutation assignApprovers($employmentId: ID!, $inputs: [ApproverInput!]!) {
    execute:assignApprovers(employmentId: $employmentId, inputs: $inputs) {
      userErrors {
        path
        message
      }

      success
    }
  }
`;

export default function AssignApproversDialog({ employmentId }) {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const form = useForm();
  const { reset } = form;
  const router = useRouter();

  const [result] = useQuery({
    query: QUERY,
    variables: { id: employmentId },
    pause: !open,
  });

  const { data, fetching } = result;
  const employment = data?.employment;

  useEffect(() => {
    if (fetching) return;

    if (employment) {
      const leaveApprovers = employment.approvers.find(
        (a) => a.payrollModule === "LEAVE",
      );
      const timesheetApprovers = employment.approvers.find(
        (a) => a.payrollModule === "TIMESHEET",
      );
      const claimApprovers = employment.approvers.find(
        (a) => a.payrollModule === "CLAIM",
      );

      reset({
        leaveApprover1: [leaveApprovers?.approver1?.approver].filter(Boolean),
        leaveApprover2: [leaveApprovers?.approver2?.approver].filter(Boolean),
        timesheetApprover1: [timesheetApprovers?.approver1?.approver].filter(
          Boolean,
        ),
        timesheetApprover2: [timesheetApprovers?.approver2?.approver].filter(
          Boolean,
        ),
        claimApprover1: [claimApprovers?.approver1?.approver].filter(Boolean),
        claimApprover2: [claimApprovers?.approver2?.approver].filter(Boolean),
      });
    }
  }, [employment, fetching, reset]);

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      router.refresh();
      reset();
      setFormData(null);
      toast.success("Approvers assigned successfully");
      setOpen(false);
    },
  });

  const onSubmit = (input) => {
    const inputs = new Set();

    const {
      leaveApprover1,
      leaveApprover2,
      timesheetApprover1,
      timesheetApprover2,
      claimApprover1,
      claimApprover2,
    } = input;

    inputs.add(buildApprover("leaveApprover1", leaveApprover1));
    inputs.add(buildApprover("leaveApprover2", leaveApprover2));
    inputs.add(buildApprover("timesheetApprover1", timesheetApprover1));
    inputs.add(buildApprover("timesheetApprover2", timesheetApprover2));
    inputs.add(buildApprover("claimApprover1", claimApprover1));
    inputs.add(buildApprover("claimApprover2", claimApprover2));

    setFormData([...inputs].filter(Boolean));

    execute({
      employmentId: employment.id,
      inputs: [...inputs].filter(Boolean),
    });
  };

  function buildApprover(name, values = []) {
    if (values.length === 0) return null;
    const value = values[0];
    if (isEmpty(value)) return null;

    const [payrollModule, level] = name.split("Approver");

    return {
      payrollModule: payrollModule.toUpperCase(),
      level: parseInt(level, 10),
      approverId: value.id,
    };
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <Edit size={20} />
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>Assign approvers</DialogHeader>

        {fetching ? (
          <DelayedSpinner className="my-8" />
        ) : (
          <Form form={form} onSubmit={onSubmit} formData={formData}>
            <FormBody className="mx-auto max-w-[800px] py-4">
              <Card>
                <div className="grid grid-cols-[110px_1fr_1fr] gap-4">
                  {employment && (
                    <>
                      <LevelHeader />
                      <LeaveApproversForm employment={employment} />
                      <TimesheetApproversForm employment={employment} />
                      <ClaimApproversForm employment={employment} />
                    </>
                  )}
                </div>
              </Card>
            </FormBody>

            <DialogFooterWithCTA label="Assign" pending={pending} />
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}

const LevelHeader = () => {
  return (
    <>
      <div></div>
      <div className="text-center text-lg font-bold">
        1<sup>st</sup> Approver
      </div>
      <div className="text-center text-lg font-bold">
        2<sup>nd</sup> Approver
      </div>
    </>
  );
};
