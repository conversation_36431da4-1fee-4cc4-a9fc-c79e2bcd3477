import HStack from "@/ui/HStack";
import { DocumentShare } from "@blueprintjs/icons";
import AutocompleteCompanyMember from "@/components/autocomplete/AutocompleteCompanyMember";
import { isEmpty } from "lodash";
import { useWatch } from "react-hook-form";
import Input from "@/ui/Input";

export default function ClaimApproversForm({ employment }) {
  const { project, approvers } = employment;
  const claimApprovers = approvers.find((a) => a.payrollModule === "CLAIM");
  const defaultClaimApprover1 = [claimApprovers.approver1?.approver].filter(
    Boolean,
  );
  const defaultClaimApprover2 = [claimApprovers.approver2?.approver].filter(
    Boolean,
  );

  const watchClaimApprover1 = useWatch({
    name: "claimApprover1",
    defaultValue: defaultClaimApprover1,
  });
  const watchClaimApprover2 = useWatch({
    name: "claimApprover2",
    defaultValue: defaultClaimApprover2,
  });

  if (!project.claimModule) {
    return (
      <>
        <div className="cursor-not-allowed self-center justify-self-end opacity-50">
          <HStack>
            <DocumentShare
              size={20}
              className="text-green-600 dark:text-green-400"
            />
            Claim
          </HStack>
        </div>
        <Input readOnly disabled label="Claim approver no.1" />
        <Input readOnly disabled label="Claim approver no.2" />
      </>
    );
  }

  return (
    <>
      <div className="self-center justify-self-end">
        <HStack>
          <DocumentShare
            size={20}
            className="text-green-600 dark:text-green-400"
          />
          Claim
        </HStack>
      </div>
      <div>
        <AutocompleteCompanyMember
          companyId={employment.companyId}
          name="claimApprover1"
          label="Claim approver no.1"
          defaultValue={defaultClaimApprover1}
          rules={
            isEmpty(watchClaimApprover1) && !isEmpty(watchClaimApprover2)
              ? {
                  required: "Approver 1 is required since 2 is there!",
                }
              : undefined
          }
        />
      </div>
      <div>
        <AutocompleteCompanyMember
          companyId={employment.companyId}
          name="claimApprover2"
          label="Claim approver no.2"
          defaultValue={defaultClaimApprover2}
          disabled={isEmpty(watchClaimApprover1)}
        />
      </div>
    </>
  );
}
