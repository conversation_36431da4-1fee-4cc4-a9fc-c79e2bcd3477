"use client";

import { useState } from "react";
import { Accordion } from "@/ui/Accordion";
import { Item } from "@/components/accordion-variants/left-arrow-basic";
import AgreementResourcesClientTable from "@/app/ws/SR/[srid]/agreements/AgreementResourcesClientTable";
import HStack from "@/ui/HStack";
import { periodLength } from "@/formatters/date";
import SolidCaretRightIcon from "@/icons/SolidCaretRightIcon";

export default function AgreementAccordion({ agreement }) {
  const allAccordionValues = ["1"];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  return (
    <Accordion
      type="multiple"
      value={accordionValues}
      onValueChange={(values) => setAccordionValues(values)}
    >
      <Item
        title={<AgreementHeader agreement={agreement} />}
        value="1"
        borderless
        triggerIcon={
          <div className="accordion-arrow transition-transform duration-150">
            <SolidCaretRightIcon />
          </div>
        }
      >
        <AgreementResourcesClientTable
          resources={agreement.allAgreementResources}
        />
      </Item>
    </Accordion>
  );
}

const AgreementHeader = ({ agreement }) => {
  return (
    <HStack className="gap-4">
      {agreement.fmId}
      <HStack className="gap-1">
        <CalendarIcon />
        <span>
          {agreement.displayPeriod} (
          {periodLength(agreement.startDate, agreement.endDate)})
        </span>
      </HStack>
    </HStack>
  );
};

const CalendarIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="-0.5 -0.5 20 20"
    >
      <g fill="currentColor">
        <path d="M1.98 13.063a.396.396 0 0 1-.397-.396v-6.73a.396.396 0 0 1 .396-.395h10.688a.396.396 0 0 1 .396.396v1.638a5.684 5.684 0 0 1 1.583 0V2.375a.792.792 0 0 0-.792-.792h-1.979a.198.198 0 0 1-.198-.198V.594a.594.594 0 0 0-1.187 0v2.969a.594.594 0 0 1-1.188 0V1.979a.396.396 0 0 0-.396-.396H5.542a.198.198 0 0 1-.198-.198V.594a.594.594 0 0 0-1.188 0v2.969a.594.594 0 0 1-1.187 0V1.979a.396.396 0 0 0-.396-.396H.792A.792.792 0 0 0 0 2.375v10.688a1.583 1.583 0 0 0 1.583 1.583h5.993a5.684 5.684 0 0 1 0-1.583Z" />
        <path d="M13.854 8.708A5.146 5.146 0 1 0 19 13.854a5.154 5.154 0 0 0-5.146-5.146Zm0 8.709a3.563 3.563 0 1 1 3.563-3.563 3.57 3.57 0 0 1-3.563 3.563Z" />
        <path d="M15.438 13.26h-.99v-1.385a.594.594 0 0 0-1.188 0v1.98a.602.602 0 0 0 .594.593h1.584a.594.594 0 0 0 0-1.188Z" />
      </g>
    </svg>
  );
};
