"use client";

import Button from "@/ui/Button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import {
  ChevronDownIcon,
  DownloadIcon,
  PackageMinusIcon,
  PackageXIcon,
} from "lucide-react";

export default function RenewalActionMenu({ renewal }) {
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger>
          <Button
            variant="secondary"
            size="sm"
            suffix={<ChevronDownIcon size={20} />}
          >
            Actions
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end">
          <DropdownMenuItem>
            <PackageMinusIcon
              size={20}
              strokeWidth={1.5}
              className="stroke-red-500"
            />
            Withdraw
          </DropdownMenuItem>

          <DropdownMenuItem>
            <PackageXIcon
              size={20}
              strokeWidth={1.5}
              className="stroke-red-500"
            />
            Close
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem>
            <DownloadIcon size={20} strokeWidth={1.5} />
            Download PDF
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
