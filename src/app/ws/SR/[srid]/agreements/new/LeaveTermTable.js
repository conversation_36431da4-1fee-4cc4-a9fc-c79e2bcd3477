"use client";

import { useQuery } from "urql";
import queryResult from "@/lib/queryResult";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import DateCell from "@/ui/DataGrid/cells/DateCell";
import PeriodLengthCell from "@/ui/DataGrid/cells/PeriodLengthCell";
import DelayedSpinner from "@/components/DelayedSpinner";

const QUERY = `
  query allocatedLeaveTerms($startDate: DatePickerScalar!, $endDate: DatePickerScalar!) {
    allocatedLeaveTerms(startDate: $startDate, endDate: $endDate) {
      startDate
      endDate
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Start",
    columnWidth: 160,
    propertyName: "startDate",
    cellComponent: DateCell,
  },
  {
    columnName: "End",
    columnWidth: 160,
    propertyName: "endDate",
    cellComponent: DateCell,
  },
  {
    columnName: "Duration",
    columnWidth: 150,
    cellComponent: PeriodLengthCell,
  },
];

export default function LeaveTermTable({ startDate, endDate }) {
  const [result] = useQuery({
    query: QUERY,
    variables: {
      startDate,
      endDate,
    },
    pause: !startDate || !endDate,
  });

  const { records: leaveTerms, fetching } = queryResult(
    result,
    "allocatedLeaveTerms",
  );

  if (fetching) {
    return <DelayedSpinner size={20} />;
  }

  return (
    <DataGridTable
      numRows={leaveTerms.length}
      data={leaveTerms}
      tableColumns={DEFAULT_TABLE_COLUMNS}
      defaultTableColumns={DEFAULT_TABLE_COLUMNS}
      emptyText=""
    />
  );
}
