"use client";

import Button from "@/ui/Button";
import { Drawer, DrawerContent } from "@/ui/Drawer";
import HStack from "@/ui/HStack";
import Spinner from "@/ui/Spinner";
import { ChevronsRightIcon, RotateCwIcon } from "lucide-react";
import { useQuery } from "urql";
import RenewalActionMenu from "@/app/ws/SR/[srid]/agreements/RenewalActionMenu";
import { List, PropertyList } from "@/ui/PropertyList";
import pluralize from "pluralize";
import { isEmpty } from "lodash";
import { currency } from "@/formatters/numeric";
import RawHtml from "@/ui/RawHtml";
import VStack from "@/ui/VStack";

const QUERY = `
  query renewal($id: ID!) {
    renewal(id: $id) {
      id
      fmId

      alBalanceDays
      alInstruction
      carryForwardValidMonths
      maxCarryForwardDays
      maxEncashDays

      alUnpaidTakenDays
      bonuses {
        chargeDescription
        charges
      }

      prevDesignation
      prevBasicSalary
      prevBasicSalaryPayKind

      displayPeriod
      newContractPeriod
      newStartDate
      newEndDate
      newDesignation
      newBasicSalary
      newBasicSalaryPayKind

      changeBenefitsInstruction
      newBenefitsInstruction
      changeWorkingHoursInstruction
      newWorkingHoursInstruction
      changeOvertimeInstruction
      newOvertimeInstruction
      changeNoticePeriodInstruction
      newNoticePeriodInstruction

      status

      expirationScheduledAt

      staffResourceUrl

      employment {
        id
        fmId

        project {
          id
          fmId
          name
          staffResourceUrl
        }

        ent {
          id
          fmId
          description
          staffResourceUrl
        }
      }
    }
  }
`;

export default function RenewalDrawer({ resource, open, setOpen }) {
  const [result, refetch] = useQuery({
    query: QUERY,
    variables: { id: resource.id },
  });
  const { data, fetching } = result;
  const renewal = data?.renewal;

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent side="right" className="w-3/4 max-w-[800px] lg:w-1/2">
        <HStack className="border-b p-4">
          <Button
            variant="plainIcon"
            onClick={() => {
              setOpen(false); // For animation
            }}
          >
            <ChevronsRightIcon
              size="32"
              strokeWidth={1.5}
              className="stroke-gray-600"
            />
          </Button>

          <h1 className="text-lg font-bold">{renewal?.fmId}</h1>

          <Button
            variant="secondary"
            className="notion-dropdown-menu-shadow-light ml-auto border-none px-3"
            outline
            size="sm"
            onClick={() => refetch({ requestPolicy: "network-only" })}
            prefix={
              <RotateCwIcon
                size={24}
                strokeWidth={1.5}
                className={fetching ? "animate-spin" : ""}
              />
            }
          />
        </HStack>

        {fetching && !renewal && <Spinner />}

        <Renewal renewal={renewal} />
      </DrawerContent>
    </Drawer>
  );
}

const Renewal = ({ renewal }) => {
  if (!renewal) return null;

  return (
    <div className="space-y-4">
      <div className="space-y-4 p-4 pb-0">
        <HStack className="justify-center-safe">
          <RenewalActionMenu renewal={renewal} />
        </HStack>

        <PropertyList>
          <List label="REWID">{renewal.fmId}</List>
          <List
            label="Project"
            href={renewal.employment.project?.staffResourceUrl}
            target="_blank"
          >
            {renewal.employment.project &&
              `${renewal.employment.project.fmId}: ${renewal.employment.project.name}`}
          </List>
          <List
            label="ENT"
            href={renewal.employment.ent?.staffResourceUrl}
            target="_blank"
          >
            {renewal.employment.ent &&
              `${renewal.employment.ent.fmId}: ${renewal.employment.ent.description}`}
          </List>
          <List.Status label="Status">{renewal.status}</List.Status>
        </PropertyList>

        <PropertyList title="Upon Contract Completion" accordion>
          <List label="AL balance">
            {pluralize("day", renewal.alBalanceDays, true)}
          </List>
          <List label="AL instruction">
            {renewal.alInstruction || "No instruction"}
          </List>

          {renewal.alInstruction === "Carry forward" && (
            <>
              <List label="Days to forward">
                {pluralize("day", renewal.maxCarryForwardDays, true)}
              </List>
              <List label="To use within">
                {pluralize("month", renewal.carryForwardValidMonths, true)}
              </List>
            </>
          )}

          {renewal.alInstruction === "Encash" && (
            <List label="Days to encash">
              {pluralize("day", renewal.maxEncashDays, true)}
            </List>
          )}

          {!isEmpty(renewal.bonuses) && (
            <VStack className="mt-4">
              <p className="font-semibold">
                Bonuses ({renewal.bonuses.length})
              </p>
              <PropertyList>
                {renewal.bonuses.map(
                  ({ chargeDescription, charges }, index) => {
                    return (
                      <List
                        key={`renewal-bonus-${renewal.fmId}-${index}`}
                        label={chargeDescription}
                      >
                        {charges !== null ? currency(charges) : "TBA"}
                      </List>
                    );
                  },
                )}
              </PropertyList>
            </VStack>
          )}
        </PropertyList>

        <PropertyList title="Renewal Details" accordion>
          <List label="New period">{renewal.displayPeriod}</List>

          <List label="Designation">{renewal.newDesignation}</List>
          <List label="Basic salary">
            {currency(renewal.newBasicSalary)} {renewal.newBasicSalaryPayKind}
          </List>
          <List label="Benefits instruction">
            {renewal.changeBenefitsInstruction ? (
              <RawHtml className="line-clamp-2">
                {renewal.newBenefitsInstruction}
              </RawHtml>
            ) : (
              "No changes"
            )}
          </List>
          <List label="Working hours instruction">
            {renewal.changeWorkingHoursInstruction ? (
              <RawHtml className="line-clamp-2">
                {renewal.newWorkingHoursInstruction}
              </RawHtml>
            ) : (
              "No changes"
            )}
          </List>
          <List label="Overtime instruction">
            {renewal.changeOvertimeInstruction ? (
              <RawHtml className="line-clamp-2">
                {renewal.newOvertimeInstruction}
              </RawHtml>
            ) : (
              "No changes"
            )}
          </List>
          <List label="Notice period instruction">
            {renewal.changeNoticePeriodInstruction ? (
              <RawHtml className="line-clamp-2">
                {renewal.newNoticePeriodInstruction}
              </RawHtml>
            ) : (
              "No changes"
            )}
          </List>
        </PropertyList>
      </div>
    </div>
  );
};
