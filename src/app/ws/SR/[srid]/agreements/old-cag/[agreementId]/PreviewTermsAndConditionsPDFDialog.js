"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/ui/Dialog";
import dynamic from "next/dynamic";
import { useState } from "react";
import { formatDate } from "@/formatters/date";
import Button from "@/ui/Button";
import VStack from "@/ui/VStack";

// Prevent this error: DOMMatrix is not defined
// See: https://github.com/wojtekmaj/react-pdf/wiki/Upgrade-guide-from-version-9.x-to-10.x
const PdfViewer = dynamic(() => import("@/ui/PdfViewer"), {
  ssr: false,
});

export default function PreviewTermsAndConditionsPDFDialog({
  agreement,
  open,
  setOpen,
}) {
  const [previewLoading, setPreviewLoading] = useState(false);
  const [blob, setBlob] = useState(null);

  const handlePreview = () => {
    const url = `${process.env.NEXT_PUBLIC_JOBLINE_NEXT_API_URL}/preview_pdf/preview`;
    setPreviewLoading(true);

    fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        klass: "Emp::PreviewPdf::ActualCag",
        filename: `preview-cag-${agreement.id}.pdf`,
        agreementId: agreement.id,
      }),
      credentials: "include",
    })
      .then((response) => {
        if (response.ok) {
          response.blob().then((blob) => {
            setBlob(blob);
          });
        }
      })
      .finally(() => {
        setPreviewLoading(false);
      });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          Previewing CAG from: {formatDate(agreement.startDate)} &mdash;{" "}
          {formatDate(agreement.endDate)}
        </DialogHeader>

        <section className="space-y-4 py-4">
          <p>
            You can preview the terms and conditions (T&C) of this agreement
            before it is being dispatched.
          </p>

          <VStack className="items-center">
            <Button
              variant="secondary"
              type="submit"
              loading={previewLoading}
              onClick={handlePreview}
            >
              Preview
            </Button>

            {blob && <PdfViewer blob={blob} loading={previewLoading} />}
          </VStack>
        </section>

        <DialogFooter>
          <div className="flex w-full justify-end gap-3">
            <DialogDismissButton />
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
