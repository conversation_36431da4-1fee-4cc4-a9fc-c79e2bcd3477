"use client";

import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import { DataGrid } from "@/ui/DataGrid";
import { MagnifyingGlassIcon } from "@phosphor-icons/react/ssr";
import Input from "@/ui/Input";
import { useState } from "react";
import { matchSorter } from "match-sorter";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "SRID",
    columnWidth: 100,
    propertyName: "employment.fmId",
    visible: true,
  },
  {
    columnName: "Name",
    columnWidth: 200,
    propertyName: "employment.contractorName",
    removable: false,
    visible: true,
    href: "/ws/companies/${params.companyId}/billing/purchase-orders/${params.purchaseOrderId}/SR/${row.employment.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Status",
    columnWidth: 100,
    propertyName: "employment.status",
    visible: true,
  },
  {
    columnName: "PTID",
    columnWidth: 100,
    propertyName: "employment.project.fmId",
    visible: true,
  },
  {
    columnName: "Since",
    columnWidth: 200,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default function PurchaseOrderEmploymentsTable({ data }) {
  const [filter, setFilter] = useState("");

  // Performance issues, not optimal since it re-renders on every keystroke
  const filteredData = matchSorter(data, filter, {
    keys: [
      { threshold: matchSorter.rankings.CONTAINS, key: "employment.fmId" },
      {
        threshold: matchSorter.rankings.CONTAINS,
        key: "employment.contractorName",
      },
      {
        threshold: matchSorter.rankings.CONTAINS,
        key: "employment.project.fmId",
      },
    ],
  });

  return (
    <div className="space-y-2 overflow-x-scroll">
      <Input
        wrapperClassName="w-1/2 rounded-full focus-within:ring-0 focus-within:ring-offset-0"
        size="sm"
        placeholder="Filter SRID"
        prefix={<MagnifyingGlassIcon size={16} weight="bold" />}
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
      />

      <DataGrid.Content>
        <DataGridTable
          data={filteredData}
          numRows={filteredData.length}
          tableColumns={DEFAULT_TABLE_COLUMNS}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </div>
  );
}
