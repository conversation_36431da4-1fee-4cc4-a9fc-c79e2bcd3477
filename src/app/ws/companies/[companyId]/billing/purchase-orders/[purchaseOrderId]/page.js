"use client";

import { useParams, useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Page() {
  const router = useRouter();
  const { companyId, purchaseOrderId } = useParams();

  useEffect(() => {
    router.push(
      `/ws/companies/${companyId}/billing/purchase-orders/${purchaseOrderId}/SR`,
    );
  }, [companyId, purchaseOrderId, router]);

  return null;
}
