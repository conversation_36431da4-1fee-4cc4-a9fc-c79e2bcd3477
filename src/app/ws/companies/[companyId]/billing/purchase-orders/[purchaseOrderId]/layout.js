import HStack from "@/ui/HStack";
import { ProgressBarLink } from "@/ui/ProgressBar";
import { ChevronLeftIcon } from "lucide-react";
import Card from "@/ui/Card";
import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import { List, PropertyList } from "@/ui/PropertyList";
import CreatorModifierVStack from "@/components/CreatorModifierVStack";
import PurchaseOrderSegmentedControl from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/_ui/PurchaseOrderSegmentedControl";
import EditPurchaseOrderDialog from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/_ui/EditPurchaseOrderDialog";
import AiChatPurchaseOrderDrawer from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/_ui/AiChatPurchaseOrderDrawer";
import Page from "@/app/(examples)/examples/chat-examples/page.js";

const QUERY = `
  query purchaseOrder($purchaseOrderId: ID!) {
    purchaseOrder(purchaseOrderId: $purchaseOrderId) {
      id
      fmId
      purchaseOrderNumber
      startDate
      expiredDate
      kind
      period
      duration
      status

      createdAt
      updatedAt

      creator {
        id
        name
      }
      modifier {
        id
        name
      }
    }
  }
`;

export default async function Layout({ params, children }) {
  const { companyId, purchaseOrderId } = await params;
  const res = await apiQuery(QUERY, { purchaseOrderId });
  const purchaseOrder = res.data.purchaseOrder;

  if (purchaseOrder === null) notFound();

  const purchaseOrdersUrl = `/ws/companies/${companyId}/billing/purchase-orders`;

  return (
    <div className="space-y-2 py-4">
      <div className="space-y-2 px-4">
        <HStack>
          <HStack className="text-xl font-bold" asChild>
            <h1>
              <ProgressBarLink href={purchaseOrdersUrl}>
                <ChevronLeftIcon />
              </ProgressBarLink>
              {purchaseOrder.fmId}
            </h1>
          </HStack>

          <EditPurchaseOrderDialog purchaseOrderId={purchaseOrder.id} />
          <AiChatPurchaseOrderDrawer purchaseOrderId={purchaseOrder.id} />
        </HStack>

        <Card className="mx-auto mt-4 max-w-[800px] space-y-2 border-gray-200 bg-gradient-to-bl from-sky-50 via-white via-70% to-amber-50 p-2">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-10">
            <PropertyList>
              <List.Status label="Status">{purchaseOrder.status}</List.Status>
              <List label="POID">{purchaseOrder.fmId}</List>
              <List label="PO No.">{purchaseOrder.purchaseOrderNumber}</List>
            </PropertyList>

            <PropertyList>
              <List.Date label="Start date">
                {purchaseOrder.startDate}
              </List.Date>
              <List.Date label="Expired date">
                {purchaseOrder.expiredDate}
              </List.Date>
              <List label="Duration">{purchaseOrder.duration}</List>
            </PropertyList>
          </div>
          <CreatorModifierVStack resource={purchaseOrder} />
        </Card>

        <div className="text-center">
          <PurchaseOrderSegmentedControl />
        </div>
      </div>

      {children}

      {/*<div className="border-t p-4">*/}
      {/*  <h1 className="text-lg font-bold">Your chat history with AI</h1>*/}
      {/*  <Page />*/}
      {/*</div>*/}
    </div>
  );
}
