import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import {
  Form,
  FormBody,
  FormRow,
  FormSection,
  formatDateForDatePicker,
  showFormErrors,
} from "@/ui/Form";
import Input from "@/ui/Input";
import { DialogFooterWithCTA } from "@/ui/Dialog";
import DatePickerInput from "@/ui/datetime/DatePickerInput";
import { sub } from "date-fns";
import Select from "@/ui/Select";
import AmountInput from "@/components/input/AmountInput";
import TiptapEditor from "@/ui/editors/TiptapEditor";

const MUTATION = `
  mutation updatePurchaseOrder($purchaseOrderId: ID!, $input: PurchaseOrderInput!) {
    execute:updatePurchaseOrder(purchaseOrderId: $purchaseOrderId, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on PurchaseOrder {
          id
        }
      }
    }
  }
`;

export default function EditPurchaseOrderForm({ purchaseOrder, setOpen }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      purchaseOrderNumber: purchaseOrder.purchaseOrderNumber,
      startDate: formatDateForDatePicker(purchaseOrder.startDate),
      expiredDate: formatDateForDatePicker(purchaseOrder.expiredDate),
      kind: purchaseOrder.kind,
      period: purchaseOrder.period,
      budgetedAmount: purchaseOrder.budgetedAmount,
      remark: purchaseOrder.remark,
    },
  });
  const {
    register,
    setError,
    formState: { errors },
  } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Purchase order updated successfully");
      router.refresh();
      setOpen(false);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);
    execute({ purchaseOrderId: purchaseOrder.id, input });
  };

  return (
    <Form form={form} onSubmit={onSubmit} formData={formData}>
      <FormBody className="mx-auto max-w-[800px] py-4">
        <FormSection>
          <FormRow>
            <Input
              label="PO number"
              {...register("purchaseOrderNumber", {
                required: "Purchase order number is required!",
              })}
              error={errors.purchaseOrderNumber}
            />
          </FormRow>
        </FormSection>

        <FormSection title="Duration and period">
          <FormRow>
            <DatePickerInput
              label="Start date"
              name="startDate"
              rules={{ required: "Start date is required!" }}
              shortcuts={false}
              minDate={sub(new Date(), { years: 2 })}
            />

            <DatePickerInput
              label="Expired date"
              name="expiredDate"
              rules={{ required: "Expired date is required!" }}
              shortcuts={false}
              minDate={sub(new Date(), { years: 2 })}
            />
          </FormRow>

          <FormRow>
            <Select label="Kind" {...register("kind")}>
              <option value=""></option>
              <option value="Day">Day</option>
              <option value="Month">Month</option>
            </Select>

            <Input label="Period" {...register("period")} />
          </FormRow>
        </FormSection>

        <FormSection>
          <FormRow>
            <AmountInput
              label="Budgeted amount"
              {...register("budgetedAmount", {
                required: "Budgeted amount is required!",
              })}
              error={errors.budgetedAmount}
            />
          </FormRow>
        </FormSection>

        <FormSection title="Remark">
          <TiptapEditor
            name="remark"
            label="Remark"
            defaultValue={purchaseOrder.remark}
          />
        </FormSection>
      </FormBody>

      <DialogFooterWithCTA label="Update" pending={pending} />
    </Form>
  );
}
