import VStack from "@/ui/VStack";
import { currency } from "@/formatters/numeric";
import HStack from "@/ui/HStack";

export default function BudgetAndBalance({ purchaseOrder }) {
  return (
    <HStack className="gap-3">
      <VStack className="relative gap-0">
        <span className="absolute top-[-20px] right-0 text-xs">Budget</span>
        <span className="text-2xl font-bold">
          {currency(purchaseOrder.budgetedAmount)}
        </span>
      </VStack>
      <strong>-</strong>
      <VStack className="relative gap-0">
        <span className="absolute top-[-20px] left-0 text-xs">Spend</span>
        <span className="text-muted text-2xl">
          {currency(purchaseOrder.usedAmount)}
        </span>
      </VStack>
      <strong>=</strong>
      <VStack className="relative gap-0">
        <span className="absolute top-[-20px] left-0 text-xs">Balance (%)</span>
        <span className="text-2xl font-bold text-green-700">
          {currency(purchaseOrder.balancedAmount)} (
          {purchaseOrder.balancedPercentage}%)
        </span>
      </VStack>
    </HStack>
  );
}
