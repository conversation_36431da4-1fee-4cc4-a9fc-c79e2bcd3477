"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTrigger,
} from "@/ui/Dialog";
import { useState } from "react";
import Button from "@/ui/Button";
import { Edit } from "@blueprintjs/icons";
import { useQuery } from "urql";
import EditPurchaseOrderForm from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/_ui/EditPurchaseOrderForm";
import DelayedSpinner from "@/components/DelayedSpinner";

const QUERY = `
  query purchaseOrder($purchaseOrderId: ID!) {
    purchaseOrder(purchaseOrderId: $purchaseOrderId) {
      id
      fmId
      purchaseOrderNumber
      startDate
      expiredDate
      kind
      period
      status
      budgetedAmount
      remark
    }
  }
`;

export default function EditPurchaseOrderDialog({ purchaseOrderId }) {
  const [open, setOpen] = useState(false);

  const [result] = useQuery({
    query: QUERY,
    variables: { purchaseOrderId },
    pause: !open,
  });
  const { data, fetching } = result;
  const purchaseOrder = data?.purchaseOrder;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <Edit size={20} />
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>Edit {purchaseOrder?.fmId}</DialogHeader>

        {fetching && !purchaseOrder ? (
          <DelayedSpinner className="my-8" />
        ) : (
          <EditPurchaseOrderForm
            purchaseOrder={purchaseOrder}
            setOpen={setOpen}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
