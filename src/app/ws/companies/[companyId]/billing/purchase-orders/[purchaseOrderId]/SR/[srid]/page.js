import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import { FormatDateToggle } from "@/formatters/date";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import AddTestInvoiceDialog from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/SR/[srid]/_ui/AddTestInvoiceDialog";
import PurchaseOrderInvoicesTable from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/SR/[srid]/_ui/PurchaseOrderInvoicesTable";

const QUERY = `
  query purchaseOrderEmployment($purchaseOrderId: ID!, $employmentId: ID!) {
    purchaseOrderEmployment(purchaseOrderId: $purchaseOrderId, employmentId: $employmentId) {
      id
      createdAt

      creator {
        id
        name
      }

      employment {
        id
        fmId
        name
        contractorName
        status
        project {
          id
          fmId
        }
      }

      invoices {
        id
        fmId
        payableAmount
        workMonth
        status
      }
    }
  }
`;

export default async function Page({ params }) {
  const { purchaseOrderId, srid } = await params;
  const res = await apiQuery(QUERY, { purchaseOrderId, employmentId: srid });
  const purchaseOrderEmployment = res.data.purchaseOrderEmployment;

  if (purchaseOrderEmployment === null) notFound();

  const employment = purchaseOrderEmployment.employment;
  const invoices = purchaseOrderEmployment.invoices;

  return (
    <div className="grid grid-rows-[48px_1fr] gap-4">
      <HStack className="gap-4">
        <VStack className="gap-0">
          <h1 className="text-xl font-bold">
            {employment.fmId}: {employment.contractorName}
          </h1>
          <p className="text-muted text-sm">
            Added by {purchaseOrderEmployment.creator.name} since{" "}
            <FormatDateToggle date={purchaseOrderEmployment.createdAt} />
          </p>
        </VStack>

        <AddTestInvoiceDialog
          purchaseOrderEmployment={purchaseOrderEmployment}
        />
      </HStack>

      <PurchaseOrderInvoicesTable data={invoices} />
    </div>
  );
}
