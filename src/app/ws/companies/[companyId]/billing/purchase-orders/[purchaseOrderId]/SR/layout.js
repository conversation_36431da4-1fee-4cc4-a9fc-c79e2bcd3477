import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import { currency } from "@/formatters/numeric";
import { CirclePlusIcon } from "lucide-react";
import Button from "@/ui/Button";
import Input from "@/ui/Input";
import { MagnifyingGlassIcon } from "@phosphor-icons/react/dist/ssr";
import PurchaseOrderEmploymentsTable from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/SR/_ui/PurchaseOrderEmploymentsTable";
import AddEmploymentsToPurchaseOrderDialog from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/SR/_ui/AddEmploymentsToPurchaseOrderDialog";
import BudgetAndBalance from "@/app/ws/companies/[companyId]/billing/purchase-orders/[purchaseOrderId]/SR/_ui/BudgetAndBalance";

const QUERY = `
  query purchaseOrder($purchaseOrderId: ID!) {
    purchaseOrder(purchaseOrderId: $purchaseOrderId) {
      id
      fmId
      companyId
      budgetedAmount
      usedAmount
      balancedAmount
      balancedPercentage
    }

    purchaseOrderEmployments(purchaseOrderId: $purchaseOrderId) {
      nodes {
        id
        createdAt

        employment {
          id
          fmId
          name
          contractorName
          status
          project {
            id
            fmId
          }
        }
      }
    }
  }
`;

export default async function Layout({ params, children }) {
  const { purchaseOrderId } = await params;
  const res = await apiQuery(QUERY, { purchaseOrderId });
  const purchaseOrder = res.data.purchaseOrder;
  const purchaseOrderEmployments = res.data.purchaseOrderEmployments.nodes;

  if (purchaseOrder === null) notFound();

  return (
    <div className="space-y-4 border-t p-4">
      <div className="pt-4">
        <BudgetAndBalance purchaseOrder={purchaseOrder} />
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="grid grid-rows-[48px_1fr] gap-4">
          <div className="mx-auto content-end-safe">
            <AddEmploymentsToPurchaseOrderDialog
              purchaseOrder={purchaseOrder}
            />
          </div>

          <PurchaseOrderEmploymentsTable data={purchaseOrderEmployments} />
        </div>

        {children}
      </div>
    </div>
  );
}
