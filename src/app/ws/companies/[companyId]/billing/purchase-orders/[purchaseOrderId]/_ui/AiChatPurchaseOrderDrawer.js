import { Drawer, DrawerContent, DrawerTrigger } from "@/ui/Drawer";
import Button from "@/ui/Button";
import Page from "@/app/(examples)/examples/chat-examples/page.js";

export default function AiChatPurchaseOrderDrawer({ purchaseOrderId }) {
  return (
    <Drawer>
      <DrawerTrigger>
        <Button
          variant="plainIcon"
          prefix={<Bot2Icon className="stroke-sky-700/50" />}
        />
      </DrawerTrigger>
      <DrawerContent side="bottom" className="h-1/2">
        <Page />
      </DrawerContent>
    </Drawer>
  );
}

const Bot2Icon = ({ className }) => {
  return (
    <svg
      viewBox="0 0 20 20"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      height={20}
      width={20}
    >
      <path
        d="M11.25 1.6666666666666667c0 0.3702083333333333 -0.16091666666666668 0.702825 -0.4166666666666667 0.9317083333333334V4.166666666666667h4.166666666666667c1.3807500000000001 0 2.5 1.1192916666666668 2.5 2.5v8.333333333333334c0 1.3807500000000001 -1.11925 2.5 -2.5 2.5H5c-1.3807083333333334 0 -2.5 -1.11925 -2.5 -2.5V6.666666666666667c0 -1.3807083333333334 1.1192916666666668 -2.5 2.5 -2.5h4.166666666666667V2.5983750000000003c-0.25575000000000003 -0.22888333333333336 -0.4166666666666667 -0.5615 -0.4166666666666667 -0.9317083333333334 0 -0.6903583333333334 0.5596666666666666 -1.25 1.25 -1.25s1.25 0.5596416666666667 1.25 1.25ZM5 5.833333333333334c-0.4602333333333333 0 -0.8333333333333334 0.37310000000000004 -0.8333333333333334 0.8333333333333334v8.333333333333334c0 0.46025000000000005 0.37310000000000004 0.8333333333333334 0.8333333333333334 0.8333333333333334h10c0.46025000000000005 0 0.8333333333333334 -0.3730833333333333 0.8333333333333334 -0.8333333333333334V6.666666666666667c0 -0.4602333333333333 -0.3730833333333333 -0.8333333333333334 -0.8333333333333334 -0.8333333333333334H5Zm-3.3333333333333335 2.5H0v5h1.6666666666666667v-5Zm16.666666666666668 0h1.6666666666666667v5h-1.6666666666666667v-5ZM7.5 12.083333333333334c0.6903583333333334 0 1.25 -0.5596666666666666 1.25 -1.25s-0.5596416666666667 -1.25 -1.25 -1.25 -1.25 0.5596666666666666 -1.25 1.25 0.5596416666666667 1.25 1.25 1.25Zm5 0c0.6903333333333334 0 1.25 -0.5596666666666666 1.25 -1.25s-0.5596666666666666 -1.25 -1.25 -1.25 -1.25 0.5596666666666666 -1.25 1.25 0.5596666666666666 1.25 1.25 1.25Z"
        strokeWidth={0.8333}
      />
    </svg>
  );
};
