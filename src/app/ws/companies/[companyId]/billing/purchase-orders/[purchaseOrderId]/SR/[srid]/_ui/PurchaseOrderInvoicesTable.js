"use client";

import DataGridTable from "@/ui/DataGrid/DataGridTable";
import { DataGrid } from "@/ui/DataGrid";
import { MagnifyingGlassIcon } from "@phosphor-icons/react/ssr";
import Input from "@/ui/Input";
import { useState } from "react";
import { matchSorter } from "match-sorter";
import CurrencyCell from "@/ui/DataGrid/cells/CurrencyCell";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "INID",
    columnWidth: 120,
    propertyName: "fmId",
    visible: true,
  },
  {
    columnName: "Payable",
    columnWidth: 100,
    propertyName: "payableAmount",
    visible: true,
    cellComponent: CurrencyCell,
  },
  {
    columnName: "Work Month",
    columnWidth: 120,
    propertyName: "workMonth",
    visible: true,
  },
  {
    columnName: "Status",
    columnWidth: 120,
    propertyName: "status",
    visible: true,
  },
];

export default function PurchaseOrderInvoicesTable({ data }) {
  const [filter, setFilter] = useState("");

  // Performance issues, not optimal since it re-renders on every keystroke
  const filteredData = matchSorter(data, filter, {
    keys: [
      { threshold: matchSorter.rankings.CONTAINS, key: "fmId" },
      {
        threshold: matchSorter.rankings.CONTAINS,
        key: "workMonth",
      },
    ],
  });

  return (
    <div className="space-y-2 overflow-x-scroll">
      <Input
        wrapperClassName="w-1/2 rounded-full focus-within:ring-0 focus-within:ring-offset-0"
        size="sm"
        placeholder="Filter INID"
        prefix={<MagnifyingGlassIcon size={16} weight="bold" />}
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
      />

      <DataGrid.Content>
        <DataGridTable
          data={filteredData}
          numRows={filteredData.length}
          tableColumns={DEFAULT_TABLE_COLUMNS}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </div>
  );
}
