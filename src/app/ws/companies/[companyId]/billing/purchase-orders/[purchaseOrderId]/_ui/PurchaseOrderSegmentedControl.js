"use client";

import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";
import HStack from "@/ui/HStack";
import { useParams, usePathname } from "next/navigation";

export default function PurchaseOrderSegmentedControl() {
  const { companyId, purchaseOrderId } = useParams();
  const pathname = usePathname();
  const baseUrl = `/ws/companies/${companyId}/billing/purchase-orders/${purchaseOrderId}`;

  let testPathname = pathname;

  if (pathname.startsWith(`${baseUrl}/SR`)) {
    testPathname = `${baseUrl}/SR`;
  }

  return (
    <SegmentedControlRoot
      value={testPathname}
      style={{ "--segmented-control-border-radius": "9999px" }}
    >
      <SegmentedControlItem href={`${baseUrl}/SR`}>
        <HStack>Employments (SR)</HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/invoices`}>
        <HStack>Invoices (INID)</HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/files`}>
        <HStack>Files</HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/rules`}>
        <HStack>Rules</HStack>
      </SegmentedControlItem>
    </SegmentedControlRoot>
  );
}
