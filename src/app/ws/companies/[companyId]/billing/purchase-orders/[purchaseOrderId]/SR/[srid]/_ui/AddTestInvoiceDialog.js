"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogFooter<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Header,
  DialogTrigger,
} from "@/ui/Dialog";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Button from "@/ui/Button";
import { CirclePlusIcon } from "lucide-react";
import {
  Form,
  FormBody,
  showFormErrors,
  FormSection,
  FormRow,
  CollectionForm,
} from "@/ui/Form";
import { useFieldArray, useForm, useFormContext } from "react-hook-form";
import AmountInput from "@/components/input/AmountInput";
import Select from "@/ui/Select";
import { formatWorkMonth } from "@/formatters/date";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";

const MUTATION = `
  mutation addTestInvoicesToPurchaseOrder($purchaseOrderEmploymentId: ID!, $invoices: [TestInvoiceInput!]!) {
    execute:addTestInvoicesToPurchaseOrder(purchaseOrderEmploymentId: $purchaseOrderEmploymentId, invoices: $invoices) {
      userErrors {
        path
        message
      }

      success
    }
  }
`;

export default function AddTestInvoiceDialog({ purchaseOrderEmployment }) {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const form = useForm({
    defaultValues: {
      invoices: [{}],
    },
  });
  const { control, reset } = form;
  const router = useRouter();

  const {
    fields: invoices,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "invoices",
  });

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      reset();
      setFormData(null);
      toast.success("Test invoices added successfully");
      router.refresh();
      setOpen(false);
    },
  });

  const onSubmit = (input) => {
    const { invoices } = input;

    setFormData(input);

    execute({
      purchaseOrderEmploymentId: purchaseOrderEmployment.id,
      invoices,
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <CirclePlusIcon
            size={32}
            strokeWidth={1.5}
            className="stroke-rose-500"
          />
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>Add test invoices</DialogHeader>

        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody className="mx-auto max-w-[800px] py-4">
            <CollectionForm
              items={invoices}
              add={append}
              remove={remove}
              maxItems={100}
            >
              {(_, index) => <InvoiceForm key={index} index={index} />}
            </CollectionForm>
          </FormBody>

          <DialogFooterWithCTA label="Add" pending={pending} />
        </Form>
      </DialogContent>
    </Dialog>
  );
}

const InvoiceForm = ({ index }) => {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <FormSection>
      <FormRow>
        <Select
          label="Work month"
          {...register(`invoices[${index}].workMonthM`, {
            required: "Work month is required!",
            valueAsNumber: true,
          })}
          error={errors.invoices?.[index]?.workMonthM}
        >
          <option value=""></option>
          {Array.from({ length: 12 }, (_, i) => {
            const month = new Date(2025, i);
            return (
              <option key={i} value={i + 1}>
                {formatWorkMonth(month)}
              </option>
            );
          })}
        </Select>

        <AmountInput
          label="Payable amount"
          {...register(`invoices[${index}].payableAmount`, {
            required: "Payable amount is required!",
          })}
          error={errors.invoices?.[index]?.payableAmount}
        />
      </FormRow>
    </FormSection>
  );
};
