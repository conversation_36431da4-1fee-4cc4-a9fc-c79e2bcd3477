"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  DialogFooter<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Header,
  DialogTrigger,
} from "@/ui/Dialog";
import { useState } from "react";
import { useParams, useRouter } from "next/navigation";
import Button from "@/ui/Button";
import { CirclePlusIcon } from "lucide-react";
import { Form, FormBody, FormSection } from "@/ui/Form";
import { useForm } from "react-hook-form";
import { currency } from "@/formatters/numeric";
import AutocompleteWithStatus from "@/components/autocomplete/AutocompleteWithStatus";
import HStack from "@/ui/HStack";
import StatusIndicator from "@/ui/StatusIndicator";
import Highlighter from "react-highlight-words";
import { uniqBy } from "lodash";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";

const QUERY = `
  query employmentsForPurchaseOrder($companyId: ID!, $purchaseOrderId: ID!, $query: String, $status: String) {
    items:employmentsForPurchaseOrder(companyId: $companyId, purchaseOrderId: $purchaseOrderId, query: $query, status: $status, page: 1, perPage: 100) {
      nodes {
        id
        fmId
        name
        contractorName
        status

        project {
          id
          fmId
        }
      }
    }
  }
`;

const MUTATION = `
  mutation addEmploymentsToPurchaseOrder($purchaseOrderId: ID!, $employmentIds: [ID!]!) {
    execute:addEmploymentsToPurchaseOrder(purchaseOrderId: $purchaseOrderId, employmentIds: $employmentIds) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Employment {
          id
        }
      }
    }
  }
`;

export default function AddEmploymentsToPurchaseOrderDialog({ purchaseOrder }) {
  const { companyId } = useParams();
  const [selection, setSelection] = useState([]);
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const form = useForm();
  const router = useRouter();

  const uniqueSelection = uniqBy(selection, (item) => item.id);

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      router.refresh();
      toast.success("SR added successfully");
      setOpen(false);
      setSelection([]);
    },
  });

  const onSubmit = () => {
    setFormData(uniqueSelection.map((item) => item.id));

    execute({
      purchaseOrderId: purchaseOrder.id,
      employmentIds: uniqueSelection.map((item) => item.id),
    });
  };

  const customSetOpen = (value) => {
    setOpen(value);

    if (!value) {
      setSelection([]);
      setFormData(null);
      form.reset();
    }
  };

  return (
    <Dialog open={open} onOpenChange={customSetOpen}>
      <DialogTrigger>
        <Button
          outline
          square
          variant="primary"
          prefix={<CirclePlusIcon size={20} />}
        >
          Add Employment &#187; PO
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          Include these employments to {purchaseOrder.fmId} (Budget=
          {currency(purchaseOrder.budgetedAmount)})
        </DialogHeader>

        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormSection className="mx-auto max-w-[800px] py-4">
            <FormBody>
              <AutocompleteWithStatus
                name="employments"
                label="Search for SRID, contractor name or PTID"
                gqlQuery={QUERY}
                gqlVariables={{ companyId, purchaseOrderId: purchaseOrder.id }}
                emptyMessage="No employments found."
                placeholder=""
                segmentedActiveLabel="Active employments only"
                onSelect={(item) => {
                  setSelection([...selection, item]);
                }}
              >
                {({ item, query }) => {
                  return (
                    <HStack className="w-full">
                      <StatusIndicator status={item.status} />
                      <Highlighter
                        textToHighlight={item.name}
                        autoEscape={true}
                        searchWords={query.split(/\s+/)}
                        className="tabular-nums"
                      />

                      <div className="text-muted ml-auto font-mono text-sm tabular-nums">
                        {item.project.fmId}
                      </div>

                      <Button
                        variant="plainIcon"
                        size="sm"
                        outline
                        prefix={
                          <CirclePlusIcon size={20} className="stroke-muted" />
                        }
                      />
                    </HStack>
                  );
                }}
              </AutocompleteWithStatus>

              <DisplaySelection
                selection={selection}
                purchaseOrder={purchaseOrder}
              />
            </FormBody>
          </FormSection>

          <DialogFooterWithCTA label="Add" pending={pending} />
        </Form>
      </DialogContent>
    </Dialog>
  );
}

const DisplaySelection = ({ selection, purchaseOrder }) => {
  if (selection.length === 0) {
    return <div className="text-muted">No employments selected.</div>;
  }

  const uniqueSelection = uniqBy(selection, (item) => item.id);

  return (
    <div className="space-y-4">
      <h1 className="text-rose-600">
        Please check these employments before we add it to {purchaseOrder.fmId}:
      </h1>
      <div className="space-y-1">
        {uniqueSelection.map((item) => {
          return (
            <HStack key={item.id} className="w-full">
              <StatusIndicator status={item.status} />
              <span className="tabular-nums">{item.name}</span>
            </HStack>
          );
        })}
      </div>
    </div>
  );
};
