"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import Button from "@/ui/Button";
import { FolderPlus, ChevronDown } from "lucide-react";
import { DotsThreeCircle } from "@phosphor-icons/react/dist/ssr";
import { useState } from "react";
import { useParams } from "next/navigation";

export default function ProjectActionsMenu() {
  const [open, setOpen] = useState(false);
  const params = useParams();
  const companyId = params.companyId;

  const baseUrl = `/ws/companies/${companyId}`;

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        onPointerDown={(e) => e.preventDefault()}
        onClick={() => setOpen((prev) => !prev)}
      >
        <Button
          variant="plainIcon"
          className="gap-0"
          prefix={<DotsThreeCircle size={24} />}
          suffix={<ChevronDown size={20} />}
        />
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <DropdownMenuItem href={`${baseUrl}/projects/new`}>
            <FolderPlus color="green" size={20} />
            New project...
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
