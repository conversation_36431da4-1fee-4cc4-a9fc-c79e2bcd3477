import apiQuery from "@/lib/apiQuery";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import Pagination from "@/ui/Pagination";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import decompressSearchParam from "@/lib/decompressSearchParam";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import QueryInput from "@/ui/DataGrid/QueryInput";
import HStack from "@/ui/HStack";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import ProjectActionsMenu from "@/app/ws/companies/[companyId]/projects/ProjectActionsMenu";

const QUERY = `
  query projects($companyId: ID, $query: String, $sorts: [SortInput!], $page: Int!) {
    projects(companyId: $companyId, query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        fmId
        name
        status
        updatedAt
        createdAt

        company {
          id
          name
        }

        owner {
          fmIdName
        }
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "PROJECTS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 120,
    propertyName: "fmId",
    visible: true,
  },
  {
    columnName: "Name",
    columnWidth: 300,
    propertyName: "name",
    removable: false,
    visible: true,
    href: "/ws/companies/${row.company.id}/projects/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Owner",
    columnWidth: 250,
    propertyName: "owner.fmIdName",
    visible: true,
  },
  {
    columnName: "Status",
    columnWidth: 120,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Headcount",
    columnWidth: 110,
    propertyName: "",
    visible: true,
  },
  {
    columnName: "Updated",
    columnWidth: 180,
    propertyName: "updatedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function CompanyProjectTable({ searchParams, params }) {
  let { page, query, sorts } = await searchParams;
  const { companyId } = await params;

  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "updatedAt", desc: true }]);

  const res = await apiQuery(QUERY, {
    companyId,
    query,
    sorts,
    page: currentPage,
  });

  const {
    records: projects,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(res.data.projects, res.data.personalTableColumn);

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      <div fix-auto-scroll-error=""></div>
      <h1 className="p-4 pb-2 text-xl font-bold">Projects</h1>
      <HStack className="sticky top-[92px] z-50 w-full justify-between gap-x-8 gap-y-2 border-b bg-background px-4 py-2">
        <QueryInput placeholder="Search projects" />

        <HStack className="flex-nowrap gap-8">
          <ProjectActionsMenu />
          <FieldsDialog
            tableName="PROJECTS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
          <Button variant="plainIcon" prefix={<Filter size={16} />}>
            Filters
          </Button>
          <SortsDialog
            initialSorts={sorts}
            options={[
              { label: "ID", propertyName: "id" },
              { label: "Name", propertyName: "name" },
              { label: "Date", propertyName: "createdAt" },
              { label: "Status", propertyName: "status" },
              { label: "Updated", propertyName: "updatedAt" },
            ]}
          />
        </HStack>
      </HStack>

      <DataGrid.Root className="p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            data={projects}
            numRows={projects.length}
            tableName="PROJECTS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
