import EarnedLeaveForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/EarnedLeaveForm";
import PaidFirstThreeMonthsLeaveForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/PaidFirstThreeMonthsLeaveForm";
import CarryForwardForm from "@/app/ws/projects/new/CarryForwardForm";
import { FormRow, FormSection } from "@/ui/Form";
import Input from "@/ui/Input";
import { useFormContext } from "react-hook-form";

export default function LeaveEntitlementForm({ ent, project }) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <FormSection title="Entitled leaves">
      <FormRow>
        <Input
          type="number"
          inputMode="numeric"
          max={99}
          label="Annual leave (AL)"
          placeholder="e.g. 14"
          suffix="days"
          suffixStyling
          {...register("alEntitledDays", {
            // required: true,
            valueAsNumber: true,
          })}
          error={errors.alEntitledDays}
        />
        <Input
          type="number"
          inputMode="numeric"
          max={99}
          label="Medical leave (ML)"
          placeholder="e.g. 14"
          suffix="days"
          suffixStyling
          {...register("mlEntitledDays", {
            // required: true,
            valueAsNumber: true,
          })}
          error={errors.mlEntitledDays}
        />
        <Input
          type="number"
          inputMode="numeric"
          max={99}
          label="Family care leave (FCL)"
          placeholder="e.g. 2"
          suffix="days"
          suffixStyling
          {...register("fclEntitledDays", { valueAsNumber: true })}
        />
      </FormRow>

      <PaidFirstThreeMonthsLeaveForm />
      <EarnedLeaveForm />
      <CarryForwardForm ent={ent} project={project} />
    </FormSection>
  );
}
