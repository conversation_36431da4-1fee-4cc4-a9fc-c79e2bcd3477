import CPFClaimHelpText from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/CPFClaimHelpText";
import ClaimTypeSelect from "@/components/ClaimTypeSelect";
import AmountInput from "@/components/input/AmountInput";
import { CollectionForm, FormBody, FormRow, FormSection } from "@/ui/Form";
import { RadioGroup, RadioGroupItem } from "@/ui/RadioGroup";
import Select from "@/ui/Select";
import { useFieldArray, useFormContext } from "react-hook-form";

export default function ClaimLimitsForm() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  const {
    fields: claimLimits,
    append,
    remove,
  } = useFieldArray({
    name: "claimLimits",
  });

  return (
    <FormBody>
      <FormSection title="Medical claim">
        <FormRow colsCount={2}>
          <AmountInput
            label="Max medical claim amount"
            {...register("maxMedicalClaimAmount", { valueAsNumber: true })}
          />
        </FormRow>
      </FormSection>

      <FormSection title={`Claim limits (${claimLimits.length})`}>
        <CollectionForm
          items={claimLimits}
          add={append}
          remove={remove}
          maxItems={50}
          allowZeroItems
        >
          {(claimLimit, index) => {
            return (
              <FormSection>
                <FormRow>
                  <RadioGroup
                    name={`claimLimits[${index}].claimMethod`}
                    rules={{ required: true }}
                    orientation="horizontal"
                  >
                    <label className="inline-flex w-fit items-center gap-2">
                      <RadioGroupItem value="Allowance" /> Allowance (CPF)
                    </label>
                    <label className="inline-flex w-fit items-center gap-2">
                      <RadioGroupItem value="Claim" /> Claim (without CPF)
                    </label>
                    <CPFClaimHelpText />
                  </RadioGroup>
                </FormRow>

                <ClaimTypeSelect fieldArrayName="claimLimits" index={index} />

                <FormRow>
                  <AmountInput
                    label="Maximum amount"
                    {...register(`claimLimits[${index}].maxAmount`, {
                      valueAsNumber: true,
                      required: true,
                    })}
                    error={errors.claimLimits?.[index]?.maxAmount}
                  />

                  <Select
                    label="Enforcement period"
                    {...register(`claimLimits[${index}].period`)}
                  >
                    <option value="Per Claim">Per Claim</option>
                    <option value="Daily">Daily</option>
                    <option value="Weekly">Weekly</option>
                    <option value="Monthly">Monthly</option>
                    <option value="Yearly">Yearly</option>
                  </Select>
                </FormRow>
              </FormSection>
            );
          }}
        </CollectionForm>
      </FormSection>
    </FormBody>
  );
}
