"use client";

import EditEntBenefitsTemplateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntBenefitsTemplateForm";
import EditEntInsuranceTemplateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntInsuranceTemplateForm";
import EditEntTerminationTemplateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntTerminationTemplateForm";
import EditEntValuesForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntValuesForm";
import {
  BenefitsIcon,
  InsuranceIcon,
  TerminationIcon,
} from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_icons";
import DelayedSpinner from "@/components/DelayedSpinner";
import {
  Dialog,
  DialogContent,
  DialogDismissButton,
  DialogFooter,
  DialogHeader,
} from "@/ui/Dialog";
import { Tab, TertiaryTabs } from "@/ui/TertiaryTabs";
import { Function } from "@blueprintjs/icons";
import { useState } from "react";
import { useQuery } from "urql";

const QUERY = `
  query ent($id: ID!) {
    ent(id: $id) {
      id
      fmId
      alEntitledDays
      mlEntitledDays
      fclEntitledDays

      description

      carryForward
      carryForwardValidMonths
      maxCarryForwardDays

      maxMedicalClaimAmount

      noticeDays
      probationMonths
      probationNoticeDays

      allow3mPaidLeaves
      earnedLeaves

      hasOvertime
      overtimePeriod
      overtimeThreshold
      overtimeDescription

      workingDays
      workingHoursDescription
      breakDurationDescription
      workingHours {
        id
        workingHourFrom
        workingHourTo
        breakDuration
        workingDays
        name
        description
        displayWorkingDays
      }

      claimLimits {
        id
        claimMethod
        claimType
        subClaimType
        maxAmount
        period
      }

      transportLimits {
        id
        transportMode
        transportRate
        status
      }

      insuranceTemplate
      benefitsTemplate
      terminationTemplate

      creator {
        id
        name
      }
      modifier {
        id
        name
      }

      status
      createdAt
      updatedAt
    }
  }
`;

export default function EditEntDialog({ entId, open, setOpen }) {
  const [tab, setTab] = useState("Ent Values");

  const [result] = useQuery({
    query: QUERY,
    variables: { id: entId },
    pause: !open,
  });

  const { data, fetching } = result;
  const ent = data?.ent;

  const tabButtonClassNames =
    "flex w-full items-center justify-center gap-2 p-2";

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>Edit {ent?.fmId}</DialogHeader>

        {fetching && !ent ? (
          <DelayedSpinner className="my-8" />
        ) : (
          <>
            <section
              className={
                fetching
                  ? "space-y-4 py-4 opacity-25 transition-opacity"
                  : "space-y-4 py-4"
              }
            >
              <TertiaryTabs className="grid-cols-4">
                <Tab active={tab === "Ent Values"}>
                  <button
                    type="button"
                    className={tabButtonClassNames}
                    onClick={() => setTab("Ent Values")}
                  >
                    <Function color="currentColor" size={20} />
                    <span className="hidden md:block">Ent Values</span>
                  </button>
                </Tab>
                <Tab active={tab === "Insurance"}>
                  <button
                    type="button"
                    className="flex w-full items-center justify-center gap-2 p-2"
                    onClick={() => setTab("Insurance")}
                  >
                    <InsuranceIcon className="fill-current" />
                    <span className="hidden md:block">Insurance</span>
                  </button>
                </Tab>
                <Tab active={tab === "Benefits"}>
                  <button
                    type="button"
                    className="flex w-full items-center justify-center gap-2 p-2"
                    onClick={() => setTab("Benefits")}
                  >
                    <BenefitsIcon className="fill-current" />
                    <span className="hidden md:block">Benefits</span>
                  </button>
                </Tab>
                <Tab active={tab === "Termination"}>
                  <button
                    type="button"
                    className="flex w-full items-center justify-center gap-2 p-2"
                    onClick={() => setTab("Termination")}
                  >
                    <TerminationIcon className="fill-current" />
                    <span className="hidden md:block">Termination</span>
                  </button>
                </Tab>
              </TertiaryTabs>

              {tab === "Ent Values" && <EditEntValuesForm ent={ent} />}
              {tab === "Insurance" && (
                <EditEntInsuranceTemplateForm ent={ent} />
              )}
              {tab === "Benefits" && <EditEntBenefitsTemplateForm ent={ent} />}
              {tab === "Termination" && (
                <EditEntTerminationTemplateForm ent={ent} />
              )}
            </section>

            <DialogFooter>
              <div className="flex w-full justify-center gap-3">
                <DialogDismissButton pill />
              </div>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
