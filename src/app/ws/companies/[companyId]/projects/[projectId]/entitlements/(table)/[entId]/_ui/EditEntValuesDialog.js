"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Di<PERSON>Trigger,
  Di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>on,
  Di<PERSON>Footer,
} from "@/ui/Dialog";
import { useState } from "react";
import Button from "@/ui/Button";
import { Edit } from "@blueprintjs/icons";
import DelayedSpinner from "@/components/DelayedSpinner";
import { useQuery } from "urql";
import EditEntValuesForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntValuesForm";

const QUERY = `
  query ent($id: ID!) {
    ent(id: $id) {
      id
      fmId
      alEntitledDays
      mlEntitledDays
      fclEntitledDays

      description

      carryForward
      carryForwardValidMonths
      maxCarryForwardDays

      maxMedicalClaimAmount

      noticeDays
      probationMonths
      probationNoticeDays

      allow3mPaidLeaves
      earnedLeaves

      hasOvertime
      overtimePeriod
      overtimeThreshold
      overtimeDescription

      workingDays
      workingHoursDescription
      breakDurationDescription
      workingHours {
        id
        workingHourFrom
        workingHourTo
        breakDuration
        workingDays
        name
        description
        displayWorkingDays
      }

      claimLimits {
        id
        claimMethod
        claimType
        subClaimType
        maxAmount
        period
      }

      transportLimits {
        id
        transportMode
        transportRate
        status
      }

      status
      createdAt
      updatedAt
    }
  }
`;

export default function EditEntValuesDialog({ entId }) {
  const [open, setOpen] = useState(false);

  const [result] = useQuery({
    query: QUERY,
    variables: { id: entId },
    pause: !open,
  });

  const { data, fetching } = result;
  const ent = data?.ent;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <Edit size={20} />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>Edit {ent?.fmId}</DialogHeader>

        {fetching && !ent ? (
          <DelayedSpinner className="my-8" />
        ) : (
          <>
            <div
              className={
                fetching ? "py-4 opacity-25 transition-opacity" : "py-4"
              }
            >
              {ent && <EditEntValuesForm ent={ent} />}
            </div>

            <DialogFooter>
              <div className="flex w-full justify-center gap-3">
                <DialogDismissButton pill />
              </div>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
