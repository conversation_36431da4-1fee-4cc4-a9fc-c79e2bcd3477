import TextCell from "@/ui/DataGrid/cells/TextCell";
import EntLeaveCell from "@/ui/DataGrid/cells/EntLeaveCell";
import BooleanCell from "@/ui/DataGrid/cells/BooleanCell";
import decompressSearchParam from "@/lib/decompressSearchParam";
import apiQuery from "@/lib/apiQuery";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import Pagination from "@/ui/Pagination";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import WorkingHourCell from "@/ui/DataGrid/cells/WorkingHourCell";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import HtmlCell from "@/ui/DataGrid/cells/HtmlCell";

const QUERY = `
  query ents($projectId: ID!, $query: String, $sorts: [SortInput!], $page: Int!) {
    ents(projectId: $projectId, query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        fmId
        status
        updatedAt
        createdAt

        alEntitledDays
        mlEntitledDays
        fclEntitledDays
        allow3mPaidLeaves
        earnedLeaves

        carryForward
        carryForwardValidMonths
        maxCarryForwardDays

        noticeDays

        description

        workingHours {
          id
          workingHourFrom
          workingHourTo
          workingDays
          breakDuration
        }
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "PROJECT_ENTS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 120,
    propertyName: "fmId",
    visible: true,
    cellComponent: TextCell,
  },
  {
    columnName: "Description",
    columnWidth: 200,
    propertyName: "description",
    visible: true,
    cellComponent: HtmlCell,
  },
  {
    columnName: "Leaves",
    columnWidth: 200,
    propertyName: "alEntitledDays",
    href: "/ws/companies/${params.companyId}/projects/${params.projectId}/entitlements/${id}",
    visible: true,
    cellComponent: EntLeaveCell,
  },
  {
    columnName: "Working Hours",
    columnWidth: 200,
    propertyName: "workingHours",
    visible: true,
    cellComponent: WorkingHourCell,
  },
  {
    columnName: "Usage",
    columnWidth: 110,
    propertyName: "",
    visible: true,
  },
  {
    columnName: "1st 3m Paid",
    columnWidth: 110,
    propertyName: "allow3mPaidLeaves",
    visible: true,
    cellComponent: BooleanCell,
  },
  {
    columnName: "Earned AL",
    columnWidth: 100,
    propertyName: "earnedLeaves",
    visible: true,
    cellComponent: BooleanCell,
  },
  {
    columnName: "Created",
    columnWidth: 150,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
  {
    columnName: "Updated",
    columnWidth: 150,
    propertyName: "updatedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function EntTable({ searchParams, params }) {
  let { page, query, sorts } = await searchParams;
  const { projectId, all } = await params;

  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "updatedAt", desc: true }]);

  const res = await apiQuery(QUERY, {
    projectId,
    query,
    sorts,
    page: currentPage,
  });

  const {
    records: ents,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(res.data.ents, res.data.personalTableColumn);

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  const selectedIds = all?.[5] ? [all[5]] : [];

  return (
    <DataGrid.Root className="space-y-2">
      <Pagination currentPage={currentPage} totalPages={totalPages} />
      <DataGrid.TotalCountAndTime
        totalCount={totalCount}
        processingTimeMs={processingTimeMs}
      />

      <DataGrid.Content>
        <DataGridTable
          selectedIds={selectedIds}
          numRows={ents.length}
          data={ents}
          tableName="PROJECT_ENTS"
          tableColumns={tableColumns}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
}
