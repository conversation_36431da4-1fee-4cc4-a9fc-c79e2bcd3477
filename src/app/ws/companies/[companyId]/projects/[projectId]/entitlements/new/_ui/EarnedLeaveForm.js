import EarnedLeavesIsNotProRatedHelpText from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/EarnedLeavesIsNotProRatedHelpText";
import EarnedLeavesIsProRatedHelpText from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/EarnedLeavesIsProRatedHelpText";
import { FormRow } from "@/ui/Form";
import HStack from "@/ui/HStack";
import Switch from "@/ui/Switch";
import VStack from "@/ui/VStack";
import { useFormContext, useWatch } from "react-hook-form";

export default function EarnedLeaveForm() {
  const { register } = useFormContext();
  const watchEarnedLeaves = useWatch({ name: "earnedLeaves" });

  return (
    <VStack className="gap-1">
      <p className="leading-tight">
        <strong>Earned leaves:</strong> Pro-rate AL, ML & HOSL based on
        completed months. Note: CCL will always be pro-rated.
      </p>

      <FormRow>
        <label className="inline-flex w-fit items-center gap-2">
          <Switch {...register("earnedLeaves")} />
          <span className="text-sm text-muted">
            {watchEarnedLeaves ? (
              <HStack>
                Leaves will be pro-rated based on completed months{" "}
                <EarnedLeavesIsProRatedHelpText />
              </HStack>
            ) : (
              <HStack>
                Leaves are not pro-rated based on completed months{" "}
                <EarnedLeavesIsNotProRatedHelpText />
              </HStack>
            )}
          </span>
        </label>
      </FormRow>
    </VStack>
  );
}
