import { Form, FormSection, showFormErrors } from "@/ui/Form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/ui/Accordion";
import TiptapEditor, {
  TiptapEditorPortalWrapper,
} from "@/ui/editors/TiptapEditor";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useCurrentEditor } from "@tiptap/react";
import VStack from "@/ui/VStack";
import HStack from "@/ui/HStack";
import Checkbox from "@/ui/Checkbox";
import Button from "@/ui/Button";
import {
  ChevronRight,
  ClipboardPasteIcon,
  GripHorizontalIcon,
} from "lucide-react";
import { InfoIcon } from "@phosphor-icons/react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";

const MUTATION = `
  mutation updateEntInsuranceTemplate($id: ID!, $insuranceTemplate: String) {
    execute:updateEntInsuranceTemplate(id: $id, insuranceTemplate: $insuranceTemplate) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Ent {
          id
        }
      }
    }
  }
`;

export default function EditEntInsuranceTemplateForm({ ent }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm();
  const { setError } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Entitlement updated successfully");
      router.refresh();
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    execute({
      id: ent.id,
      insuranceTemplate: input.insuranceTemplate,
    });
  };

  return (
    <div className="space-y-4">
      <Form form={form} onSubmit={onSubmit} formData={formData}>
        <FormSection>
          <HStack className="justify-between">
            <h1 className="text-lg font-bold">Insurance</h1>

            <Button type="submit" loading={pending}>
              Save insurance template
            </Button>
          </HStack>

          <div id="editor-insurance-preset" />

          <TiptapEditor
            name="insuranceTemplate"
            label="Insurance template"
            rules={{ required: "Template body is required!" }}
            defaultValue={ent.insuranceTemplate}
            stickyTop="70px"
          >
            <TiptapEditorPortalWrapper portalId="editor-insurance-preset">
              <InsurancePresets />
            </TiptapEditorPortalWrapper>
          </TiptapEditor>

          <HStack className="justify-end">
            <Button type="submit" loading={pending}>
              Save insurance template
            </Button>
          </HStack>
        </FormSection>
      </Form>
    </div>
  );
}

const PRESETS = [
  { value: "Work Injury Compensation Act (WICA)", tag: "li" },
  { value: "Group Hospitalisation & Surgical Insurance (H&S)", tag: "li" },
  { value: "Group Term Life (GTL)", tag: "li" },
  {
    value: "Group Term Life and Group Hospitalisation & Surgical Plan",
    tag: "li",
  },
  {
    value:
      "Except Workman Compensation, Group Term Life and Group Term Life and Group Hospitalisation & Surgical Plan insurance policies are only applicable to employees with at least 6 months of contract with NCS.",
    tag: "li",
  },
  {
    space: true,
  },
  {
    value:
      "Insurance coverage is subject to the age limit criteria, medical underwriting requirement, exclusions, termination clauses and other terms and conditions as defined in the policies and cover is upon acceptance by the insurance company. Pre-existing conditions are not covered.",
    tag: "li",
  },
  {
    value:
      "Please download the insurance booklet from Jobline portal to view the entry age and age limit for the different insurance policies (if the policies are applicable to you and if you are covered under each policy).",
    tag: "li",
  },
  {
    value:
      "For medical treatment expenses, contract staff agrees the following terms:",
    tag: "p",
    breakAbove: true,
    values: [
      {
        value:
          "At all times, you will evaluate and select the most economical ward class and arrange for treatments appropriate to the insurance coverage level and your financial situation for inpatient/outpatient treatments in Singapore Government / Restructured Hospital / clinics. Do note that our insurance coverage is extended to Singapore Government / Restructured Hospital only.",
        tag: "ol",
      },
      {
        value:
          "Should you select any treatment at Private Hospitals/Clinics not under our company insurance coverage, you agree that you will bear all medical expenses not reimbursed by the insurer.",
        tag: "ol",
      },
      {
        value:
          "You will settle all medical treatment expenses bills (Inpatient and/or Outpatient) first with the hospitals/clinics and submit the required supporting document to our insurer for insurance claims processing.",
        tag: "ol",
      },
      {
        value:
          "Any medical expenses not reimbursed by the insurer will be borne by yourself.",
        tag: "ol",
      },
      {
        value:
          "For non-work injury inpatient medical treatment expenses, S Pass / Work Permit holders agree to pay part of his/her own medical bills if the inpatient medical treatment costs is above the $15,000 minimum medical insurance requirement. The portion of inpatient medical expenses payable by the Pass holders will be at no more than 10% of the Pass holder’s fixed monthly salary for a period of 6 months.",
        tag: "ol",
      },
    ],
  },
];

function InsurancePresets() {
  const { editor } = useCurrentEditor();
  const [formData, setFormData] = useState({});
  const form = useForm();
  const { register, reset } = form;

  const onSubmit = (data) => {
    setFormData(data);

    // Based on the selection checkbox, we filter a new PRESET array
    const newPreset = [];
    PRESETS.filter((preset, index) => {
      if (preset.space) return;

      if (preset.values) {
        // Any options selected?
        const selectedOptions = preset.values.filter((option, innerIndex) => {
          return data.presets[index].options[innerIndex].selected;
        });

        if (selectedOptions.length > 0) {
          if (preset.breakAbove) {
            newPreset.push({ value: "", tag: "p" });
          }

          newPreset.push({
            ...preset,
            values: selectedOptions,
          });
        } else if (data.presets[index].selected) {
          if (preset.breakAbove) {
            newPreset.push({ value: "", tag: "p" });
          }

          newPreset.push({ value: preset.value, tag: preset.tag }); // Just the main content
        }
      } else {
        if (data.presets[index].selected) {
          newPreset.push(preset);
        }
      }
    });

    const htmlArray = newPreset.map((preset) => {
      if (preset.values) {
        const listTag = preset.values[0].tag === "ol" ? "ol" : "ul";

        return `
<${preset.tag}>${preset.value}</${preset.tag}>
<${listTag}>${preset.values.map((value) => `<li>${value.value}</li>`).join("")}</${listTag}>
        `;
      } else {
        return `<${preset.tag}>${preset.value}</${preset.tag}>`;
      }
    });

    const html = htmlArray.map((html) => html.trim()).join("");
    editor.chain().focus().insertContent(html).run();

    reset();
  };

  return (
    <Accordion type="multiple">
      <AccordionItem value="leave" outline={false}>
        <AccordionTrigger
          className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
          arrow={false}
        >
          <ChevronRight
            className="accordion-arrow transition-transform duration-150"
            strokeWidth={2}
          />
          <div className="text-amber-600">
            <ClipboardPasteIcon />
          </div>
          <VStack className="gap-0">
            <h2 className="text-lg leading-none font-bold text-amber-600">
              Presets
            </h2>
            <p className="text-muted text-sm font-normal">
              Choose the following insurance lines and copy them to the editor.
            </p>
          </VStack>
        </AccordionTrigger>
        <AccordionContent className="pl-[28px]">
          <div className="space-y-4">
            <Form
              form={form}
              onSubmit={onSubmit}
              formData={formData}
              className="space-y-4"
            >
              <VStack className="gap-0 divide-y text-sm leading-tight">
                {PRESETS.map((preset, index) => {
                  if (preset.space) {
                    return (
                      <div
                        key={index}
                        className="flex justify-center text-gray-300"
                      >
                        <GripHorizontalIcon />
                      </div>
                    );
                  }

                  return (
                    <div key={index} className="py-1">
                      <HStack>
                        <Checkbox
                          suffix={preset.value}
                          {...register(`presets.${index}.selected`)}
                        />
                      </HStack>
                      {preset.values && (
                        <div className="pl-8">
                          <div className="divide-y">
                            {preset.values.map((value, innerIndex) => (
                              <HStack
                                key={`inner-${innerIndex}`}
                                className="py-1"
                              >
                                <Checkbox
                                  suffix={value.value}
                                  {...register(
                                    `presets.${index}.options.${innerIndex}.selected`,
                                  )}
                                />
                              </HStack>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </VStack>

              <HStack>
                <Button
                  type="submit"
                  variant="secondary"
                  suffix={<ClipboardPasteIcon size={20} />}
                >
                  Copy preset to editor
                </Button>
                <InfoIcon size={20} />
                <span className="text-muted">
                  Ensure content correctness before saving!
                </span>
              </HStack>
            </Form>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
