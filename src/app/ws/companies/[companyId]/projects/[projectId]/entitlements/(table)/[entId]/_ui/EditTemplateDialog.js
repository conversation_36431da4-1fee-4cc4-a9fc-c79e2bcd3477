"use client";

import EditEntBenefitsTemplateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntBenefitsTemplateForm";
import EditEntInsuranceTemplateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntInsuranceTemplateForm";
import EditEntTerminationTemplateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/EditEntTerminationTemplateForm";
import {
  BenefitsIcon,
  InsuranceIcon,
  TerminationIcon,
} from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_icons";
import DelayedSpinner from "@/components/DelayedSpinner";
import cn from "@/lib/cn";
import Button from "@/ui/Button";
import {
  Dialog,
  DialogContent,
  Dialog<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTrigger,
} from "@/ui/Dialog";
import { Edit } from "@blueprintjs/icons";
import { useState } from "react";
import { useQuery } from "urql";

const QUERY = `
  query ent($id: ID!) {
    ent(id: $id) {
      id

      insuranceTemplate
      benefitsTemplate
      terminationTemplate
    }
  }
`;

export default function EditTemplateDialog({
  entId,
  defaultTab = "Insurance",
}) {
  const [open, setOpen] = useState(false);
  const [tab, setTab] = useState(defaultTab);

  const [result] = useQuery({
    query: QUERY,
    variables: { id: entId },
    pause: !open,
  });

  const { data, fetching } = result;
  const ent = data?.ent;

  const tabButtonClassNames = (tabName) => {
    return cn(
      "flex items-center justify-center gap-2 p-2",
      "hover:bg-gray-100 hover:first:rounded-l-sm hover:last:rounded-r-sm",
      {
        "bg-gradient-to-t from-white to-gray-200 font-medium first:rounded-l-sm last:rounded-r-sm":
          tab === tabName,
      },
    );
  };

  // Note: fetching need to test for ent existence to avoid flashing UI after submission

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <Edit size={20} />
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>Edit ENT: {ent?.id}</DialogHeader>

        {fetching && !ent ? (
          <DelayedSpinner className="my-8" />
        ) : (
          <>
            <section className="space-y-4 py-4">
              <div className="grid grid-cols-3 divide-x divide-gray-300 rounded-sm border border-gray-300 bg-gradient-to-b from-white to-gray-100 text-center select-none">
                <button
                  type="button"
                  className={tabButtonClassNames("Insurance")}
                  onClick={() => setTab("Insurance")}
                >
                  <InsuranceIcon />
                  <span className="hidden md:block">Insurance</span>
                </button>
                <button
                  type="button"
                  className={tabButtonClassNames("Benefits")}
                  onClick={() => setTab("Benefits")}
                >
                  <BenefitsIcon />
                  <span className="hidden md:block">Benefits</span>
                </button>
                <button
                  type="button"
                  className={tabButtonClassNames("Termination")}
                  onClick={() => setTab("Termination")}
                >
                  <TerminationIcon />
                  <span className="hidden md:block">Termination</span>
                </button>
              </div>

              {tab === "Insurance" && (
                <EditEntInsuranceTemplateForm ent={ent} />
              )}
              {tab === "Benefits" && <EditEntBenefitsTemplateForm ent={ent} />}
              {tab === "Termination" && (
                <EditEntTerminationTemplateForm ent={ent} />
              )}
            </section>

            <DialogFooter>
              <div className="flex w-full justify-center gap-3">
                <DialogDismissButton pill />
              </div>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
