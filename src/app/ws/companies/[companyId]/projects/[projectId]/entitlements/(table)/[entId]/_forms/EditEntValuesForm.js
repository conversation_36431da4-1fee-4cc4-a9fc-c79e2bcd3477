import { updateEntAction } from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_forms/updateEntAction";
import ClaimLimitsForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/ClaimLimitsForm";
import LeaveEntitlementForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/LeaveEntitlementForm";
import TransportLimitsForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/TransportLimitsForm";
import WorkingHoursForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/WorkingHoursForm";
import AutocompleteNoticeDay from "@/components/autocomplete/AutocompleteNoticeDay";
import useAction from "@/hooks/useAction";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/ui/Accordion";
import Button from "@/ui/Button";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import {
  Form,
  FormBody,
  FormRow,
  FormSection,
  showFormErrors,
} from "@/ui/Form";
import HStack from "@/ui/HStack";
import Select from "@/ui/Select";
import VStack from "@/ui/VStack";
import { Numerical } from "@blueprintjs/icons";
import { isPlainObject } from "lodash";
import {
  BadgeDollarSignIcon,
  ChevronRight,
  ClockIcon,
  FuelIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export default function EditEntValuesForm({ ent }) {
  const [formData, setFormData] = useState(null);
  const [accordionValues, setAccordionValues] = useState("");
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      noticeDays: ent.noticeDays,
      probationNoticeDays: ent.probationNoticeDays,
      probationMonths: ent.probationMonths,
      alEntitledDays: ent.alEntitledDays,
      mlEntitledDays: ent.mlEntitledDays,
      fclEntitledDays: ent.fclEntitledDays,
      allow3mPaidLeaves: ent.allow3mPaidLeaves,
      earnedLeaves: ent.earnedLeaves,
      carryForward: ent.carryForward,
      carryForwardValidMonths: ent.carryForwardValidMonths,
      maxCarryForwardDays: ent.maxCarryForwardDays,
      hasOvertime: ent.hasOvertime,
      overtimePeriod: ent.overtimePeriod,
      overtimeThreshold: ent.overtimeThreshold,
      overtimeDescription: ent.overtimeDescription,
      workingDays: ent.workingDays,
      workingHoursDescription: ent.workingHoursDescription,
      breakDurationDescription: ent.breakDurationDescription,
      workingHours: ent.workingHours,
      maxMedicalClaimAmount: ent.maxMedicalClaimAmount,
      claimLimits: ent.claimLimits,
      transportLimits: ent.transportLimits,
    },
  });
  const { setError } = form;

  const [execute, pending] = useAction(updateEntAction, {
    onSuccess: () => {
      toast.success("Entitlement updated successfully");
      router.refresh();
    },
    onError: ({ userErrors }) => {
      const errorKeys = userErrors.map((e) => e.path[0]);
      if (
        ["alEntitledDays", "mlEntitledDays", "fclEntitledDays"].some((value) =>
          errorKeys.includes(value),
        )
      )
        errorKeys.push("leave");

      setAccordionValues(errorKeys);
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    const { workingHours, claimLimits, transportLimits, ...restOfInput } =
      input;

    const workingHourInputs = workingHours.map((workingHour) => {
      return {
        workingHourFrom: isPlainObject(workingHour.workingHourFrom)
          ? workingHour.workingHourFrom.value
          : workingHour.workingHourFrom,
        breakDuration: isPlainObject(workingHour.breakDuration)
          ? workingHour.breakDuration.value
          : workingHour.breakDuration,
        workingHourTo: isPlainObject(workingHour.workingHourTo)
          ? workingHour.workingHourTo.value
          : workingHour.workingHourTo,
        workingDays: workingHour.workingDays,
        description: workingHour.description,
        name: workingHour.name,
      };
    });

    const claimLimitInputs = claimLimits.map((claimLimit) => {
      const { id, ...rest } = claimLimit;

      return rest;
    });

    const transportLimitInputs = transportLimits.map((transportLimit) => {
      const { id, ...rest } = transportLimit; // remove the ID if any because we are going to replace the whole object

      return rest;
    });

    const newInput = {
      ...restOfInput,
      noticeDays: isPlainObject(restOfInput.noticeDays)
        ? restOfInput.noticeDays?.value
        : restOfInput.noticeDays,
      probationNoticeDays: isPlainObject(restOfInput.probationNoticeDays)
        ? restOfInput.probationNoticeDays?.value
        : restOfInput.probationNoticeDays,
      carryForwardValidMonths: isPlainObject(
        restOfInput.carryForwardValidMonths,
      )
        ? restOfInput.carryForwardValidMonths?.value
        : restOfInput.carryForwardValidMonths,
      maxCarryForwardDays: isPlainObject(restOfInput.maxCarryForwardDays)
        ? restOfInput.maxCarryForwardDays?.value
        : restOfInput.maxCarryForwardDays,
    };

    execute({
      id: ent.id,
      entInput: newInput,
      workingHourInputs,
      claimLimitInputs,
      transportLimitInputs,
    });
  };

  return (
    <div className="space-y-8">
      <VStack className="gap-0">
        <h1 className="text-lg font-bold">Entitlement Values</h1>
        <p className="text-sm text-muted">
          Configure Entitlement Values such as entitled leaves, claim limit
          amounts and working hours that constitute the Key Employment Terms
          (KET).
        </p>
      </VStack>

      <div className="mx-auto max-w-[800px] space-y-4">
        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody className="space-y-4">
            <TiptapEditor
              name="description"
              label="ENT description"
              rules={{ required: "Description is required!" }}
              defaultValue={ent.description}
            />

            <FormSection title="Notice period for termination of employment">
              <FormRow colsCount={2}>
                <AutocompleteNoticeDay
                  label="Notice period"
                  name="noticeDays"
                  defaultValue={ent.noticeDays}
                />
              </FormRow>
            </FormSection>

            <FormSection title="Notice period during probation (optional)">
              <FormRow colsCount={2}>
                <Select
                  label="Probation duration (in months)"
                  {...form.register("probationMonths", { valueAsNumber: true })}
                >
                  <option value=""></option>
                  <option value="3">3 months</option>
                  <option value="6">6 months</option>
                </Select>
                <AutocompleteNoticeDay
                  label="Notice period during probation"
                  name="probationNoticeDays"
                  defaultValue={ent.probationNoticeDays}
                />
              </FormRow>
            </FormSection>

            <HStack className="justify-end">
              <Button type="submit" loading={pending}>
                Save
              </Button>
            </HStack>

            <Accordion
              type="multiple"
              value={accordionValues}
              onValueChange={(values) => setAccordionValues(values)}
            >
              <AccordionItem value="leave" outline={false}>
                <AccordionTrigger
                  className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                  arrow={false}
                >
                  <ChevronRight
                    className="accordion-arrow transition-transform duration-150"
                    strokeWidth={1.5}
                  />
                  <div className="text-amber-600">
                    <Numerical size={24} />
                  </div>
                  <VStack className="gap-0">
                    <h2 className="text-lg leading-none font-semibold">
                      Entitled Leaves
                    </h2>
                    <p className="text-sm font-normal text-muted">
                      Set up the number of entitled leaves and the carry-forward
                      policy.
                    </p>
                  </VStack>
                </AccordionTrigger>
                <AccordionContent className="pr-1.5 pl-14">
                  <div className="space-y-4 py-2">
                    <LeaveEntitlementForm ent={ent} />

                    <Button type="submit" fullWidth loading={pending}>
                      Save
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="workingHours" outline={false}>
                <AccordionTrigger
                  className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                  arrow={false}
                >
                  <ChevronRight
                    className="accordion-arrow transition-transform duration-150"
                    strokeWidth={1.5}
                  />

                  <div className="text-pink-600">
                    <ClockIcon size={24} strokeWidth={1.5} />
                  </div>

                  <VStack className="gap-0">
                    <h2 className="text-lg leading-none font-semibold">
                      Working Hours &amp; Overtime Settings
                    </h2>
                    <p className="text-sm font-normal text-muted">
                      Configure the working hours and overtime settings.
                    </p>
                  </VStack>
                </AccordionTrigger>
                <AccordionContent className="pr-1.5 pl-14">
                  <div className="space-y-4 py-2">
                    <WorkingHoursForm ent={ent} />

                    <Button type="submit" fullWidth loading={pending}>
                      Save
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="claimLimits" outline={false}>
                <AccordionTrigger
                  className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                  arrow={false}
                >
                  <ChevronRight
                    className="accordion-arrow transition-transform duration-150"
                    strokeWidth={1.5}
                  />

                  <div className="text-purple-600">
                    <BadgeDollarSignIcon size={24} strokeWidth={1.5} />
                  </div>

                  <VStack className="gap-0">
                    <h2 className="text-lg leading-none font-semibold">
                      Claim Limits &amp; Medical Claim Amounts
                    </h2>
                    <p className="text-sm font-normal text-muted">
                      Determine what types of claims are allowed and the maximum
                      limits.
                    </p>
                  </VStack>
                </AccordionTrigger>
                <AccordionContent className="pr-1.5 pl-14">
                  <div className="space-y-4 py-2">
                    <ClaimLimitsForm />

                    <Button type="submit" fullWidth loading={pending}>
                      Save
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="transportLimits" outline={false}>
                <AccordionTrigger
                  className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                  arrow={false}
                >
                  <ChevronRight
                    className="accordion-arrow transition-transform duration-150"
                    strokeWidth={1.5}
                  />

                  <div className="text-blue-600">
                    <FuelIcon size={24} strokeWidth={1.5} />
                  </div>

                  <VStack className="gap-0">
                    <h2 className="text-lg leading-none font-semibold">
                      Transport Rates
                    </h2>
                    <p className="text-sm font-normal text-muted">
                      Set the claimable mileage.
                    </p>
                  </VStack>
                </AccordionTrigger>
                <AccordionContent className="pr-1.5 pl-14">
                  <div className="space-y-4 py-2">
                    <TransportLimitsForm />

                    <Button type="submit" fullWidth loading={pending}>
                      Save
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </FormBody>
        </Form>
      </div>
    </div>
  );
}
