"use client";

import ClaimLimitsForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/ClaimLimitsForm";
import LeaveEntitlementForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/LeaveEntitlementForm";
import TransportLimitsForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/TransportLimitsForm";
import WorkingHoursForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/WorkingHoursForm";
import { createEntAction } from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/createEntAction";
import AutocompleteNoticeDay from "@/components/autocomplete/AutocompleteNoticeDay";
import useAction from "@/hooks/useAction";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/ui/Accordion";
import Button from "@/ui/Button";
import Card from "@/ui/Card";
import { Form, FormRow, FormSection, showFormErrors } from "@/ui/Form";
import HStack from "@/ui/HStack";
import Select from "@/ui/Select";
import Tooltip from "@/ui/Tooltip";
import VStack from "@/ui/VStack";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import { Numerical } from "@blueprintjs/icons";
import { isPlainObject } from "lodash";
import {
  BadgeDollarSignIcon,
  ChevronRight,
  ChevronsDownUpIcon,
  ChevronsUpDownIcon,
  ClockIcon,
  FuelIcon,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export default function NewEntForm({ project }) {
  const allAccordionValues = ["leave", "working-hours", "claims", "transport"];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const params = useParams();
  const form = useForm({
    defaultValues: {
      workingHours: [],
      claimLimits: [],
      transportLimits: [],
      carryForward: project.carryForward,
    },
  });
  const { setError } = form;

  const companyId = params.companyId;
  const projectId = params.projectId;

  const [execute, pending] = useAction(createEntAction, {
    onSuccess: () => {
      toast.success("Entitlement created successfully");
      router.push(
        `/ws/companies/${companyId}/projects/${projectId}/entitlements`,
      );
    },
    onError: ({ userErrors }) => {
      const errorKeys = userErrors.map((e) => e.path[0]);
      if (
        ["alEntitledDays", "mlEntitledDays", "fclEntitledDays"].some((value) =>
          errorKeys.includes(value),
        )
      )
        errorKeys.push("leave");

      setAccordionValues(errorKeys);
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    const { workingHours, claimLimits, transportLimits, ...restOfInput } =
      input;

    // Normalize {time} and {min} to its primitive value
    const workingHourInputs = workingHours.map((workingHour) => {
      return {
        ...workingHour,
        workingHourFrom: workingHour.workingHourFrom?.value,
        breakDuration: workingHour.breakDuration?.value,
        workingHourTo: workingHour.workingHourTo?.value,
        workingDays: workingHour.workingDays,
        description: workingHour.description,
        name: workingHour.name,
      };
    });

    const newInput = {
      ...restOfInput,
      noticeDays: restOfInput.noticeDays?.value,
      probationNoticeDays: restOfInput.probationNoticeDays?.value,
      carryForwardValidMonths: isPlainObject(
        restOfInput.carryForwardValidMonths,
      )
        ? restOfInput.carryForwardValidMonths?.value
        : project?.carryForwardValidMonths,
      maxCarryForwardDays: isPlainObject(restOfInput.maxCarryForwardDays)
        ? restOfInput.maxCarryForwardDays?.value
        : project?.maxCarryForwardDays,
    };

    execute({
      projectId,
      entInput: newInput,
      workingHourInputs,
      claimLimitInputs: claimLimits,
      transportLimitInputs: transportLimits,
    });
  };

  return (
    <div className="space-y-4 p-4">
      <div className="mx-auto max-w-[800px]">
        <div className="mb-8 space-y-4">
          <h1 className="text-2xl font-bold">New entitlement</h1>
          <p>
            {
              "The entitlement values outlined here serve as the contract T&C to be delivered to the project's contractors. Please ensure you've exhausted all usable entitlement before creating a new one."
            }
          </p>
        </div>

        <Form
          form={form}
          onSubmit={onSubmit}
          formData={formData}
          debug
          className="space-y-4"
        >
          <HStack className="mx-auto max-w-[200px] flex-nowrap justify-between">
            <Button type="submit" fullWidth loading={pending}>
              Create
            </Button>
            <HStack className="flex-nowrap gap-1">
              <Tooltip content="Expand" sideOffset={4}>
                <Button
                  prefix={<ChevronsUpDownIcon strokeWidth={1.5} />}
                  variant="bgIcon"
                  onClick={() => setAccordionValues(allAccordionValues)}
                />
              </Tooltip>

              <Tooltip content="Collapse" sideOffset={4}>
                <Button
                  prefix={<ChevronsDownUpIcon strokeWidth={1.5} />}
                  variant="bgIcon"
                  onClick={() => setAccordionValues([])}
                />
              </Tooltip>
            </HStack>
          </HStack>

          <FormSection>
            <TiptapEditor
              name="description"
              label="ENT description"
              rules={{ required: "Description is required!" }}
            />
          </FormSection>

          <FormSection title="Notice period for termination of employment">
            <FormRow colsCount={2}>
              <AutocompleteNoticeDay label="Notice period" name="noticeDays" />
            </FormRow>
          </FormSection>

          <FormSection title="Notice period during probation (optional)">
            <FormRow colsCount={2}>
              <Select
                label="Probation duration (in months)"
                {...form.register("probationMonths", { valueAsNumber: true })}
              >
                <option value=""></option>
                <option value="3">3 months</option>
                <option value="6">6 months</option>
              </Select>
              <AutocompleteNoticeDay
                label="Notice period during probation"
                name="probationNoticeDays"
              />
            </FormRow>
          </FormSection>

          <Accordion
            type="multiple"
            value={accordionValues}
            onValueChange={(values) => setAccordionValues(values)}
          >
            <AccordionItem value="leave" outline={false}>
              <AccordionTrigger
                className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                arrow={false}
              >
                <ChevronRight
                  className="accordion-arrow transition-transform duration-150"
                  strokeWidth={1.5}
                />
                <div className="text-amber-600">
                  <Numerical size={24} />
                </div>
                <VStack className="gap-0">
                  <h2 className="text-lg leading-none font-semibold">
                    Entitled Leaves
                  </h2>
                  <p className="text-sm font-normal text-muted">
                    Set up the number of entitled leaves and the carry-forward
                    policy.
                  </p>
                </VStack>
              </AccordionTrigger>
              <AccordionContent className="pl-14">
                <div className="py-2">
                  <Card>
                    <LeaveEntitlementForm project={project} />
                  </Card>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="workingHours" outline={false}>
              <AccordionTrigger
                className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                arrow={false}
              >
                <ChevronRight
                  className="accordion-arrow transition-transform duration-150"
                  strokeWidth={1.5}
                />

                <div className="text-pink-600">
                  <ClockIcon size={24} strokeWidth={1.5} />
                </div>

                <VStack className="gap-0">
                  <h2 className="text-lg leading-none font-semibold">
                    Working Hours &amp; Overtime Settings
                  </h2>
                  <p className="text-sm font-normal text-muted">
                    Configure the working hours and overtime settings.
                  </p>
                </VStack>
              </AccordionTrigger>
              <AccordionContent className="pl-14">
                <div className="py-2">
                  <Card>
                    <WorkingHoursForm />
                  </Card>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="claimLimits" outline={false}>
              <AccordionTrigger
                className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                arrow={false}
              >
                <ChevronRight
                  className="accordion-arrow transition-transform duration-150"
                  strokeWidth={1.5}
                />

                <div className="text-purple-600">
                  <BadgeDollarSignIcon size={24} strokeWidth={1.5} />
                </div>

                <VStack className="gap-0">
                  <h2 className="text-lg leading-none font-semibold">
                    Claim Limits &amp; Medical Claim Amounts
                  </h2>
                  <p className="text-sm font-normal text-muted">
                    Determine what types of claims are allowed and the maximum
                    limits.
                  </p>
                </VStack>
              </AccordionTrigger>
              <AccordionContent className="pl-14">
                <div className="py-2">
                  <Card>
                    <ClaimLimitsForm />
                  </Card>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="transportLimits" outline={false}>
              <AccordionTrigger
                className="grid grid-cols-[16px_24px_1fr] gap-2 border-b py-2 text-justify [&[data-state=open]>.accordion-arrow]:rotate-90"
                arrow={false}
              >
                <ChevronRight
                  className="accordion-arrow transition-transform duration-150"
                  strokeWidth={1.5}
                />

                <div className="text-blue-600">
                  <FuelIcon size={24} strokeWidth={1.5} />
                </div>

                <VStack className="gap-0">
                  <h2 className="text-lg leading-none font-semibold">
                    Transport Rates
                  </h2>
                  <p className="text-sm font-normal text-muted">
                    Set the claimable mileage.
                  </p>
                </VStack>
              </AccordionTrigger>
              <AccordionContent className="pl-14">
                <div className="py-2">
                  <Card>
                    <TransportLimitsForm />
                  </Card>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="mx-auto max-w-[200px]">
            <Button type="submit" fullWidth loading={pending}>
              Create
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
}
