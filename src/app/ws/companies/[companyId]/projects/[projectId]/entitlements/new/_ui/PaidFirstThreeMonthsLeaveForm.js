import { FormRow } from "@/ui/Form";
import Switch from "@/ui/Switch";
import VStack from "@/ui/VStack";
import { useFormContext, useWatch } from "react-hook-form";

export default function PaidFirstThreeMonthsLeaveForm() {
  const { register } = useFormContext();
  const watchAllow3mPaidLeaves = useWatch({ name: "allow3mPaidLeaves" });

  return (
    <VStack className="gap-1">
      <p>
        <strong>First 3 months:</strong> Specify if the first 3 months can take
        paid or unpaid AL.
      </p>

      <FormRow>
        <label className="inline-flex w-fit items-center gap-2">
          <Switch {...register("allow3mPaidLeaves")} />
          <span className="text-sm text-muted">
            {watchAllow3mPaidLeaves ? (
              <span>
                Can take <span className="text-green-600">entitled paid</span>{" "}
                AL during the first 3 months.
              </span>
            ) : (
              <span>
                Can only take <span className="text-rose-500">unpaid</span> AL
                during the first 3 months.
              </span>
            )}
          </span>
        </label>
      </FormRow>
    </VStack>
  );
}
