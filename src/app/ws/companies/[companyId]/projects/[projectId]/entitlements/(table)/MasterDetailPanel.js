"use client";

import { PanelGroup, Panel, PanelResizeHandle } from "react-resizable-panels";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useQuery } from "urql";
import { updatePersonalPanelColumn } from "@/ui/Panel/actions/updatePersonalPanelColumn";
import { round } from "mathjs";

const QUERY = `
  query PersonalPanelColumn {
    personalPanelColumn(panelName: "PROJECT_ENTITLEMENTS") {
      panelName
      sizes
    }
  }
`;

const DEFAULT_PANEL_SIZES = [40, 60];

export default function MasterDetailPanel({ master, details }) {
  const router = useRouter();
  const params = useParams();
  const panelGroupRef = useRef();
  // For double-clicks action
  const [clickCount, setClickCount] = useState(0);

  const [result, refetch] = useQuery({
    query: QUERY,
  });
  const { sizes: panelSizes = DEFAULT_PANEL_SIZES } =
    result.data?.personalPanelColumn || {};

  // This will only be invoked on mouse click or release, it will not during dragging
  // When start dragging, this will invoke with isDragging=true.
  // After done dragging, isDragging will be `false`
  const handleResize = async (isDragging) => {
    // The panel sizes having too many precisions [56.0802271833, 43.9197728167]
    // We do not update for too tiny changes, like 0.03
    // Thus we round up the sizes to whole number
    const sizes = panelGroupRef.current.getLayout().map((s) => round(s));
    const stringifiedPanelSizes = JSON.stringify(panelSizes);
    const stringifiedSizes = JSON.stringify(sizes);
    if (isDragging && stringifiedSizes !== "[50, 50]") {
      setClickCount((c) => c + 1);
    }

    setTimeout(() => setClickCount(0), [250]); // 250ms for double click speed

    const isSizesDifferent = stringifiedPanelSizes !== stringifiedSizes;

    if (!isDragging && isSizesDifferent) {
      await updatePersonalPanelColumn({
        panelName: "PROJECT_ENTITLEMENTS",
        sizes,
      });

      // I am still not sure why router refresh doesn't refetch the query
      // router.refresh();
      refetch();
    }
  };

  // Double-click to split the panels by 50:50
  useEffect(() => {
    if (clickCount === 2) {
      panelGroupRef.current.setLayout([50, 50]);
      // handleResize(false);
    }
  }, [clickCount]);

  // I still not sure why this line is needed
  // Without this line, the PanelGroup will not take the saved panel sizes and will reset to default sizes
  if (result.data?.personalPanelColumn === undefined) return null;

  // Need to use `h-full` instead of `h-screen` for scrolling to work
  return (
    <PanelGroup
      ref={panelGroupRef}
      direction="horizontal"
      style={{ height: "calc(100% - 95px)" }}
    >
      <Panel
        id="master"
        order={1}
        defaultSize={params?.entId ? panelSizes[0] : 100}
        minSize={30}
      >
        <div className="h-full overflow-auto">{master}</div>
      </Panel>

      {params?.entId && (
        <>
          <PanelResizeHandle
            className="flex items-stretch justify-stretch"
            onDragging={handleResize}
          >
            <div className="my-2 w-2 rounded-full bg-border/50 transition-colors hover:bg-border"></div>
          </PanelResizeHandle>

          <Panel
            id="details"
            order={2}
            defaultSize={panelSizes[1]}
            minSize={30}
          >
            <div className="relative h-full overflow-auto bg-background">
              {details}
            </div>
          </Panel>
        </>
      )}
    </PanelGroup>
  );
}
