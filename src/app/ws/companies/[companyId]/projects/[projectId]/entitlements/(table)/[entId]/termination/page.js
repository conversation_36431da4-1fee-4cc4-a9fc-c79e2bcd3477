import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import RawHtml from "@/ui/RawHtml";
import HStack from "@/ui/HStack";
import EditTemplateDialog from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_ui/EditTemplateDialog";

const QUERY = `
  query ent($id: ID!) {
    ent(id: $id) {
      id
      terminationTemplate
    }
  }
`;

export default async function Page({ params }) {
  const { entId } = await params;
  const res = await apiQuery(QUERY, { id: entId });
  const ent = res.data.ent;
  if (ent === null) notFound();

  return (
    <div className="mx-auto max-w-[1080px]">
      <HStack className="justify-end">
        <EditTemplateDialog entId={entId} defaultTab="Termination" />
      </HStack>
      <RawHtml className="text-sm">{ent.terminationTemplate}</RawHtml>
    </div>
  );
}
