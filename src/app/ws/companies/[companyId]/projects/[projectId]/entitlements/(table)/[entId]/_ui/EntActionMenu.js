"use client";

import EditEntDialog from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_ui/EditEntDialog";
import PreviewContractAgreementDialog from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_ui/PreviewContractAgreementDialog";
import Button from "@/ui/Button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuEmptyIcon,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import { EllipsisVertical, PlusIcon, View } from "lucide-react";
import { useParams } from "next/navigation";
import { useState } from "react";

export default function EntActionMenu({ ent }) {
  const { companyId, projectId } = useParams();
  const [openPreviewDialog, setOpenPreviewDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const baseUrl = `/ws/companies/${companyId}/projects/${projectId}/entitlements`;

  const setOpenEditDialog2 = () => {
    setOpenEditDialog((prevState) => !prevState);
    setTimeout(() => (document.body.style.pointerEvents = ""), 0);
  };

  return (
    <>
      <PreviewContractAgreementDialog
        key={Math.random()}
        ent={ent}
        open={openPreviewDialog}
        setOpen={setOpenPreviewDialog}
      />

      {openEditDialog && (
        <EditEntDialog
          entId={ent.id}
          open={openEditDialog}
          setOpen={setOpenEditDialog2}
        />
      )}

      <DropdownMenu>
        <DropdownMenuTrigger>
          <Button
            variant="plainIcon"
            className="gap-0"
            prefix={<EllipsisVertical size={24} />}
          />
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={setOpenEditDialog2}>
            <DropdownMenuEmptyIcon />
            Edit...
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={() => setOpenPreviewDialog((prevState) => !prevState)}
          >
            <View size={20} strokeWidth={1.5} />
            Preview how CAG PDF will look like...
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem href={`${baseUrl}/new`}>
            <PlusIcon color="green" size={20} />
            New entitlement...
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
