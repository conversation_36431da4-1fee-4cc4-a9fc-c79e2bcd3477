import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import EntAccordion from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_ui/EntAccordion";
import Card from "@/ui/Card";

const QUERY = `
  query ent($id: ID!) {
    ent(id: $id) {
      id
      alEntitledDays
      mlEntitledDays
      fclEntitledDays

      carryForward
      carryForwardValidMonths
      maxCarryForwardDays

      maxMedicalClaimAmount

      allow3mPaidLeaves
      earnedLeaves

      hasOvertime
      overtimePeriod
      overtimeThreshold
      overtimeDescription

      workingDays
      workingHoursDescription
      breakDurationDescription
      workingHours {
        id
        workingHourFrom
        workingHourTo
        breakDuration
        workingDays
        displayWorkingDays
        name
        description
        formattedBreakDuration
        formattedWorkingDays
      }

      claimLimits {
        id
        claimMethod
        claimType
        subClaimType
        maxAmount
        period
      }

      transportLimits {
        id
        transportMode
        transportRate
        status
      }
    }
  }
`;

export default async function Page({ params }) {
  const { entId } = await params;
  const res = await apiQuery(QUERY, { id: entId });
  const ent = res.data.ent;
  if (ent === null) notFound();

  return (
    <Card className="mx-auto max-w-[800px] space-y-4">
      <EntAccordion ent={ent} />
    </Card>
  );
}
