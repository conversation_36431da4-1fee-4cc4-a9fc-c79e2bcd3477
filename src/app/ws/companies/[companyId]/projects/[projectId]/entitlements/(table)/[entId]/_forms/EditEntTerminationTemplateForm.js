import { Form, FormSection, showFormErrors } from "@/ui/Form";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import { useState } from "react";
import { useForm } from "react-hook-form";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";

const MUTATION = `
  mutation updateEntTerminationTemplate($id: ID!, $terminationTemplate: String) {
    execute:updateEntTerminationTemplate(id: $id, terminationTemplate: $terminationTemplate) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Ent {
          id
        }
      }
    }
  }
`;

export default function EditEntTerminationTemplateForm({ ent }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm();
  const { setError } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Entitlement updated successfully");
      router.refresh();
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    execute({
      id: ent.id,
      terminationTemplate: input.terminationTemplate,
    });
  };

  return (
    <div className="space-y-4">
      <Form form={form} onSubmit={onSubmit} formData={formData}>
        <FormSection>
          <HStack className="justify-between">
            <h1 className="text-lg font-bold">Termination</h1>

            <Button type="submit" loading={pending}>
              Save termination template
            </Button>
          </HStack>

          <TiptapEditor
            name="terminationTemplate"
            label="Termination template"
            rules={{ required: "Template body is required!" }}
            defaultValue={ent.terminationTemplate}
            stickyTop="70px"
          />

          <HStack className="justify-end">
            <Button type="submit" loading={pending}>
              Save termination template
            </Button>
          </HStack>
        </FormSection>
      </Form>
    </div>
  );
}
