import { Form, FormSection, showFormErrors } from "@/ui/Form";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import { useState } from "react";
import { useForm } from "react-hook-form";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";

const MUTATION = `
  mutation updateEntBenefitsTemplate($id: ID!, $benefitsTemplate: String) {
    execute:updateEntBenefitsTemplate(id: $id, benefitsTemplate: $benefitsTemplate) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Ent {
          id
        }
      }
    }
  }
`;

export default function EditEntBenefitsTemplateForm({ ent }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm();
  const { setError } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Entitlement updated successfully");
      router.refresh();
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    execute({
      id: ent.id,
      benefitsTemplate: input.benefitsTemplate,
    });
  };

  return (
    <div className="space-y-4">
      <Form form={form} onSubmit={onSubmit} formData={formData}>
        <FormSection>
          <HStack className="justify-between">
            <h1 className="text-lg font-bold">Benefits</h1>

            <Button type="submit" loading={pending}>
              Save benefits template
            </Button>
          </HStack>

          <TiptapEditor
            name="benefitsTemplate"
            label="Benefits template"
            rules={{ required: "Template body is required!" }}
            defaultValue={ent.benefitsTemplate}
            stickyTop="70px"
          />

          <HStack className="justify-end">
            <Button type="submit" loading={pending}>
              Save benefits template
            </Button>
          </HStack>
        </FormSection>
      </Form>
    </div>
  );
}
