import { CollectionForm, FormBody, FormRow, FormSection } from "@/ui/Form";
import { RadioGroup, RadioGroupItem } from "@/ui/RadioGroup";
import StrictNumberInput from "@/ui/StrictNumberInput";
import { useFieldArray, useFormContext } from "react-hook-form";

export default function TransportLimitsForm() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  const {
    fields: transportLimits,
    append,
    remove,
  } = useFieldArray({
    name: "transportLimits",
  });

  return (
    <FormBody>
      <FormSection title={`Mileage (${transportLimits.length})`}>
        <CollectionForm
          items={transportLimits}
          add={append}
          remove={remove}
          maxItems={50}
          allowZeroItems
        >
          {(_, index) => {
            return (
              <FormSection>
                <FormRow>
                  <RadioGroup
                    name={`transportLimits[${index}].transportMode`}
                    rules={{ required: true }}
                    orientation="horizontal"
                    errorMessage={
                      errors.transportLimits?.[index]?.transportMode
                    }
                  >
                    <label className="inline-flex w-fit items-center gap-2">
                      <RadioGroupItem value="Car" /> Car
                    </label>
                    <label className="inline-flex w-fit items-center gap-2">
                      <RadioGroupItem value="Bike" /> Bike
                    </label>
                  </RadioGroup>
                </FormRow>
                <FormRow colsCount={2}>
                  <StrictNumberInput
                    label="Rate"
                    {...register(`transportLimits[${index}].transportRate`, {
                      required: true,
                      valueAsNumber: true,
                    })}
                    min={0}
                    max={3}
                    step={0.01}
                    error={errors.transportLimits?.[index]?.transportRate}
                  />
                </FormRow>
              </FormSection>
            );
          }}
        </CollectionForm>
      </FormSection>
    </FormBody>
  );
}
