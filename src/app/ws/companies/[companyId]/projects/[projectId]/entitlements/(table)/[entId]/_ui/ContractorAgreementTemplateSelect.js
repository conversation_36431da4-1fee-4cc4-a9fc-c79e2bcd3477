"use client";

import Select from "@/ui/Select";
import { useQuery } from "urql";

const QUERY = `
  query employmentAgreementTemplateNames{
    employmentAgreementTemplateNames {
      nodes {
        id
        name
      }
    }
  }
`;

export default function ContractorAgreementTemplateSelect(props) {
  const [result] = useQuery({ query: QUERY });

  const { data, fetching } = result;
  const items = data?.employmentAgreementTemplateNames?.nodes || [];

  return (
    <Select loading={fetching} label="CAG template" {...props}>
      <option value="">Choose a template</option>
      {items.map((item) => (
        <option key={item.id} value={item.id}>
          {item.name}
        </option>
      ))}
    </Select>
  );
}
