"use server";

import apiMutation from "@/lib/apiMutation";
import { retrieveActionResult } from "@/lib/mutationResponse";

const MUTATION = `
  mutation updateEnt(
    $id: ID!,
    $entInput: EntInput!,
    $workingHourInputs: [WorkingHourInput!]
    $claimLimitInputs: [ClaimLimitInput!]
    $transportLimitInputs: [TransportLimitInput!]
  ) {
    updateEnt(
      id: $id,
      entInput: $entInput,
      workingHourInputs: $workingHourInputs,
      claimLimitInputs: $claimLimitInputs,
      transportLimitInputs: $transportLimitInputs
    ) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Ent {
          id
        }
      }
    }
  }
`;

export async function updateEntAction({
  id,
  entInput,
  workingHourInputs,
  claimLimitInputs,
  transportLimitInputs,
}) {
  // Return it to get back the response
  const res = await apiMutation(MUTATION, {
    id,
    entInput,
    workingHourInputs,
    claimLimitInputs,
    transportLimitInputs,
  });

  return retrieveActionResult(res, "updateEnt");
}

// const [execute, pending] = useAction(boundAction(MUTATION), {
//   onSuccess: () => {
//     router.refresh();
//     toast.success("Company description updated successfully");
//     setOpen(false);
//   },
//   onError: ({ userErrors }) => {
//     showFormErrors({ userErrors, setError });
//   },
// });
