"use client";

import ContractorAgreementTemplateSelect from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_ui/ContractorAgreementTemplateSelect";
import Button from "@/ui/Button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogHeader,
} from "@/ui/Dialog";
import { Form, FormRow } from "@/ui/Form";
import { isEmpty } from "lodash";
import { DownloadIcon } from "lucide-react";
import dynamic from "next/dynamic";
import { useState } from "react";
import { useForm } from "react-hook-form";

const PdfViewer = dynamic(() => import("@/ui/PdfViewer"), {
  ssr: false,
});

export default function PreviewContractAgreementDialog({ ent, open, setOpen }) {
  const [previewLoading, setPreviewLoading] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [blob, setBlob] = useState(null);
  const [selectedTemplateId, setSelectedTemplateId] = useState();
  const form = useForm();
  const { register } = form;

  const hasSelectedTemplate = !isEmpty(selectedTemplateId);

  const onSubmit = (input) => {
    const url = `${process.env.NEXT_PUBLIC_JOBLINE_NEXT_API_URL}/preview_pdf/preview`;
    setPreviewLoading(true);

    fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        klass: "Emp::PreviewPdf::NotionalCag",
        filename: `preview-notional-${ent.id}.pdf`,
        templateId: input.templateId,
        entId: ent.id,
      }),
      credentials: "include",
    })
      .then((response) => {
        if (response.ok) {
          response.blob().then((blob) => {
            setBlob(blob);
          });
        }
      })
      .finally(() => {
        setPreviewLoading(false);
      });
  };

  const onDownload = () => {
    const url = `${process.env.NEXT_PUBLIC_JOBLINE_NEXT_API_URL}/preview_pdf/preview`;
    setDownloadLoading(true);

    const filename = `preview-notional-${ent.id}.pdf`;

    fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        klass: "Emp::PreviewPdf::NotionalCag",
        filename,
        templateId: selectedTemplateId,
        entId: ent.id,
      }),
      credentials: "include",
    })
      .then((response) => {
        if (response.ok) {
          response.blob().then((blob) => {
            const href = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = href;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(href);
            document.body.removeChild(a);
          });
        }
      })
      .finally(() => {
        setDownloadLoading(false);
      });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          Preview how CAG PDF will look like using a notional example
        </DialogHeader>

        <section className="space-y-4 py-4">
          <p>
            Choose a CAG template to preview how this ENT will look like using a
            notional employment.
          </p>

          <Form form={form} onSubmit={onSubmit}>
            <FormRow colsCount={2}>
              <ContractorAgreementTemplateSelect
                {...register("templateId")}
                onChange={(e) => setSelectedTemplateId(e.target.value)}
              />
              <div className="grid grid-cols-[1fr_100px] gap-4">
                <Button
                  variant="secondary"
                  type="submit"
                  loading={previewLoading}
                  disabled={!hasSelectedTemplate}
                >
                  Preview
                </Button>
                <Button
                  variant="secondary"
                  outline
                  loading={downloadLoading}
                  disabled={!hasSelectedTemplate}
                  onClick={onDownload}
                  prefix={<DownloadIcon strokeWidth={1.5} />}
                />
              </div>
            </FormRow>
          </Form>
        </section>

        {blob && (
          <div className="flex justify-center p-2">
            <PdfViewer blob={blob} loading={previewLoading} />
          </div>
        )}

        <DialogFooter>
          <div className="flex w-full justify-end gap-3">
            <DialogDismissButton />
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
