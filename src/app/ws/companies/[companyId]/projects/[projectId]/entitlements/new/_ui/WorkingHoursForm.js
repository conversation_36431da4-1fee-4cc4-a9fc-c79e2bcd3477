import OvertimeSettingsForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/OvertimeSettingsForm";
import WorkingHourVariantsForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/WorkingHourVariantsForm";
import WorkingDaySelect from "@/components/select/WorkingDaySelect";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import { FormBody, FormRow, FormSection } from "@/ui/Form";
import { useFormContext } from "react-hook-form";

export default function WorkingHoursForm({ ent }) {
  const { register } = useFormContext();

  return (
    <FormBody>
      <FormSection
        title="Working days"
        description="Select the days of the week that are considered working days."
      >
        <FormRow colsCount={2}>
          <WorkingDaySelect {...register("workingDays")} />
        </FormRow>
      </FormSection>

      <WorkingHourVariantsForm />

      <FormSection
        title="Working hours description"
        description="Provide additional information that the variants can't address."
      >
        <FormRow>
          <TiptapEditor
            name="workingHoursDescription"
            label="Working hours description"
            defaultValue={ent?.workingHoursDescription}
          />
        </FormRow>
      </FormSection>

      <FormSection
        title="Break duration description"
        description="Provide additional information on break duration (lunch and dinner)."
      >
        <FormRow>
          <TiptapEditor
            name="breakDurationDescription"
            label="Break duration description"
            defaultValue={ent?.breakDurationDescription}
          />
        </FormRow>
      </FormSection>

      <OvertimeSettingsForm resource={ent} />
    </FormBody>
  );
}
