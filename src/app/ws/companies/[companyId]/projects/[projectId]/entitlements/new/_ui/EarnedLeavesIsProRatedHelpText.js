import Button from "@/ui/Button";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import VStack from "@/ui/VStack";
import { CircleHelp } from "lucide-react";

export default function EarnedLeavesIsProRatedHelpText() {
  return (
    <Popover>
      <PopoverTrigger>
        <Button
          variant="plainIcon"
          prefix={<CircleHelp size={16} />}
          className="text-muted"
        />
      </PopoverTrigger>
      <PopoverContent className="max-w-md bg-yellow-50 p-4 text-sm dark:border-transparent dark:text-black">
        <VStack className="gap-4">
          <h1 className="font-bold">
            Example: Contractor entitled to 12 days per year
          </h1>
          <p>
            If 7 days of leaves are taken by the 6th months, only 6 days will be
            paid and 1 day will be unpaid.
          </p>
        </VStack>
      </PopoverContent>
    </Popover>
  );
}
