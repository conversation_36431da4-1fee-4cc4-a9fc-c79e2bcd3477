"use client";

import { Form, FormSection, showFormErrors } from "@/ui/Form";
import Button from "@/ui/Button";
import { useState } from "react";
import { toast } from "sonner";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import HStack from "@/ui/HStack";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";

const MUTATION = `
  mutation updateEntRemark($id: ID!, $remark: String) {
    updateEntRemark(id: $id, remark: $remark) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Ent {
          id
        }
      }
    }
  }
`;

// Deprecated, not being used. But don't remove it!
export default function EditRemarkForm({ ent }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm();

  const { setError } = form;

  const [execute, pending] = useAction(
    boundAction(MUTATION, "updateEntRemark"),
    {
      onSuccess: () => {
        toast.success("Entitlement updated successfully");
        router.refresh();
      },
      onError: ({ userErrors }) => {
        showFormErrors({ userErrors, setError });
      },
    },
  );

  const onSubmit = (input) => {
    setFormData(input);

    execute({
      id: ent.id,
      remark: input.remark,
    });
  };

  return (
    <Form form={form} onSubmit={onSubmit} formData={formData}>
      <FormSection title="Give your entitlement a remark for easy recognition">
        <TiptapEditor
          name="remark"
          label="Remark"
          rules={{ required: "Remark is required!" }}
          defaultValue={ent.remark}
        />

        <HStack className="justify-end">
          <Button type="submit" loading={pending}>
            Save remark
          </Button>
        </HStack>
      </FormSection>
    </Form>
  );
}
