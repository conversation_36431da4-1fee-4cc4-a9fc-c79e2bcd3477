import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import NewEntForm from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/NewEntForm";

const PROJECT_QUERY = `
  query project($id: ID!) {
    project(id: $id) {
      id
      carryForward
      carryForwardValidMonths
      maxCarryForwardDays
    }
  }
`;

export default async function Page({ params }) {
  const { projectId } = await params;
  const res = await apiQuery(PROJECT_QUERY, { id: projectId });
  const project = res.data.project;

  if (project === null) notFound();

  return <NewEntForm project={project} />;
}
