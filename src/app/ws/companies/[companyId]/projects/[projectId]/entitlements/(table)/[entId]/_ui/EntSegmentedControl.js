"use client";

import {
  BenefitsIcon,
  InsuranceIcon,
  TerminationIcon,
} from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_icons";
import HStack from "@/ui/HStack";
import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";
import { Function } from "@blueprintjs/icons";
import { useParams, usePathname } from "next/navigation";

export default function EntSegmentedControl() {
  const { companyId, projectId, entId } = useParams();
  const pathname = usePathname();
  const baseUrl = `/ws/companies/${companyId}/projects/${projectId}/entitlements/${entId}`;

  return (
    <SegmentedControlRoot
      value={pathname}
      style={{ "--segmented-control-border-radius": "9999px" }}
    >
      <SegmentedControlItem href={`${baseUrl}`}>
        <HStack>
          <Function size={20} />
          Ent Values
        </HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/insurance`}>
        <HStack>
          <InsuranceIcon className="fill-green-600 dark:fill-green-500 dark:stroke-green-100" />
          Insurance
        </HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/benefits`}>
        <HStack>
          <BenefitsIcon className="fill-rose-600 dark:fill-rose-200 dark:stroke-rose-500" />
          Benefits
        </HStack>
      </SegmentedControlItem>
      <SegmentedControlItem href={`${baseUrl}/termination`}>
        <HStack>
          <TerminationIcon className="fill-gray-600 dark:fill-gray-300" />
          Termination
        </HStack>
      </SegmentedControlItem>
    </SegmentedControlRoot>
  );
}
