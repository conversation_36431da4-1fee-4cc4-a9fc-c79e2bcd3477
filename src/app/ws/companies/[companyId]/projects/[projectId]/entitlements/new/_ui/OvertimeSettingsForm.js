import { FormRow, FormSection } from "@/ui/Form";
import Select from "@/ui/Select";
import Switch from "@/ui/Switch";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import { useFormContext, useWatch } from "react-hook-form";

export default function OvertimeSettingsForm({ resource }) {
  const { register } = useFormContext();

  const watchOvertimePeriod = useWatch({ name: "overtimePeriod" });
  const watchHasOvertime = useWatch({ name: "hasOvertime" });

  return (
    <FormSection title="Overtime" description="Configure overtime settings.">
      <label className="inline-flex w-fit items-center gap-2">
        <Switch {...register("hasOvertime")} />
        <span className="text-sm text-muted">
          {watchHasOvertime ? (
            <span className="text-green-600">
              There will be overtime entitlement
            </span>
          ) : (
            <span className="text-rose-500">
              There will be no overtime entitlement
            </span>
          )}
        </span>
      </label>

      {watchHasOvertime && (
        <>
          <FormRow colsCount={2}>
            <Select
              label="Overtime period"
              {...register("overtimePeriod", {
                deps: ["overtimeDescription"],
              })}
            >
              <option value=""></option>
              <option value="Daily">Daily</option>
              <option value="Weekly">Weekly</option>
            </Select>

            {["Daily"].includes(watchOvertimePeriod) && (
              <Select
                label="Overtime threshold"
                {...register("overtimeThreshold", { valueAsNumber: true })}
              >
                <optgroup label="Daily">
                  <option value="7">7 hrs</option>
                  <option value="8">8 hrs</option>
                  <option value="8.5">8.5 hrs</option>
                  <option value="8.75">8.75 hrs</option>
                  <option value="8.8">8.8 hrs</option>
                </optgroup>
              </Select>
            )}

            {["Weekly"].includes(watchOvertimePeriod) && (
              <Select
                label="Overtime threshold"
                {...register("overtimeThreshold", { valueAsNumber: true })}
              >
                <optgroup label="Weekly">
                  <option value="11">11 hrs</option>
                  <option value="40">40 hrs</option>
                  <option value="44">44 hrs</option>
                </optgroup>
              </Select>
            )}
          </FormRow>

          <FormSection
            title="Overtime description"
            description="Provide additional information on overtime."
          >
            <FormRow>
              <TiptapEditor
                name="overtimeDescription"
                label="Overtime description"
                defaultValue={resource?.overtimeDescription}
              />
            </FormRow>
          </FormSection>
        </>
      )}
    </FormSection>
  );
}
