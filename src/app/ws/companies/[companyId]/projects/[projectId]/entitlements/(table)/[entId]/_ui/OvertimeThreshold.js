export default function OvertimeThreshold({
  overtimePeriod,
  overtimeThreshold,
}) {
  if (!overtimePeriod || !overtimeThreshold) return null;

  return (
    <div>
      <h1 className="font-bold">Overtime Threshold</h1>

      <p>
        Overtime will be calculated{" "}
        <strong className="font-semibold">
          {overtimePeriod.toLowerCase()}
        </strong>{" "}
        after{" "}
        <strong className="font-semibold">{overtimeThreshold || 0}</strong>{" "}
        hours of work.
      </p>
    </div>
  );
}
