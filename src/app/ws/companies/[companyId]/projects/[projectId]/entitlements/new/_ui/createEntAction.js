"use server";

import apiMutation from "@/lib/apiMutation";
import { retrieveActionResult } from "@/lib/mutationResponse";

const MUTATION = `
  mutation createEnt(
    $projectId: ID!,
    $entInput: EntInput!,
    $workingHourInputs: [WorkingHourInput!]!
    $claimLimitInputs: [ClaimLimitInput!]!
    $transportLimitInputs: [TransportLimitInput!]!
  ) {
    createEnt(
      projectId: $projectId,
      entInput: $entInput,
      workingHourInputs: $workingHourInputs,
      claimLimitInputs: $claimLimitInputs,
      transportLimitInputs: $transportLimitInputs
    ) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Ent {
          id
        }
      }
    }
  }
`;

export async function createEntAction({
  projectId,
  entInput,
  workingHourInputs,
  claimLimitInputs,
  transportLimitInputs,
}) {
  // Return it to get back the response
  const res = await apiMutation(MUTATION, {
    projectId,
    entInput,
    workingHourInputs,
    claimLimitInputs,
    transportLimitInputs,
  });

  return retrieveActionResult(res, "createEnt");
}
