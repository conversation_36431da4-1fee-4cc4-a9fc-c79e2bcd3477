import NewButton from "@/components/NewButton";
import HStack from "@/ui/HStack";
import EntTable from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/EntTable";

export default async function Page({ params, searchParams }) {
  const { companyId, projectId } = await params;

  return (
    <div className="space-y-4 p-4">
      <HStack>
        <NewButton
          href={`/ws/companies/${companyId}/projects/${projectId}/entitlements/new`}
          title="Create a new entitlement"
          subTitle="Configure leave entitlements, working hours and claim limits"
        />
      </HStack>

      <EntTable searchParams={searchParams} params={params} />
    </div>
  );
}
