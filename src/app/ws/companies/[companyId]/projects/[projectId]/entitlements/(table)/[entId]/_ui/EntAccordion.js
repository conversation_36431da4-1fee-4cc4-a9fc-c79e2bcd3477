"use client";

import EditEntValuesDialog from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_ui/EditEntValuesDialog";
import OvertimeThreshold from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/(table)/[entId]/_ui/OvertimeThreshold";
import EarnedLeavesIsNotProRatedHelpText from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/EarnedLeavesIsNotProRatedHelpText";
import EarnedLeavesIsProRatedHelpText from "@/app/ws/companies/[companyId]/projects/[projectId]/entitlements/new/_ui/EarnedLeavesIsProRatedHelpText";
import QuotedRemark from "@/components/QuotedRemark";
import ExpandCollapseButton from "@/components/accordion-variants/ExpandCollapseButton";
import { currency } from "@/formatters/numeric";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/ui/Accordion";
import Card from "@/ui/Card";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import CurrencyCell from "@/ui/DataGrid/cells/CurrencyCell";
import HStack from "@/ui/HStack";
import { List, PropertyList } from "@/ui/PropertyList";
import VStack from "@/ui/VStack";
import { Numerical } from "@blueprintjs/icons";
import {
  BadgeDollarSignIcon,
  BanIcon,
  ClockIcon,
  FuelIcon,
} from "lucide-react";
import pluralize from "pluralize";
import { useState } from "react";

export default function EntAccordion({ ent }) {
  const allAccordionValues = ["leave", "working-hours", "claims", "transport"];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  return (
    <div>
      <HStack className="flex-nowrap justify-end gap-4">
        <ExpandCollapseButton
          onExpand={() => setAccordionValues(allAccordionValues)}
          onCollapse={() => setAccordionValues([])}
          iconSize={20}
          iconStrokeWidth={2}
        />
        <EditEntValuesDialog entId={ent.id} />
      </HStack>

      <Accordion
        type="multiple"
        value={accordionValues}
        onValueChange={(values) => setAccordionValues(values)}
      >
        <AccordionItem value="leave" outline={false}>
          <AccordionTrigger className="border-b py-2">
            <div className="grid grid-cols-[24px_1fr] gap-2">
              <div className="text-amber-600">
                <Numerical size={24} />
              </div>
              <h2 className="font-bold">Entitled Leaves</h2>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pl-[32px]">
            <div className="py-2">
              <Card className="space-y-4 border-transparent bg-gray-50 p-2 dark:bg-neutral-700">
                <div className="mx-auto grid max-w-[300px] grid-cols-3 gap-4">
                  <VStack className="gap-0.5 text-center">
                    <div className="rounded-t-xl bg-pink-100 py-0.5 text-sm dark:bg-pink-800">
                      AL
                    </div>
                    <div className="rounded-b-xl bg-pink-200 py-2 font-semibold dark:bg-pink-900">
                      {ent.alEntitledDays}
                    </div>
                  </VStack>
                  <VStack className="gap-0.5 text-center">
                    <div className="rounded-t-xl bg-pink-100 py-0.5 text-sm dark:bg-pink-800">
                      ML
                    </div>
                    <div className="rounded-b-xl bg-pink-200 py-2 font-semibold dark:bg-pink-900">
                      {ent.mlEntitledDays || "n/a"}
                    </div>
                  </VStack>
                  <VStack className="gap-0.5 text-center">
                    <div className="rounded-t-xl bg-pink-100 py-0.5 text-sm dark:bg-pink-800">
                      FCL
                    </div>
                    <div className="rounded-b-xl bg-pink-200 py-2 font-semibold dark:bg-pink-900">
                      {ent.fclEntitledDays || "n/a"}
                    </div>
                  </VStack>
                </div>
                <PropertyList size="sm">
                  <List
                    label={
                      <HStack className="text-muted">
                        1st 3 months paid leaves
                      </HStack>
                    }
                  >
                    {ent.allow3mPaidLeaves ? "Entitled" : "Not entitled"}
                  </List>
                  <List
                    label={
                      <HStack className="text-muted">
                        Earned leaves
                        {ent.earnedLeaves ? (
                          <EarnedLeavesIsProRatedHelpText />
                        ) : (
                          <EarnedLeavesIsNotProRatedHelpText />
                        )}
                      </HStack>
                    }
                  >
                    {ent.earnedLeaves ? "Yes" : "No"}
                  </List>
                  <List label="Carry forward AL">
                    {ent.carryForward && (
                      <span>
                        {ent.maxCarryForwardDays}{" "}
                        {pluralize("day", ent.maxCarryForwardDays, false)} of
                        annual leaves can be carried forward and consumed within{" "}
                        {ent.carryForwardValidMonths}{" "}
                        {pluralize("month", ent.carryForwardValidMonths, false)}
                        .
                      </span>
                    )}
                  </List>
                </PropertyList>
              </Card>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="working-hours" outline={false}>
          <AccordionTrigger className="border-b py-2">
            <div className="grid grid-cols-[24px_1fr] gap-2">
              <div className="text-pink-600">
                <ClockIcon size={24} strokeWidth={1.5} />
              </div>

              <h2 className="font-bold">
                Working Hours &amp; Overtime Settings
              </h2>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pl-[32px]">
            <div className="space-y-4 py-2">
              <Card className="space-y-4 border-transparent bg-gray-50 p-2 dark:bg-neutral-700">
                <PropertyList>
                  <List label="Working days">{ent.workingDays}</List>
                </PropertyList>
              </Card>

              <WorkingHourTable workingHours={ent.workingHours} />

              <QuotedRemark title="Working Hours Description">
                {ent.workingHoursDescription}
              </QuotedRemark>

              <QuotedRemark title="Break Duration Description">
                {ent.breakDurationDescription}
              </QuotedRemark>

              <Overtime
                hasOvertime={ent.hasOvertime}
                overtimePeriod={ent.overtimePeriod}
                overtimeThreshold={ent.overtimeThreshold}
                overtimeDescription={ent.overtimeDescription}
              />
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="claims" outline={false}>
          <AccordionTrigger className="border-b py-2">
            <div className="grid grid-cols-[24px_1fr] gap-2">
              <div className="text-purple-600">
                <BadgeDollarSignIcon size={24} strokeWidth={1.5} />
              </div>
              <h2 className="font-bold">
                Claim Limits &amp; Medical Claim Amounts
              </h2>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pl-[32px]">
            <div className="space-y-4 py-2">
              <p>
                <strong>Maximum medical claim amounts:</strong>{" "}
                <span className="text-xl">
                  {currency(ent.maxMedicalClaimAmount)}
                </span>
              </p>
              <ClaimLimitTable claimLimits={ent.claimLimits} />
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="transport" outline={false}>
          <AccordionTrigger className="border-b py-2">
            <div className="grid grid-cols-[24px_1fr] gap-2">
              <div className="text-blue-600">
                <FuelIcon size={24} strokeWidth={1.5} />
              </div>
              <h2 className="font-bold">Transport Rates</h2>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pl-[32px]">
            <div className="space-y-4 py-2">
              <TransportLimitTable transportLimits={ent.transportLimits} />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}

const Overtime = ({
  hasOvertime,
  overtimePeriod,
  overtimeThreshold,
  overtimeDescription,
}) => {
  if (!hasOvertime) return <p>There will be no overtime entitlement.</p>;

  return (
    <>
      <OvertimeThreshold
        overtimePeriod={overtimePeriod}
        overtimeThreshold={overtimeThreshold}
      />

      <QuotedRemark title="Overtime Description">
        {overtimeDescription}
      </QuotedRemark>
    </>
  );
};

const WorkingHourTable = ({ workingHours }) => {
  const defaultTableColumns = [
    {
      columnName: "Name",
      columnWidth: 150,
      propertyName: "name",
    },
    {
      columnName: "From",
      columnWidth: 100,
      propertyName: "workingHourFrom",
    },
    {
      columnName: "To",
      columnWidth: 100,
      propertyName: "workingHourTo",
    },
    {
      columnName: "Break",
      columnWidth: 90,
      propertyName: "formattedBreakDuration",
    },
    {
      columnName: "Days",
      columnWidth: 190,
      propertyName: "formattedWorkingDays",
    },
    {
      columnName: "Description",
      columnWidth: 400,
      propertyName: "description",
    },
  ];

  if (workingHours.length === 0)
    return (
      <HStack className="text-muted">
        <BanIcon size={20} />
        No working hour variants.
      </HStack>
    );

  return (
    <DataGrid.Root>
      <DataGrid.Content>
        <DataGridTable
          numRows={workingHours.length}
          data={workingHours}
          tableColumns={defaultTableColumns}
          defaultTableColumns={defaultTableColumns}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
};

const ClaimLimitTable = ({ claimLimits }) => {
  const defaultTableColumns = [
    {
      columnName: "Method",
      columnWidth: 120,
      propertyName: "claimMethod",
    },
    {
      columnName: "Claim Type",
      columnWidth: 150,
      propertyName: "claimType",
    },
    {
      columnName: "Sub Claim Type",
      columnWidth: 180,
      propertyName: "subClaimType",
    },
    {
      columnName: "Max Amount",
      columnWidth: 120,
      propertyName: "maxAmount",
      cellComponent: CurrencyCell,
    },
    {
      columnName: "Period",
      columnWidth: 150,
      propertyName: "period",
    },
  ];

  if (claimLimits.length === 0)
    return (
      <HStack className="text-muted">
        <BanIcon size={20} />
        No claim limits.
      </HStack>
    );

  return (
    <DataGrid.Root>
      <DataGrid.Content>
        <DataGridTable
          numRows={claimLimits.length}
          data={claimLimits}
          tableColumns={defaultTableColumns}
          defaultTableColumns={defaultTableColumns}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
};

const TransportLimitTable = ({ transportLimits }) => {
  const defaultTableColumns = [
    {
      columnName: "Mode",
      columnWidth: 120,
      propertyName: "transportMode",
    },
    {
      columnName: "Rate",
      columnWidth: 120,
      propertyName: "transportRate",
    },
    {
      columnName: "Status",
      columnWidth: 120,
      propertyName: "status",
    },
  ];

  if (transportLimits.length === 0)
    return (
      <HStack className="text-muted">
        <BanIcon size={20} />
        No transport rate.
      </HStack>
    );

  return (
    <DataGrid.Root>
      <DataGrid.Content>
        <DataGridTable
          numRows={transportLimits.length}
          data={transportLimits}
          tableColumns={defaultTableColumns}
          defaultTableColumns={defaultTableColumns}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
};
