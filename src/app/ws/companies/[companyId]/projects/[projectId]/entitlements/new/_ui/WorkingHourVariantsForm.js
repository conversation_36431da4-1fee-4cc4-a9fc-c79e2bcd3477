import AutocompleteBreakDuration from "@/components/autocomplete/AutocompleteBreakDuration";
import AutocompleteWorkingHour from "@/components/autocomplete/AutocompleteWorkingHour";
import DayToggleForm from "@/components/DayToggleForm";
import {
  appendWithDefaultValues,
  CollectionForm,
  FormRow,
  FormSection,
} from "@/ui/Form";
import Input from "@/ui/Input";
import { useFieldArray, useFormContext } from "react-hook-form";

export default function WorkingHourVariantsForm() {
  const {
    register,
    getValues,
    formState: { errors },
  } = useFormContext();
  const {
    fields: workingHours,
    append: workingHourAppend,
    remove: workingHourRemove,
  } = useFieldArray({
    name: "workingHours",
  });

  return (
    <FormSection
      title={`Working hour variants (${workingHours.length})`}
      description="Remember to name your variants for better assignment later at the Timesheet Form."
    >
      <CollectionForm
        items={workingHours}
        add={appendWithDefaultValues(workingHourAppend, {
          workingHourFrom: null,
          breakDuration: null,
          workingHourTo: null,
          workingDays: [],
        })}
        remove={workingHourRemove}
        maxItems={10}
        allowZeroItems
      >
        {(workingHour, index) => {
          return (
            <FormSection>
              <FormRow>
                <AutocompleteWorkingHour
                  label="Working hour from"
                  name={`workingHours[${index}].workingHourFrom`}
                  defaultValue={getValues(
                    `workingHours[${index}].workingHourFrom`,
                  )}
                  rules={{ required: true }}
                  suffixStyling
                  suffix={<span className="text-sm">12hrs</span>}
                />
                <AutocompleteBreakDuration
                  label="Break duration"
                  name={`workingHours[${index}].breakDuration`}
                  defaultValue={getValues(
                    `workingHours[${index}].breakDuration`,
                  )}
                  suffixStyling
                  suffix={<span className="text-sm">mins</span>}
                />
                <AutocompleteWorkingHour
                  label="Working hour to"
                  name={`workingHours[${index}].workingHourTo`}
                  defaultValue={getValues(
                    `workingHours[${index}].workingHourTo`,
                  )}
                  rules={{ required: true }}
                  suffixStyling
                  suffix={<span className="text-sm">12hrs</span>}
                />
              </FormRow>

              <FormRow colsCount={3}>
                <Input
                  label="Name"
                  data-1p-ignore
                  {...register(`workingHours[${index}].name`, {
                    required: "Name is required!",
                  })}
                  error={errors.workingHours?.[index]?.name}
                />
                <div className="sm:col-span-2">
                  <Input
                    label="Description (optional)"
                    {...register(`workingHours[${index}].description`)}
                  />
                </div>
              </FormRow>

              <DayToggleForm name={`workingHours[${index}].workingDays`} />
            </FormSection>
          );
        }}
      </CollectionForm>
    </FormSection>
  );
}
