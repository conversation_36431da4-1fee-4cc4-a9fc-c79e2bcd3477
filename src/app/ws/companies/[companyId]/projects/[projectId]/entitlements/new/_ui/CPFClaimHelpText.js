import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import VStack from "@/ui/VStack";
import { CircleHelp, ExternalLink } from "lucide-react";

export default function CPFClaimHelpText() {
  return (
    <Popover>
      <PopoverTrigger>
        <Button
          variant="plainIcon"
          prefix={<CircleHelp size={16} />}
          className="ml-auto text-muted"
        />
      </PopoverTrigger>
      <PopoverContent className="max-w-md bg-yellow-50 p-4 text-sm dark:border-transparent dark:text-black">
        <VStack className="gap-4">
          <h1 className="font-bold">
            What is the difference between Allowance and Claim?
          </h1>

          <p>
            Allowance falls under additional wages and will attract CPF
            contributions.
          </p>
          <p>
            Claim made occasionally are not considered wages and do not attract
            CPF contributions.
          </p>

          <HStack className="flex-nowrap gap-1">
            <ExternalLink size={14} />
            <a
              className="text-xs text-link"
              target="_blank"
              href="https://www.cpf.gov.sg/employer/employer-obligations/what-payments-attract-cpf-contributions"
            >
              Original source
            </a>
          </HStack>
        </VStack>
      </PopoverContent>
    </Popover>
  );
}
