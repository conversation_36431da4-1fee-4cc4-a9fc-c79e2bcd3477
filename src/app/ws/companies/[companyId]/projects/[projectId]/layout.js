import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import ObjectSwitcher from "@/components/ObjectSwitcher";
import { SecondaryTabs, Tab } from "@/ui/SecondaryTabs";
import {
  LayoutDashboardIcon,
  UsersIcon,
  KeyRoundIcon,
  SquareFunctionIcon,
  FolderTreeIcon,
} from "lucide-react";

const PROJECT_QUERY = `
  query project($id: ID!) {
    project(id: $id) {
      id
      fmId
      name
      projectNameWithOwnerName
      status
    }
  }
`;

const PROJECTS_QUERY = `
  query projects($companyId: ID, $query: String) {
    objects:projects(companyId: $companyId, query: $query, page: 1, perPage: 100) {
      nodes {
        id
        fmId
        name
        projectNameWithOwnerName

        company {
          id
          fmId
          name
        }
      }
    }
  }
`;

export default async function Layout({ children, params }) {
  const { companyId, projectId } = await params;
  const res = await apiQuery(PROJECT_QUERY, { id: projectId });
  const project = res.data.project;

  if (project === null) notFound();

  const baseUrl = `/ws/companies/${companyId}/projects/${projectId}`;

  // Sticky has been removed because I find the top-[xx] hard to manage safely.
  return (
    <>
      {/* sticky top-[92px] */}
      <div className="z-20 bg-background p-2">
        <ObjectSwitcher
          currentObject={project}
          gqlQuery={PROJECTS_QUERY}
          gqlVariables={{ companyId }}
          emptyMessage="No projects found."
          replacedParamName="projectId"
          label="Choose a project"
          placeholder="Search for a project..."
          routeSegmentSlice={7}
          replacedPosition={5}
          itemName="projectNameWithOwnerName"
          itemId="fmId"
        />
      </div>

      {/* sticky top-[142px]  */}
      <SecondaryTabs className="bg-background pt-0">
        <Tab
          href={`${baseUrl}`}
          exact
          prefix={<LayoutDashboardIcon size={20} strokeWidth={1.5} />}
        >
          Overview
        </Tab>
        <Tab
          href={`${baseUrl}/entitlements`}
          prefix={<SquareFunctionIcon size={20} strokeWidth={1.5} />}
        >
          Ent Values
        </Tab>
        <Tab
          href={`${baseUrl}/files`}
          prefix={<FolderTreeIcon size={20} strokeWidth={1.5} />}
        >
          Project files
        </Tab>
        <Tab
          href={`${baseUrl}/headcount`}
          prefix={<UsersIcon size={20} strokeWidth={1.5} />}
        >
          Headcount
        </Tab>
        <Tab
          href={`${baseUrl}/access-control`}
          prefix={<KeyRoundIcon size={20} strokeWidth={1.5} />}
        >
          Project Access
        </Tab>
      </SecondaryTabs>

      {children}
    </>
  );
}
