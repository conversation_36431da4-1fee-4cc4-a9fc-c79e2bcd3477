import ProjectAccessesTable from "@/app/ws/companies/[companyId]/projects/[projectId]/access-control/_ui/ProjectAccessesTable";
import apiQuery from "@/lib/apiQuery";
import decompressSearchParam from "@/lib/decompressSearchParam";

const QUERY = `
  query projectAccesses($projectId: ID!, $query: String, $sorts: [SortInput!], $page: Int!) {
    projectAccesses(projectId: $projectId, query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        status

        membership {
          id
          fmId
          name
          email
        }

        creator {
          id
          name
        }
        modifier {
          id
          name
        }

        createdAt
        updatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "STAFF_PROJECT_ACCESSES") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

export default async function Page({ searchParams, params }) {
  let { page, query, sorts } = await searchParams;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "updatedAt", desc: true }]);
  const { projectId } = await params;

  const promise = apiQuery(QUERY, {
    projectId,
    query,
    sorts,
    page: currentPage,
  });

  return <ProjectAccessesTable sorts={sorts} findProjectAccesses={promise} />;
}
