"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogFooter<PERSON><PERSON><PERSON><PERSON>,
  DialogHeader,
} from "@/ui/Dialog";
import { useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Form, FormBody } from "@/ui/Form";
import { useForm } from "react-hook-form";
import AlertCard from "@/ui/AlertCard";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";

const MUTATION = `
  mutation grantProjectAccess($id: ID!) {
    execute:grantProjectAccess(id: $id) {
      userErrors {
        path
        message
      }

      success
    }
  }
`;

export default function GrantProjectAccessDialog({ resource, open, setOpen }) {
  const [formData, setFormData] = useState(null);
  const form = useForm();
  const router = useRouter();
  const { projectId } = useParams();

  const { membership } = resource;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Access granted successfully");
      router.refresh();
      setOpen(false);
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    execute({ id: resource.id });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>Grant {membership.name}&#39;s access</DialogHeader>

        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody className="mx-auto max-w-[800px] py-4">
            <AlertCard variant="info">
              You are about to re-grant <strong>{membership.name}</strong>&#39;s
              access to this project <strong>PTID{projectId}</strong>.
            </AlertCard>
          </FormBody>

          <DialogFooterWithCTA label="Grant" pending={pending} />
        </Form>
      </DialogContent>
    </Dialog>
  );
}
