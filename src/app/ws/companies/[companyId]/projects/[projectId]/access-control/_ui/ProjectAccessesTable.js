"use client";

import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import QueryInput from "@/ui/DataGrid/QueryInput";
import HStack from "@/ui/HStack";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import Pagination from "@/ui/Pagination";
import ProjectAccessActionMenu from "@/app/ws/companies/[companyId]/projects/[projectId]/access-control/_ui/ProjectAccessActionMenu";
import { use, useState } from "react";
import { useSearchParams } from "next/navigation";
import RevokeProjectAccessDialog from "@/app/ws/companies/[companyId]/projects/[projectId]/access-control/_ui/RevokeProjectAccessDialog";
import GrantProjectAccessDialog from "@/app/ws/companies/[companyId]/projects/[projectId]/access-control/_ui/GrantProjectAccessDialog";

export default function ProjectAccessesTable({ sorts, findProjectAccesses }) {
  const [resource, setResource] = useState(null);
  const [open, setOpen] = useState(false);
  const { page } = useSearchParams();
  const currentPage = Number(page || 1);

  const res = use(findProjectAccesses);

  function handleChargeTypeChange(row) {
    setResource(row);
    setOpen(true);
  }

  const DEFAULT_TABLE_COLUMNS = [
    {
      columnName: "CLCTID",
      columnWidth: 150,
      propertyName: "membership.fmId",
      visible: true,
    },
    {
      columnName: "Name",
      columnWidth: 200,
      propertyName: "membership.name",
      onClick: handleChargeTypeChange,
      visible: true,
    },
    {
      columnName: "Status",
      columnWidth: 150,
      propertyName: "status",
      visible: true,
    },
    {
      columnName: "Updated",
      columnWidth: 200,
      propertyName: "updatedAt",
      visible: true,
      cellComponent: DateTimeCell,
    },
    {
      columnName: "Modifier",
      columnWidth: 200,
      propertyName: "modifier.name",
      visible: true,
    },
  ];

  const {
    records: projectAccesses,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(res.data.projectAccesses, res.data.personalTableColumn);

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      {resource && resource.status === "ACTIVE" && open && (
        <RevokeProjectAccessDialog
          resource={resource}
          open={open}
          setOpen={setOpen}
        />
      )}

      {resource && resource.status === "INACTIVE" && open && (
        <GrantProjectAccessDialog
          resource={resource}
          open={open}
          setOpen={setOpen}
        />
      )}

      <HStack className="p-4 pb-2">
        <h1 className="text-xl font-bold">Project Access</h1>

        <ProjectAccessActionMenu />
      </HStack>

      <HStack className="w-full justify-between gap-x-8 gap-y-2 border-b px-4 py-2">
        <QueryInput placeholder="Search members" />

        <HStack className="flex-nowrap gap-8">
          <FieldsDialog
            tableName="STAFF_PROJECT_ACCESSES"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
          <Button variant="plainIcon" prefix={<Filter size={16} />}>
            Filters
          </Button>
          <SortsDialog
            initialSorts={sorts}
            options={[
              { label: "CLCTID", propertyName: "membership.fmId" },
              { label: "Name", propertyName: "membership.name" },
              { label: "Status", propertyName: "status" },
              { label: "Updated", propertyName: "updatedAt" },
            ]}
          />
        </HStack>
      </HStack>

      <DataGrid.Root className="space-y-2 p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            numRows={projectAccesses.length}
            data={projectAccesses}
            tableName="STAFF_PROJECT_ACCESSES"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
