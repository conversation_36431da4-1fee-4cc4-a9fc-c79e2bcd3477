"use client";

import { useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import {
  Form,
  FormBody,
  FormSection,
  FormRow,
  CollectionForm,
  appendWithDefaultValues,
} from "@/ui/Form";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import pluralize from "pluralize";
import AutocompleteCompanyMember from "@/components/autocomplete/AutocompleteCompanyMember";
import { useParams, useRouter } from "next/navigation";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";

const MUTATION = `
  mutation grantProjectAccesses($projectId: ID!, $membershipIds: [ID!]!) {
    execute:grantProjectAccesses(projectId: $projectId, membershipIds: $membershipIds) {
      userErrors {
        path
        message
      }

      success
      successIndexes
    }
  }
`;

export default function Page() {
  const [formData, setFormData] = useState(null);
  const { companyId, projectId } = useParams();
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      members: [{}],
    },
  });
  const {
    formState: { errors },
  } = form;

  const {
    fields: members,
    append: memberAppend,
    remove: memberRemove,
  } = useFieldArray({
    control: form.control,
    name: "members",
  });

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Project access granted successfully");
      router.push(
        `/ws/companies/${companyId}/projects/${projectId}/access-control`,
      );
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    const { members } = input;
    const membershipIds = members.map((m) => m.membership[0].id);

    execute({
      projectId,
      membershipIds,
    });
  };

  return (
    <div className="space-y-4 p-4">
      <h1 className="text-xl font-bold">Grant project access to member</h1>

      <div className="mx-auto max-w-[800px]">
        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody>
            <FormSection
              title={
                <HStack className="justify-between">
                  <h1>Grant members ({members.length})</h1>

                  <Button type="submit" loading={pending}>
                    Grant {members.length} {pluralize("member", members.length)}
                  </Button>
                </HStack>
              }
            >
              <CollectionForm
                items={members}
                add={appendWithDefaultValues(memberAppend, {})}
                remove={memberRemove}
                maxItems={10}
              >
                {(member, index) => {
                  return (
                    <FormSection>
                      <FormRow>
                        <AutocompleteCompanyMember
                          companyId={companyId}
                          name={`members.${index}.membership`}
                          label="Member"
                          rules={{ required: "Member is required!" }}
                          error={errors.members?.[index]?.membership}
                        />
                      </FormRow>
                    </FormSection>
                  );
                }}
              </CollectionForm>

              <HStack className="justify-end">
                <Button type="submit" loading={pending}>
                  Grant {members.length} {pluralize("member", members.length)}
                </Button>
              </HStack>
            </FormSection>
          </FormBody>
        </Form>
      </div>
    </div>
  );
}
