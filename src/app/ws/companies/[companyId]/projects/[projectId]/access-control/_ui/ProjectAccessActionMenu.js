"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import Button from "@/ui/Button";
import { EllipsisIcon, PlusIcon } from "lucide-react";
import { useParams } from "next/navigation";

export default function ProjectAccessActionMenu() {
  const { companyId, projectId } = useParams();
  const baseUrl = `/ws/companies/${companyId}/projects/${projectId}/access-control`;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button
          variant="plainIcon"
          className="gap-0"
          prefix={<EllipsisIcon size={24} />}
        />
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuItem href={`${baseUrl}/new`}>
          <PlusIcon color="green" size={20} />
          Grant access to member
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
