"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import Button from "@/ui/Button";
import { HeartHandshakeIcon, FolderTreeIcon } from "lucide-react";
import { useState } from "react";
import { useParams } from "next/navigation";

export default function SettingUpMenu() {
  const [open, setOpen] = useState(false);
  const params = useParams();
  const companyId = params.companyId;
  const projectId = params.projectId;

  const baseUrl = `/ws/companies/${companyId}/projects/${projectId}`;

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        onPointerDown={(e) => e.preventDefault()}
        onClick={() => setOpen((prev) => !prev)}
      >
        <Button variant="plainIcon" className="gap-0">
          Setting Up
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start">
        <DropdownMenuItem href={`${baseUrl}/entitlements`}>
          <HeartHandshakeIcon size={20} strokeWidth={1.5} />
          Entitlement values
        </DropdownMenuItem>
        <DropdownMenuItem href={`${baseUrl}/files`}>
          <FolderTreeIcon size={20} strokeWidth={1.5} />
          Project files
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
