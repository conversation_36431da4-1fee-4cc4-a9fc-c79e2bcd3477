"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>ger,
  DialogFooterWithCTA,
} from "@/ui/Dialog";
import { useState } from "react";
import { useForm } from "react-hook-form";
import Button from "@/ui/Button";
import {
  Form,
  FormBody,
  FormSection,
  FormRow,
  showFormErrors,
} from "@/ui/Form";
import Input from "@/ui/Input";
import Select from "@/ui/Select";
import ProjectStaffsForm from "@/app/ws/projects/new/ProjectStaffsForm";
import PrivateProjectForm from "@/app/ws/projects/new/PrivateProjectForm";
import projectUpdateAction from "@/app/ws/companies/[companyId]/projects/[projectId]/projectUpdateAction";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import useAction from "@/hooks/useAction";
import CarryForwardForm from "@/app/ws/projects/new/CarryForwardForm";
import ProjectMemberForm from "@/app/ws/projects/new/ProjectMemberForm";
import RenewalEmailSendingModeForm from "@/app/ws/projects/new/RenewalEmailSendingModeForm";
import ReportingForm from "@/app/ws/projects/new/ReportingForm";
import { Edit } from "@blueprintjs/icons";

export default function EditProjectDialog({ project }) {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      name: project.name,
      privateProject: project.privateProject,
      status: project.status,
      renewalEmailSendingMode: project.renewalEmailSendingMode,
      projectReporting: project.projectReporting,
      billingReporting: project.billingReporting,
      carryForward: project.carryForward,
    },
  });

  const {
    register,
    setError,
    formState: { errors },
  } = form;

  const [execute, pending] = useAction(projectUpdateAction, {
    onSuccess: () => {
      router.refresh(); // Must refresh or else bfcache will not reflect
      toast.success("Project updated successfully");
      setOpen(false);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);

    const {
      owner,
      msdBy,
      payrollBy,
      billingBy,
      carryForwardValidMonths,
      maxCarryForwardDays,
      ...restOfInput
    } = input;
    const newInput = {
      ...restOfInput,
      ownerId: owner[0]?.id,
      msdById: msdBy[0]?.id || null,
      payrollById: payrollBy[0]?.id || null,
      billingById: billingBy[0]?.id || null,
      carryForwardValidMonths: carryForwardValidMonths?.value,
      maxCarryForwardDays: maxCarryForwardDays?.value,
    };

    execute({ id: project.id, input: newInput });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <Edit size={20} />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>Update &quot;{project.name}&quot;</DialogHeader>

        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody className="mx-auto max-w-[600px] py-4">
            <FormSection title="Profile">
              <FormRow>
                <Input
                  data-1p-ignore
                  label="Project name"
                  {...register("name", { required: "Name is required!" })}
                  error={errors.name}
                />
                <Select
                  label="Status"
                  {...register("status", { required: "Status is required!" })}
                  error={errors.status}
                >
                  <option value=""></option>
                  <option value="NEW">NEW</option>
                  <option value="ACTIVE">ACTIVE</option>
                  <option value="INACTIVE">INACTIVE</option>
                </Select>
              </FormRow>

              <ProjectMemberForm
                project={project}
                companyId={project.company.id}
              />
            </FormSection>

            <PrivateProjectForm />
            <CarryForwardForm project={project} />
            <RenewalEmailSendingModeForm />
            <ReportingForm />
            <ProjectStaffsForm project={project} />
          </FormBody>

          <DialogFooterWithCTA label="Update" pending={pending} />
        </Form>
      </DialogContent>
    </Dialog>
  );
}
