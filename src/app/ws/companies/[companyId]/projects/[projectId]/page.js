import Card from "@/ui/Card";
import { PropertyList, List } from "@/ui/PropertyList";
import HStack from "@/ui/HStack";
import { notFound } from "next/navigation";
import apiQuery from "@/lib/apiQuery";
import PayrollModulesUpdateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/PayrollModulesUpdateForm";
import BillingAddressUpdateForm from "@/app/ws/companies/[companyId]/projects/[projectId]/BillingAddressUpdateForm";
import EditProjectDialog from "@/app/ws/companies/[companyId]/projects/[projectId]/EditProjectDialog";
import { ProgressBarLink } from "@/ui/ProgressBar";
import CopyToClipboard from "@/components/CopyToClipboard";

const PROJECT_QUERY = `
  query project($id: ID!) {
    project(id: $id) {
      id
      fmId
      name
      status
      privateProject
      leaveModule
      timesheetModule
      claimModule
      timesheetUploadModule
      billingDepartment
      billingAttention
      billingAddress
      billingPostalCode

      carryForward
      maxCarryForwardDays
      carryForwardValidMonths

      renewalEmailSendingMode
      projectReporting
      billingReporting

      company {
        id
        name
      }

      owner {
        id
        fmId
        fmIdName
        companyId
        memberId
        status
        name
        email
        memberStatus
      }
      msdBy {
        id
        name
        email
        status
      }
      payrollBy {
        id
        name
        email
        status
      }
      billingBy {
        id
        name
        email
        status
      }

      createdAt
      updatedAt

      creator {
        id
        name
      }
      modifier {
        id
        name
      }
    }
  }
`;

export default async function Page({ params }) {
  const { projectId } = await params;
  const res = await apiQuery(PROJECT_QUERY, { id: projectId });
  const project = res.data.project;

  if (project === null) notFound();

  return (
    <div className="mx-auto grid max-w-[1000px] grid-cols-1 gap-2 p-4 lg:grid-cols-2">
      <Card className="space-y-4">
        <PropertyList
          title="Project overview"
          titleSuffix={<EditProjectDialog project={project} />}
        >
          <List label="Name" className="font-bold">
            {project.name}
          </List>
          <List label="PTID">
            <CopyToClipboard>{project.fmId}</CopyToClipboard>
          </List>
          <List label="Status">{project.status}</List>
          <List.Membership label="Project owner" membership={project.owner} />
          <List.NameEmailStatus label="Managed by" user={project.msdBy} />
          <List.NameEmailStatus label="Payroll by" user={project.payrollBy} />
          <List.NameEmailStatus label="Billing by" user={project.billingBy} />

          <List label="Private project">
            {project.privateProject ? "Private" : "Public"}
          </List>
        </PropertyList>

        <PropertyList title="Headcount &amp; quotation">
          <List label="Active headcount">
            {Math.floor(Math.random() * 101)}
          </List>
          <List label="Total headcount">{Math.floor(Math.random() * 101)}</List>
          <List label="QID">
            <span>NOT MODEL YET!</span>
          </List>
        </PropertyList>

        <PropertyList title="Carry forward">
          <List label="Can carry forward AL leaves">
            {project.carryForward ? "Yes" : "No"}
          </List>
          <List label="Max days allowed to forward">
            {project.maxCarryForwardDays}
          </List>
          <List label="For how many months">
            {project.carryForwardValidMonths}
          </List>
        </PropertyList>

        <PropertyList title="Reporting">
          <List label="Project reporting">
            {project.projectReporting.join(", ")}
          </List>
          <List label="Billing reporting">
            {project.billingReporting.join(", ")}
          </List>
        </PropertyList>

        <PropertyList title="Renewal">
          <List label="Email sending mode">
            {project.renewalEmailSendingMode}
          </List>
        </PropertyList>
      </Card>

      <div className="space-y-2">
        <Card className="space-y-4">
          <PayrollModulesUpdateForm project={project} />
        </Card>

        <Card className="space-y-4">
          <BillingAddressUpdateForm project={project} />
        </Card>

        <Card className="space-y-4">
          <PropertyList title="Timestamp">
            <List.DateBy label="Created" by={project.creator?.name}>
              {project.createdAt}
            </List.DateBy>
            <List.DateBy label="Updated" by={project.modifier?.name}>
              {project.updatedAt}
            </List.DateBy>
          </PropertyList>
        </Card>
      </div>
    </div>
  );
}
