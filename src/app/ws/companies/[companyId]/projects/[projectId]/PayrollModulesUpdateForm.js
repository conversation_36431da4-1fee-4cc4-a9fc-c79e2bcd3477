"use client";

import { Form, showFormErrors } from "@/ui/Form";
import { useState } from "react";
import { useForm } from "react-hook-form";
import PayrollModulesForm from "@/app/ws/projects/new/PayrollModulesForm";
import Button from "@/ui/Button";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import projectUpdateAction from "@/app/ws/companies/[companyId]/projects/[projectId]/projectUpdateAction";
import useAction from "@/hooks/useAction";

export default function PayrollModulesUpdateForm({ project }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      leaveModule: project.leaveModule,
      timesheetModule: project.timesheetModule,
      claimModule: project.claimModule,
      timesheetUploadModule: project.timesheetUploadModule,
    },
  });

  const {
    setError,
    reset,
    formState: { isDirty },
  } = form;

  const [execute, pending] = useAction(projectUpdateAction, {
    onSuccess: () => {
      reset({}, { keepValues: true });
      router.refresh(); // Must do a refresh or else bfcache will not reflect
      toast.success("Project updated successfully");
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);
    execute({ id: project.id, input });
  };

  return (
    <Form
      form={form}
      onSubmit={onSubmit}
      formData={formData}
      className="space-y-4"
    >
      <PayrollModulesForm />

      <div className="text-center">
        <Button
          type="submit"
          size="sm"
          fullWidth
          loading={pending}
          variant={isDirty ? "success" : "secondary"}
          disabled={!isDirty}
        >
          Update
        </Button>
      </div>
    </Form>
  );
}
