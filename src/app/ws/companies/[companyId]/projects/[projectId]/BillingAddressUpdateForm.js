"use client";

import { Form, FormRow, FormSection, showFormErrors } from "@/ui/Form";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import Button from "@/ui/Button";
import Input from "@/ui/Input";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import projectUpdateAction from "@/app/ws/companies/[companyId]/projects/[projectId]/projectUpdateAction";
import useAction from "@/hooks/useAction";

export default function BillingAddressUpdateForm({ project }) {
  const [formData, setFormData] = useState(null);
  const router = useRouter();

  // Here, the defaultValues needed to be an empty string in order for isDirty to work
  // when backspacing the input fields to an empty string.
  const form = useForm({
    defaultValues: {
      billingDepartment: project.billingDepartment || "",
      billingAttention: project.billingAttention || "",
      billingAddress: project.billingAddress || "",
      billingPostalCode: project.billingPostalCode || "",
    },
  });

  const {
    register,
    setError,
    reset,
    formState: { isDirty },
  } = form;

  const [execute, pending] = useAction(projectUpdateAction, {
    onSuccess: () => {
      reset({}, { keepValues: true });
      router.refresh(); // Must refresh or else bfcache will not reflect
      toast.success("Project updated successfully");
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);
    execute({ id: project.id, input });
  };

  return (
    <Form
      form={form}
      onSubmit={onSubmit}
      formData={formData}
      className="space-y-4"
    >
      <FormSection
        title="Configure billing instructions"
        description="Who to bill to for this project?"
      >
        <FormRow>
          <Input
            size="sm"
            label="Billing department"
            {...register("billingDepartment")}
          />
          <Input
            size="sm"
            label="Billing attention (ATTN)"
            {...register("billingAttention")}
          />
        </FormRow>

        <FormRow>
          <Input
            size="sm"
            data-1p-ignore
            label="Billing address"
            {...register("billingAddress")}
          />
        </FormRow>

        <FormRow colsCount={2}>
          <Input
            size="sm"
            data-1p-ignore
            label="Billing postal code"
            {...register("billingPostalCode")}
          />
        </FormRow>
      </FormSection>

      <div className="text-center">
        <Button
          type="submit"
          loading={pending}
          size="sm"
          fullWidth
          variant={isDirty ? "success" : "secondary"}
          disabled={!isDirty}
        >
          Update
        </Button>
      </div>
    </Form>
  );
}
