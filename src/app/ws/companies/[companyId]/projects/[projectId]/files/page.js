import Card from "@/ui/Card";
import CompanyFileTree from "@/app/ws/companies/[companyId]/files/_ui/CompanyFileTree";
import { ChevronsRightIcon } from "lucide-react";
import Select from "@/ui/Select";
import HStack from "@/ui/HStack";
import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";

const COMPANY_QUERY = `
  query company($id: ID!) {
    company(id: $id) {
      id
      name
      companyFileDescriptions
    }
  }
`;

export default async function Page({ params }) {
  const { companyId } = await params;

  const res = await apiQuery(COMPANY_QUERY, { id: companyId });
  const company = res.data.company;

  if (company === null) notFound();

  return (
    <div className="space-y-4 p-4">
      <h1 className="text-lg font-bold">
        Input source file from company to project.
      </h1>

      <div className="grid grid-cols-2 gap-4">
        <Card className="space-y-4">
          <h1 className="flex items-center justify-between font-bold">
            Source company files
            <ChevronsRightIcon />
          </h1>

          <h1>{"Don't do it here..."}</h1>

          <CompanyFileTree enableSwitchView={false} company={company} />

          <HStack>
            <Select size="sm">
              <option value="Onboarding Documents">Onboarding Documents</option>
            </Select>

            <Select size="sm">
              <option value="Letter of Undertaking">
                Letter of Undertaking
              </option>
            </Select>
          </HStack>
        </Card>

        <Card className="space-y-4">
          <h1 className="flex items-center justify-between font-bold">
            <ChevronsRightIcon />
            Selected project files (20)
          </h1>

          <CompanyFileTree enableSwitchView={false} company={company} />
        </Card>
      </div>
    </div>
  );
}
