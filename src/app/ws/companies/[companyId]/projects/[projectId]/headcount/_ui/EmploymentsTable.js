import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import apiQuery from "@/lib/apiQuery";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import decompressSearchParam from "@/lib/decompressSearchParam";
import QueryInput from "@/ui/DataGrid/QueryInput";
import HStack from "@/ui/HStack";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import Pagination from "@/ui/Pagination";
import DateCell from "@/ui/DataGrid/cells/DateCell";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";

const QUERY = `
  query projectEmployments($projectId: ID!, $query: String, $sorts: [SortInput!], $page: Int!) {
    projectEmployments(projectId: $projectId, query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        fmId
        contractorName
        commencementDate
        expiredDate
        status

        creator {
          id
          name
        }
        modifier {
          id
          name
        }

        createdAt
        updatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "STAFF_PROJECT_EMPLOYMENTS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "SRID",
    columnWidth: 120,
    propertyName: "fmId",
    visible: true,
  },
  {
    columnName: "Contractor",
    columnWidth: 200,
    propertyName: "contractorName",
    visible: true,
    href: "/ws/SR/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Commencement",
    columnWidth: 150,
    propertyName: "commencementDate",
    visible: true,
    cellComponent: DateCell,
  },
  {
    columnName: "Expired",
    columnWidth: 150,
    propertyName: "expiredDate",
    visible: true,
    cellComponent: DateCell,
  },
  {
    columnName: "Status",
    columnWidth: 150,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Updated",
    columnWidth: 200,
    propertyName: "updatedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
  {
    columnName: "Modifier",
    columnWidth: 200,
    propertyName: "modifier.name",
    visible: true,
  },
];

export default async function EmploymentsTable({ searchParams, params }) {
  let { page, query, sorts } = await searchParams;
  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "updatedAt", desc: true }]);
  const { projectId } = await params;

  const res = await apiQuery(QUERY, {
    projectId,
    query,
    sorts,
    page: currentPage,
  });

  const {
    records: projectEmployments,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(
    res.data.projectEmployments,
    res.data.personalTableColumn,
  );

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      <HStack className="p-4 pb-2">
        <h1 className="text-xl font-bold">Headcount</h1>
      </HStack>

      <HStack className="w-full justify-between gap-x-8 gap-y-2 border-b px-4 py-2">
        <QueryInput placeholder="Search contractors, employments" />

        <HStack className="flex-nowrap gap-8">
          <FieldsDialog
            tableName="STAFF_PROJECT_EMPLOYMENTS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
          <Button variant="plainIcon" prefix={<Filter size={16} />}>
            Filters
          </Button>
          <SortsDialog
            initialSorts={sorts}
            options={[
              { label: "SRID", propertyName: "fmId" },
              { label: "Contractor", propertyName: "contractorName" },
              { label: "Status", propertyName: "status" },
              { label: "Updated", propertyName: "updatedAt" },
            ]}
          />
        </HStack>
      </HStack>

      <DataGrid.Root className="space-y-2 p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            numRows={projectEmployments.length}
            data={projectEmployments}
            tableName="STAFF_PROJECT_EMPLOYMENTS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
