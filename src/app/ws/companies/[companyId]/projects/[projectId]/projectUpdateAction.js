"use server";

import apiMutation from "@/lib/apiMutation";
import { retrieveActionResult } from "@/lib/mutationResponse";

const MUTATION = `
  mutation updateProject($id: ID!, $input: ProjectInput!) {
    updateProject(id: $id, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Project {
          id
        }
      }
    }
  }
`;

export default async function projectUpdateAction({ id, input }) {
  const res = await apiMutation(MUTATION, { id, input });

  return retrieveActionResult(res, "updateProject");
}
