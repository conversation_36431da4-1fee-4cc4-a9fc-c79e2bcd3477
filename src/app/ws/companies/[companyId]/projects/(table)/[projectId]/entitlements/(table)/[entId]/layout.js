import EntSegmentedControl from "@/app/ws/companies/[companyId]/projects/(table)/[projectId]/entitlements/(table)/[entId]/EntSegmentedControl";
import apiQuery from "@/lib/apiQuery";
import { PropertyList, List } from "@/ui/PropertyList";
import { notFound } from "next/navigation";
import Card from "@/ui/Card";
import pluralize from "pluralize";
import HStack from "@/ui/HStack";
import EntActionMenu from "@/app/ws/companies/[companyId]/projects/(table)/[projectId]/entitlements/(table)/[entId]/EntActionMenu";
import { Citation } from "@blueprintjs/icons";
import RawHtml from "@/ui/RawHtml";
import VStack from "@/ui/VStack";
import QuotedRemark from "@/components/QuotedRemark";

const QUERY = `
  query ent($id: ID!) {
    ent(id: $id) {
      id
      fmId
      description
      noticeDays
      displayProbationNoticeDays

      creator {
        id
        name
      }
      modifier {
        id
        name
      }

      status
      createdAt
      updatedAt
    }
  }
`;

export default async function Layout({ children, params }) {
  const { entId } = await params;
  const res = await apiQuery(QUERY, { id: entId });
  const ent = res.data.ent;
  if (ent === null) notFound();

  return (
    <div className="space-y-4 p-4">
      <div className="mx-auto max-w-[800px] space-y-4">
        <Card className="space-y-4">
          <HStack className="justify-between text-lg font-bold">
            {ent.fmId}
            <EntActionMenu ent={ent} />
          </HStack>

          <QuotedRemark title="ENT Description" bg={false}>
            {ent.description}
          </QuotedRemark>

          <PropertyList>
            <List label="ID">{ent.fmId}</List>
            <List label="Status">{ent.status}</List>
            <List label="Probation notice period">
              {ent.displayProbationNoticeDays}
            </List>
            <List label="Notice period">
              {ent.noticeDays &&
                `${ent.noticeDays} ${pluralize("day", ent.noticeDays)}`}
            </List>
            <List.DateBy label="Created" by={ent.creator?.name}>
              {ent.createdAt}
            </List.DateBy>
            <List.DateBy label="Updated" by={ent.modifier?.name}>
              {ent.updatedAt}
            </List.DateBy>
          </PropertyList>
        </Card>
      </div>

      <div className="mx-auto max-w-[1000px] text-center">
        <EntSegmentedControl />
      </div>

      {children}
    </div>
  );
}
