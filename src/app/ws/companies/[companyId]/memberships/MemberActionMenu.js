"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import Button from "@/ui/Button";
import { EllipsisIcon, PlusIcon } from "lucide-react";
import { useParams } from "next/navigation";

export default function MemberActionMenu() {
  const { companyId } = useParams();
  const baseUrl = `/ws/companies/${companyId}`;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button
          variant="plainIcon"
          className="gap-0"
          prefix={<EllipsisIcon size={24} />}
        />
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuItem href={`${baseUrl}/memberships/new`}>
          <PlusIcon color="green" size={20} />
          New membership
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
