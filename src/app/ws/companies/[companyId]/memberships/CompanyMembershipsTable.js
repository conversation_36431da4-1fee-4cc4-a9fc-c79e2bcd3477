import decompressSearchParam from "@/lib/decompressSearchParam";
import apiQuery from "@/lib/apiQuery";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import TextCell from "@/ui/DataGrid/cells/TextCell";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import HStack from "@/ui/HStack";
import QueryInput from "@/ui/DataGrid/QueryInput";
import FieldsDialog from "@/ui/DataGrid/FieldsDialog";
import Button from "@/ui/Button";
import { Filter } from "lucide-react";
import SortsDialog from "@/ui/DataGrid/SortsDialog";
import Pagination from "@/ui/Pagination";
import MemberActionMenu from "@/app/ws/companies/[companyId]/memberships/MemberActionMenu";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import EmailCell from "@/ui/DataGrid/cells/EmailCell";

const QUERY = `
  query companyMemberships($companyId: ID!, $query: String, $sorts: [SortInput!], $page: Int!) {
    companyMemberships(companyId: $companyId, query: $query, sorts: $sorts, page: $page) {
      nodes {
        id
        fmId
        memberId
        companyId
        name
        email
        memberStatus
        status
        updatedAt
        createdAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "COMPANY_MEMBERSHIPS") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 130,
    propertyName: "fmId",
    visible: true,
    cellComponent: TextCell,
  },
  {
    columnName: "Name",
    columnWidth: 220,
    propertyName: "name",
    removable: false,
    visible: true,
    href: "/ws/companies/${row.companyId}/memberships/${row.id}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Email",
    columnWidth: 300,
    propertyName: "email",
    visible: true,
    cellComponent: EmailCell,
  },
  {
    columnName: "Status",
    columnWidth: 150,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Updated",
    columnWidth: 200,
    propertyName: "updatedAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default async function CompanyMembershipsTable({
  searchParams,
  params,
}) {
  let { page, query, sorts } = await searchParams;
  const { companyId } = await params;

  const currentPage = Number(page || 1);
  sorts = decompressSearchParam(sorts, [{ id: "updatedAt", desc: true }]);

  const res = await apiQuery(QUERY, {
    query,
    sorts,
    page: currentPage,
    companyId,
  });

  const {
    records: memberships,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(
    res.data.companyMemberships,
    res.data.personalTableColumn,
  );

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <>
      <div fix-auto-scroll-error=""></div>
      <HStack className="p-4 pb-2">
        <h1 className="text-xl font-bold">Memberships</h1>

        <MemberActionMenu />
      </HStack>

      <HStack className="sticky top-[92px] z-50 justify-between gap-x-8 gap-y-2 border-b bg-background px-4 py-2">
        <QueryInput placeholder="Search memberships" />

        <HStack className="flex-nowrap gap-8">
          <FieldsDialog
            tableName="COMPANY_MEMBERSHIPS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
          <Button variant="plainIcon" prefix={<Filter size={16} />}>
            Filters
          </Button>
          <SortsDialog
            initialSorts={sorts}
            options={[
              { label: "ID", propertyName: "id" },
              { label: "Name", propertyName: "orgMembers.name" },
              { label: "Date", propertyName: "createdAt" },
              { label: "Status", propertyName: "status" },
              { label: "Updated", propertyName: "updatedAt" },
            ]}
          />
        </HStack>
      </HStack>

      <DataGrid.Root className="p-4">
        <Pagination currentPage={currentPage} totalPages={totalPages} />
        <DataGrid.TotalCountAndTime
          totalCount={totalCount}
          processingTimeMs={processingTimeMs}
        />

        <DataGrid.Content>
          <DataGridTable
            data={memberships}
            numRows={memberships.length}
            tableName="COMPANY_MEMBERSHIPS"
            tableColumns={tableColumns}
            defaultTableColumns={DEFAULT_TABLE_COLUMNS}
          />
        </DataGrid.Content>
      </DataGrid.Root>
    </>
  );
}
