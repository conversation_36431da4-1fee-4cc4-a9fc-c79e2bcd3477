import { SecondaryTabs, Tab } from "@/ui/SecondaryTabs";
import {
  CheckCheckIcon,
  KeyRoundIcon,
  LayoutDashboardIcon,
  UsersIcon,
} from "lucide-react";

export default async function Layout({ children, params }) {
  const { companyId, memberId } = await params;
  const baseUrl = `/ws/companies/${companyId}/memberships/${memberId}`;

  return (
    <>
      <SecondaryTabs className="bg-background/95 sticky top-[94px] backdrop-blur-sm">
        <Tab
          href={`${baseUrl}`}
          exact
          prefix={<LayoutDashboardIcon size={20} strokeWidth={1.5} />}
        >
          Overview
        </Tab>
        <Tab
          href={`${baseUrl}/access-control`}
          prefix={<KeyRoundIcon size={20} strokeWidth={1.5} />}
        >
          Permissions
        </Tab>
        <Tab
          href={`${baseUrl}/approval`}
          prefix={<CheckCheckIcon size={20} strokeWidth={1.5} />}
        >
          Approval
        </Tab>
        <Tab
          href={`${baseUrl}/contractors`}
          prefix={<UsersIcon size={20} strokeWidth={1.5} />}
        >
          Contractors
        </Tab>
      </SecondaryTabs>

      {children}
    </>
  );
}
