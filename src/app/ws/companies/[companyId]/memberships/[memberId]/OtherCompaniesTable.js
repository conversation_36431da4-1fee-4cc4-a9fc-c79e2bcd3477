import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "CLCTID",
    columnWidth: 130,
    propertyName: "fmId",
  },
  {
    columnName: "Name",
    columnWidth: 300,
    propertyName: "company.fmIdName",
  },
  {
    columnName: "Status",
    columnWidth: 100,
    propertyName: "status",
  },
];

export default async function OtherCompaniesTable({ otherMemberships }) {
  return (
    <DataGrid.Root className="space-y-4">
      <DataGrid.Content>
        <DataGridTable
          numRows={otherMemberships.length}
          data={otherMemberships}
          tableColumns={DEFAULT_TABLE_COLUMNS}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
}
