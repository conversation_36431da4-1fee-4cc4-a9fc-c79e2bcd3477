"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooterWithCTA,
} from "@/ui/Dialog";
import Button from "@/ui/Button";
import {
  FormSection,
  FormBody,
  FormRow,
  Form,
  showFormErrors,
} from "@/ui/Form";
import Input from "@/ui/Input";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";
import Select from "@/ui/Select";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Edit } from "@blueprintjs/icons";

const MUTATION = `
  mutation updateCompanyMembership($id: ID!, $input: CompanyMembershipInput!) {
    execute:updateCompanyMembership(id: $id, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Member {
          id
        }
      }
    }
  }
`;

export default function EditMemberDialog({ membership, member }) {
  const [open, setO<PERSON>] = useState(false);
  const [formData, setFormData] = useState(null);
  const form = useForm({
    defaultValues: {
      name: member.name,
      email: member.email,
      designation: member.designation,
      department: member.department,
      phoneNumber: member.phoneNumber,
      memberStatus: member.status,
      membershipStatus: membership.status,
    },
  });
  const router = useRouter();

  const {
    register,
    formState: { errors },
    setError,
  } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      router.refresh();
      toast.success("Membership updated successfully!");
      setOpen(false);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    setFormData(input);
    execute({ id: membership.id, input });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="plainIcon" className="text-muted">
          <Edit size={20} />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>Update &quot;{member.name}&quot;</DialogHeader>

        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <FormBody className="mx-auto max-w-[600px] py-4">
            <FormSection
              title="Membership"
              description="By changing the membership status, you can control the access of this member to the company."
            >
              <FormRow>
                <Select
                  label="Membership status"
                  {...register("membershipStatus", {
                    required: "Membership status is required!",
                  })}
                  error={errors.membershipStatus}
                >
                  <option value="ACTIVE">ACTIVE</option>
                  <option value="INACTIVE">INACTIVE</option>
                </Select>
              </FormRow>
            </FormSection>

            <FormSection
              title="Member profile"
              description="Note: Inactive member will not be allowed to sign in."
            >
              <FormRow>
                <Input
                  label="Name"
                  {...register("name", { required: "Name is required!" })}
                  error={errors.name}
                />

                <Input
                  label="Email"
                  {...register("email", { required: "Email is required!" })}
                  error={errors.email}
                />
              </FormRow>

              <FormRow>
                <Input label="Designation" {...register("designation")} />

                <Input label="Department" {...register("department")} />
              </FormRow>

              <FormRow>
                <Input label="Phone Number" {...register("phoneNumber")} />

                <Select
                  label="Member status"
                  {...register("memberStatus", {
                    required: "Member status is required!",
                  })}
                  error={errors.status}
                >
                  <option value="ACTIVE">ACTIVE</option>
                  <option value="INACTIVE">INACTIVE</option>
                </Select>
              </FormRow>
            </FormSection>
          </FormBody>

          <DialogFooterWithCTA label="Update" pending={pending} />
        </Form>
      </DialogContent>
    </Dialog>
  );
}
