import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import HStack from "@/ui/HStack";
import { CircleHelp } from "lucide-react";
import EditMemberDialog from "@/app/ws/companies/[companyId]/memberships/[memberId]/EditMemberDialog";
import cn from "@/lib/cn";
import { PropertyList, List, ListHelpText } from "@/ui/PropertyList";
import Card from "@/ui/Card";
import VStack from "@/ui/VStack";
import { ProgressBarLink } from "@/ui/ProgressBar";
import { sortBy } from "lodash";
import MembershipPortal from "@/components/MembershipPortal";
import OtherCompaniesTable from "@/app/ws/companies/[companyId]/memberships/[memberId]/OtherCompaniesTable";
import CopyToClipboard from "@/components/CopyToClipboard";

const QUERY = `
  query companyMembership($id: ID!) {
    companyMembership(id: $id) {
      id
      fmId
      slug
      roles
      status
      remark
      createdAt
      updatedAt

      member {
        id
        name
        email
        phoneNumber
        status
        designation
        department
        createdAt
        updatedAt

        companies {
          id
          fmId
          fmIdName
          name
          status
        }
      }

      otherMemberships {
        fmId
        status

        company {
          fmIdName
        }
      }

      company {
        id
        fmId
        name
      }
    }
  }
`;

export default async function Page({ params }) {
  const { memberId, companyId } = await params;
  const res = await apiQuery(QUERY, { id: memberId });
  const membership = res.data.companyMembership;

  if (membership === null) notFound();
  if (membership.company.id !== companyId) notFound();

  const { member, company, otherMemberships } = membership;

  const classNames = cn("space-y-4", {
    "bg-red-50/50": member.status.toUpperCase() === "INACTIVE",
  });

  return (
    <div className="mx-auto max-w-[800px] p-4 pt-2">
      <Card className={classNames}>
        <HStack className="justify-between text-lg font-bold">
          {membership.fmId}: {member.name}
          <EditMemberDialog membership={membership} member={member} />
        </HStack>

        <PropertyList title={`Membership info with '${company.name}'`}>
          <List label="CLCTID">
            <HStack className="justify-end">
              <CopyToClipboard>{membership.fmId}</CopyToClipboard>
              <MembershipPortal membership={membership} />
            </HStack>
          </List>
          <List label="Company name">
            {company.fmId}: {company.name}
          </List>
          <List.Status
            label="Membership status"
            helpText={
              <ListHelpText>
                The status with this company as part of the membership.
                Different from member status.
              </ListHelpText>
            }
          >
            {membership.status}
          </List.Status>
          <List.Timestamp label="Membership since">
            {membership.createdAt}
          </List.Timestamp>
          <List.Timestamp label="Updated">
            {membership.updatedAt}
          </List.Timestamp>
        </PropertyList>

        <PropertyList
          title={
            <VStack className="gap-0">
              <h1 className="font-bold">Member info</h1>
              <p className="text-sm font-normal text-muted">
                Note: A unique member (identified by email) can hold memberships
                in multiple companies.
              </p>
            </VStack>
          }
        >
          <List label="Name" className="font-bold">
            <CopyToClipboard>{member.name}</CopyToClipboard>
          </List>
          <List.ID object={member} label="Member ID" />
          <List.Email label="Email">{member.email}</List.Email>
          <List.Status
            label="Member status"
            helpText={
              <ListHelpText>
                This status takes precedence over membership. If inactive,
                member will not be able to sign in.
              </ListHelpText>
            }
          >
            {member.status}
          </List.Status>
          <List label="Designation">{member.designation}</List>
          <List label="Department">{member.department}</List>
          <List label="Phone Number">{member.phoneNumber}</List>
          <List.Timestamp label="Member since">
            {member.createdAt}
          </List.Timestamp>
          <List.Timestamp label="Updated">{member.updatedAt}</List.Timestamp>
        </PropertyList>

        <PropertyList title={`Other companies ${member.name} is assigned to`}>
          <OtherCompaniesTable otherMemberships={otherMemberships} />
        </PropertyList>
      </Card>
    </div>
  );
}
