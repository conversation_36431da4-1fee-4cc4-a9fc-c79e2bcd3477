@import "tailwindcss";
@import "tw-animate-css";
@import "./theme.css";
@import "./toaster.css";
@import "./tiptap-editor.css";
@import "./loader.css";
@import "./segmented-control.css";
@import "./blueprint-table.css";
@import "./blueprint-datepicker.css";
@import "./avatar.css";
@import "./react-complex-tree-overrides.css";

@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));

:root {
  --background: var(--color-white);
  --foreground: var(--color-black);

  --primary: #2d72d2;
  --primary-foreground: var(--color-white);
  --primary-border: rgb(33, 93, 176); /* oklch(0.4868 0.1459 257.66); */
  --primary-ring: rgba(33, 93, 176, 0.8);

  --secondary: var(--color-gray-200);
  --secondary-foreground: var(--color-black);
  --secondary-border: --alpha(var(--color-gray-400) / 50%);
  --secondary-ring: var(--color-gray-400);

  --success: var(--color-green-500);
  --success-foreground: var(--color-white);
  --success-border: var(--color-green-600);
  --success-ring: var(--color-green-500);

  --warning: var(--color-orange-400);
  --warning-foreground: var(--color-white);
  --warning-border: var(--color-orange-500);
  --warning-ring: var(--color-orange-400);

  --danger: var(--color-red-500);
  --danger-foreground: var(--color-white);
  --danger-border: var(--color-red-600);
  --danger-ring: var(--color-red-500);

  --border: var(--color-gray-200);

  --input: var(--color-gray-400);
  --input-ring: var(--color-gray-400);
  --input-error: var(--color-red-400);
  --input-error-ring: var(--color-red-600);

  --card: var(--color-white);
  --card-foreground: var(--color-black);

  --dialog: var(--color-white);

  --popover: var(--color-white);
  --popover-foreground: var(--color-black);
  --popover-focus: var(--color-gray-200);
  --popover-active: var(--color-gray-300);

  --muted: var(--color-gray-500);
  /*--link: oklch(0.4868 0.1459 257.66);*/
  --link: var(--color-blue-800);
}

[data-theme="dark"] {
  --background: var(--color-zinc-900);
  --foreground: oklch(0.985 0 0);

  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-white);
  --primary-border: var(--color-blue-500);
  --primary-ring: var(--color-blue-600);

  --secondary: var(--color-neutral-700);
  --secondary-foreground: var(--color-white);
  --secondary-border: var(--color-neutral-500);
  --secondary-ring: var(--color-neutral-700);

  --success: var(--color-green-900);
  --success-foreground: var(--color-white);
  --success-border: var(--color-green-600);
  --success-ring: var(--color-green-700);

  --warning: var(--color-orange-900);
  --warning-foreground: var(--color-orange-300);
  --warning-border: var(--color-orange-300);
  --warning-ring: var(--color-orange-300);

  --danger: var(--color-red-900);
  --danger-foreground: var(--color-red-200);
  --danger-border: var(--color-red-300);
  --danger-ring: var(--color-red-300);

  --border: var(--color-neutral-700);

  --input: var(--color-neutral-500);
  --input-ring: var(--color-neutral-400);
  --input-error: var(--color-red-300);
  --input-error-ring: var(--color-red-500);

  --card: var(--color-zinc-800);
  --card-foreground: var(--color-white);

  --dialog: var(--color-zinc-800);

  --popover: var(--dialog);
  --popover-foreground: var(--foreground);
  --popover-focus: var(--color-zinc-700);
  --popover-active: var(--color-zinc-900);

  --muted: var(--color-gray-400);
  --link: var(--color-blue-400);
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border, currentColor);
  }
}

html {
  /*font-size: 0.9375rem; !* 15px *!*/
}

body {
  @apply bg-background text-foreground;
}

svg {
  @apply flex-none;
}

.input-select {
  @apply !pr-8 text-ellipsis;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' viewBox='0 0 24 24'%3E%3Cpath d='m6 9 6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: calc(100% - 8px);
}

[data-theme="dark"] .input-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' viewBox='0 0 24 24'%3E%3Cpath d='m6 9 6 6 6-6'/%3E%3C/svg%3E");
}

.form-row {
  @apply data-[cols='1']:sm:grid-cols-1;
  @apply data-[cols='2']:sm:grid-cols-2;
  @apply data-[cols='3']:sm:grid-cols-3;
  @apply data-[cols='4']:sm:grid-cols-4;
  @apply data-[cols='5']:sm:grid-cols-5;
  @apply data-[cols='6']:sm:grid-cols-6;
  @apply data-[cols='7']:sm:grid-cols-7;
  @apply data-[cols='8']:sm:grid-cols-8;
  @apply data-[cols='9']:sm:grid-cols-9;
  @apply data-[cols='10']:sm:grid-cols-10;
}

.temp-animation {
  animation-iteration-count: 3 !important;
}

mark {
  @apply bg-yellow-300 box-decoration-clone dark:bg-yellow-400;
}

.iras-building {
  background: url("/images/IRAS_building.png") no-repeat;
  background-size: cover;
  clip-path: ellipse(230px 150px at right 100%);
}

.hstack {
  @apply flex items-center gap-2;
}

.form-table {
  @apply w-full table-auto;
}

.form-table td {
  @apply w-1/2 border border-gray-400 p-2 align-top;
}

.onboarding-table {
  @apply bg-background w-full table-auto;
}

.onboarding-table th {
  @apply bg-gray-50 text-left font-normal text-gray-500;
}

.onboarding-table td,
.onboarding-table th {
  @apply border border-gray-400 px-2 py-1 align-top;
}

.basic-property-table {
  @apply w-full table-auto;
}

.basic-property-table td {
  @apply border-b;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -webkit-overflow-scrolling: auto;
  -ms-overflow-style: none;
  scrollbar-width: none; /* Firefox */
}

.mask-left-right {
  mask-image: linear-gradient(
    to right,
    transparent,
    black 10px,
    black 95%,
    transparent
  );
}

.mask-right {
  mask-image: linear-gradient(
    to right,
    black,
    black 10px,
    black 95%,
    transparent
  );
}

.mask-left {
  mask-image: linear-gradient(
    to left,
    black,
    black 10px,
    black 95%,
    transparent
  );
}

.notion-dropdown-menu-shadow {
  box-shadow:
    rgba(0, 0, 0, 0.1) 0 14px 28px -6px,
    rgba(0, 0, 0, 0.06) 0 2px 4px -1px,
    rgba(84, 72, 49, 0.08) 0 0 0 1px;
}

.notion-dropdown-menu-shadow-light {
  box-shadow:
    rgba(0, 0, 0, 0.1) 0 4px 8px -6px,
    rgba(0, 0, 0, 0.06) 0 2px 4px -1px,
    rgba(84, 72, 49, 0.15) 0 0 0 1px;
}

.text-underline-link {
  @apply text-link underline underline-offset-4;
}
