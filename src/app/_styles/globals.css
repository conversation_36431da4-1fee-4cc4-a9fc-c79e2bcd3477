@import "tailwindcss";
@import "tw-animate-css";
@import "./theme.css";
@import "./toaster.css";
@import "./tiptap-editor.css";
@import "./loader.css";
@import "./segmented-control.css";
@import "./blueprint-table.css";
@import "./blueprint-datepicker.css";
@import "./full-calendar.css";
@import "./avatar.css";
@import "./react-complex-tree-overrides.css";
@import "@xyflow/react/dist/style.css";
@import "./react-flow-dark.css";

@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));

:root {
  --background: var(--color-white);
  --foreground: var(--color-black);

  --primary: oklch(0.5603 0.1634 257.63);
  --primary-foreground: var(--color-white);
  --primary-border: oklch(0.4868 0.1459 257.66); /* rgb(33, 93, 176); */
  --primary-hover: oklch(0.5104 0.1634 257.63);
  --primary-active: oklch(0.4486 0.1634 257.63);

  --secondary: var(--color-gray-200);
  --secondary-foreground: var(--color-black);
  --secondary-border: var(--color-gray-400);
  --secondary-hover: --alpha(var(--color-gray-300) / 80%);
  --secondary-active: var(--color-gray-300);

  --success: var(--color-green-500);
  --success-foreground: var(--color-white);
  --success-border: var(--color-green-600);
  --success-hover: oklch(0.6929 0.1924 147.82);
  --success-active: oklch(0.6311 0.1924 147.82);

  --warning: oklch(0.8189 0.1301 68.1);
  --warning-foreground: var(--color-black);
  --warning-border: oklch(0.7045 0.1301 68.1);
  --warning-hover: oklch(0.7527 0.1301 68.1);
  --warning-active: oklch(0.7045 0.1301 68.1);

  --danger: var(--color-red-500);
  --danger-foreground: var(--color-white);
  --danger-border: oklch(0.5181 0.1973 27.8);
  --danger-hover: oklch(0.5853 0.237 25.331);
  --danger-active: oklch(0.5181 0.1973 27.8);
  --danger-text: var(--color-rose-600);

  --border: var(--color-gray-200);

  --input: var(--color-gray-400);
  --input-ring: oklch(0.6058 0.1674 252.7);
  --input-error: var(--color-red-400);
  --input-error-ring: var(--color-red-500);

  --card: var(--color-white);
  --card-foreground: var(--color-black);

  --dialog: var(--color-white);

  --popover: var(--color-white);
  --popover-foreground: var(--color-black);
  --popover-focus: var(--color-gray-200);
  --popover-active: var(--color-gray-300);

  --muted: var(--color-gray-500);
  /*--link: oklch(0.4868 0.1459 257.66);*/
  --link: var(--color-blue-800);
}

[data-theme="dark"] {
  @apply antialiased;

  --background: var(--color-zinc-900);
  --foreground: oklch(0.985 0 0);

  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-white);
  --primary-border: var(--color-blue-300);
  --primary-hover: oklch(0.5104 0.1634 257.63);
  --primary-active: oklch(0.4486 0.1634 257.63);

  --secondary: var(--color-neutral-700);
  --secondary-foreground: var(--color-white);
  --secondary-border: var(--color-neutral-500);
  --secondary-hover: var(--color-neutral-600);
  --secondary-active: var(--color-neutral-700);

  --success: var(--color-green-900);
  --success-foreground: var(--color-white);
  --success-border: var(--color-green-600);
  --success-hover: oklch(0.4692 0.095 152.535);
  --success-active: oklch(0.3662 0.095 152.535);

  --warning: oklch(0.8189 0.1301 68.1);
  --warning-foreground: var(--color-black);
  --warning-border: var(--color-orange-300);
  --warning-hover: oklch(0.7527 0.1301 68.1);
  --warning-active: oklch(0.6733 0.1301 68.1);

  --danger: var(--color-red-900);
  --danger-foreground: var(--color-red-200);
  --danger-border: var(--color-red-300);
  --danger-hover: oklch(0.4529 0.141 25.723);
  --danger-active: oklch(0.4164 0.141 25.723);
  --danger-text: var(--color-rose-300);

  --border: var(--color-neutral-700);

  --input: var(--color-neutral-500);
  --input-ring: oklch(0.6058 0.1674 252.7);
  --input-error: var(--color-red-300);
  --input-error-ring: var(--color-red-500);

  --card: var(--color-zinc-800);
  --card-foreground: var(--color-white);

  --dialog: var(--color-zinc-800);

  --popover: var(--dialog);
  --popover-foreground: var(--foreground);
  --popover-focus: var(--color-zinc-700);
  --popover-active: var(--color-zinc-600);

  --muted: var(--color-gray-400);
  --link: var(--color-blue-400);
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border, currentColor);
  }
}

html {
  /*font-size: 0.9375rem; !* 15px *!*/
}

body {
  @apply bg-background text-foreground;
}

svg {
  @apply flex-none;
}

.input-select {
  @apply !pr-8 text-ellipsis;
  /*background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' viewBox='0 0 24 24'%3E%3Cpath d='m6 9 6 6 6-6'/%3E%3C/svg%3E");*/
  /*background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9Imx1Y2lkZSBsdWNpZGUtY2hldnJvbnMtdXAtZG93bi1pY29uIGx1Y2lkZS1jaGV2cm9ucy11cC1kb3duIj48cGF0aCBkPSJtNyAxNSA1IDUgNS01Ii8+PHBhdGggZD0ibTcgOSA1LTUgNSA1Ii8+PC9zdmc+");*/
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWNoZXZyb24tZG93bi1pY29uIGx1Y2lkZS1jaGV2cm9uLWRvd24iPjxwYXRoIGQ9Im02IDkgNiA2IDYtNiIvPjwvc3ZnPg==");
  background-repeat: no-repeat;
  background-position: calc(100% - 8px);
}

[data-theme="dark"] .input-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' viewBox='0 0 24 24'%3E%3Cpath d='m6 9 6 6 6-6'/%3E%3C/svg%3E");
}

.form-row {
  @apply data-[cols='1']:sm:grid-cols-1;
  @apply data-[cols='2']:sm:grid-cols-2;
  @apply data-[cols='3']:sm:grid-cols-3;
  @apply data-[cols='4']:sm:grid-cols-4;
  @apply data-[cols='5']:sm:grid-cols-5;
  @apply data-[cols='6']:sm:grid-cols-6;
  @apply data-[cols='7']:sm:grid-cols-7;
  @apply data-[cols='8']:sm:grid-cols-8;
  @apply data-[cols='9']:sm:grid-cols-9;
  @apply data-[cols='10']:sm:grid-cols-10;
}

.temp-animation {
  animation-iteration-count: 3 !important;
}

mark {
  @apply bg-yellow-300 box-decoration-clone dark:bg-yellow-400;
}

.iras-building {
  background: url("/images/IRAS_building.png") no-repeat;
  background-size: cover;
  clip-path: ellipse(230px 150px at right 100%);
}

.hstack {
  @apply flex items-center gap-2;
}

.form-table {
  @apply w-full table-auto;
}

.form-table td {
  @apply w-1/2 border border-gray-400 p-2 align-top;
}

.onboarding-table {
  @apply w-full table-auto bg-background;
}

.onboarding-table th {
  @apply bg-gray-50 text-left font-normal text-gray-500 dark:bg-neutral-700 dark:text-gray-300;
}

.onboarding-table td,
.onboarding-table th {
  @apply border border-gray-400 px-2 py-1 align-top;
}

.basic-property-table {
  @apply w-full table-auto;
}

.basic-property-table td {
  @apply border-b;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -webkit-overflow-scrolling: auto;
  -ms-overflow-style: none;
  scrollbar-width: none; /* Firefox */
}

.mask-left-right {
  mask-image: linear-gradient(
    to right,
    transparent,
    black 10px,
    black 95%,
    transparent
  );
}

.mask-right {
  mask-image: linear-gradient(
    to right,
    black,
    black 10px,
    black 95%,
    transparent
  );
}

.mask-left {
  mask-image: linear-gradient(
    to left,
    black,
    black 10px,
    black 95%,
    transparent
  );
}

.notion-dropdown-menu-shadow {
  box-shadow:
    rgba(0, 0, 0, 0.1) 0 14px 28px -6px,
    rgba(0, 0, 0, 0.06) 0 2px 4px -1px,
    rgba(84, 72, 49, 0.08) 0 0 0 1px;
}

.notion-dropdown-menu-shadow-light {
  box-shadow:
    rgba(0, 0, 0, 0.1) 0 4px 8px -6px,
    rgba(0, 0, 0, 0.06) 0 2px 4px -1px,
    rgba(84, 72, 49, 0.15) 0 0 0 1px;
}

.text-underline-link {
  @apply text-link underline underline-offset-4;
}

.btn-box-shadow {
  box-shadow:
    inset 0 0 0 1px rgba(17, 20, 24, 0.2),
    0 1px 2px rgba(17, 20, 24, 0.1);
}

[data-theme="dark"] .btn-box-shadow {
  box-shadow:
    inset 0 0 0 1px hsla(0, 0%, 100%, 0.1),
    0 1px 2px rgba(17, 20, 24, 0.2);
}

.payroll-shadow {
  @apply shadow-md shadow-neutral-200 dark:shadow-none;
}
