import { useState } from "react";
import { useForm } from "react-hook-form";
import { Form, FormRow, FormSection } from "@/ui/Form";
import Card from "@/ui/Card";
import H1 from "@/app/(examples)/examples/H1";
import SingleFileUploader from "@/ui/SingleFileUploader";
import Button from "@/ui/Button";

export default function SingleFileUploaderExamples() {
  const [formData, setFormData] = useState({});
  const form = useForm({
    defaultValues: {
      attachment: null,
    },
  });

  const onSubmit = (data) => {
    setFormData(data);
  };

  return (
    <Card>
      <Form form={form} onSubmit={onSubmit} formData={formData} debug>
        <FormSection>
          <H1>Single File Upload Example</H1>

          <FormRow>
            <SingleFileUploader
              name="attachment"
              rules={{ required: "Attachment is required!" }}
              accept={["images", "pdf"]}
              previewSize={400}
              minFileSize={1024} // 1KB
              maxFileSize={1 * 1024 * 1024} // 1MB
              enablePreview
            />
          </FormRow>
          <Button type="submit">Submit</Button>
        </FormSection>
      </Form>
    </Card>
  );
}
