import { useState } from "react";
import { useChat } from "@ai-sdk/react";

export default function ChatMessageInput({ chat }) {
  const [input, setInput] = useState("");
  const { sendMessage } = useChat({ chat });

  return (
    <form
      className="flex justify-center pt-24"
      onSubmit={(e) => {
        e.preventDefault();
        sendMessage({ text: input });
        setInput("");
      }}
    >
      <input
        className="fixed bottom-0 mb-4 min-w-md rounded-full border border-zinc-300 bg-background p-4 shadow-xl dark:border-zinc-800 dark:bg-zinc-900"
        value={input}
        placeholder="Ask PO questions..."
        onChange={(e) => setInput(e.currentTarget.value)}
      />
    </form>
  );
}
