import { useChat } from "@ai-sdk/react";
import { useEffect, useRef } from "react";

export default function ChatScrollToBottom({ chat }) {
  const { messages } = useChat({ chat });
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return <div ref={messagesEndRef} />;
}
