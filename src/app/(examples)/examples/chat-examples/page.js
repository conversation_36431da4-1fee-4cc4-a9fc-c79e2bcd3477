"use client";

import { Chat, useChat } from "@ai-sdk/react";
import { DefaultChatTransport } from "ai";
import cn from "@/lib/cn";
import VStack from "@/ui/VStack";
import { MemoizedMarkdown } from "@/app/(examples)/examples/chat-examples/ChatMarkdownBlock";
import ChatMessageInput from "@/app/(examples)/examples/chat-examples/ChatMessageInput";
import ChatScrollToBottom from "@/app/(examples)/examples/chat-examples/ChatScrollToBottom";

const URL = "http://localhost:8000/ai_api/chats";

const chat = new Chat({
  transport: new DefaultChatTransport({
    api: URL,
    credentials: "include",
  }),
});

export default function Page() {
  const { messages } = useChat({ chat });
  // For testing using Next.js Node API /api/chat URL instead of Rails backend
  // const { messages, sendMessage } = useChat();

  return (
    <div className="">
      <div className="relative overflow-y-auto">
        <div className="stretch mx-auto flex w-full flex-col space-y-4">
          {messages.map((message, index) => {
            const bot = message.role !== "user";
            const user = message.role === "user";

            const containerClassNames = cn("flex", {
              "justify-end": user,
            });

            const messageClassNames = cn("p-2", {
              "mt-4 rounded-3xl rounded-br-lg bg-gray-200/80 px-4": user,
              "w-full border-l-3 border-sky-700/50 px-2 py-0": bot,
            });

            return (
              <div
                key={`${message.id}-${index}`}
                className="tiptap whitespace-pre-wrap"
              >
                <div className={containerClassNames}>
                  <div className={messageClassNames}>
                    <VStack className="gap-0">
                      {bot && <Bot2Icon className="stroke-sky-700/50" />}

                      {message.parts.map((part, i) => {
                        switch (part.type) {
                          case "text":
                            return bot ? (
                              <MemoizedMarkdown
                                key={`${message.id}-${i}`}
                                id={message.id}
                                content={part.text}
                              />
                            ) : (
                              <div key={`${message.id}-${i}`}>{part.text}</div>
                            );
                        }
                      })}
                    </VStack>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Invisible element for scrolling purposes only */}
        <ChatScrollToBottom chat={chat} />
      </div>

      <ChatMessageInput chat={chat} />
    </div>
  );
}

const BotIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      id="Science-Fiction-Robot--Streamline-Streamline-3.0"
      height={24}
      width={24}
    >
      <g>
        <path
          d="M15.75 5.75A0.25 0.25 0 0 0 16 5.5V4a4 4 0 0 0 -8 0v1.5a0.25 0.25 0 0 0 0.25 0.25Z"
          fill="#000000"
          strokeWidth={1}
        />
        <path
          d="M7.25 17.25a0.25 0.25 0 0 0 -0.25 0.25V22H6a1 1 0 0 0 0 2h4.5a1 1 0 0 0 0.5 -1.87V17.5a0.25 0.25 0 0 0 -0.25 -0.25Z"
          fill="#000000"
          strokeWidth={1}
        />
        <path
          d="M13.25 17.25a0.25 0.25 0 0 0 -0.25 0.25v4.63a1 1 0 0 0 0.5 1.87H18a1 1 0 0 0 0 -2h-1v-4.5a0.25 0.25 0 0 0 -0.25 -0.25Z"
          fill="#000000"
          strokeWidth={1}
        />
        <path
          d="M22.5 13.77V11.6a4.49 4.49 0 0 0 -3.56 -4.43 0.26 0.26 0 0 0 -0.24 0.07 0.26 0.26 0 0 0 -0.06 0.25 15.15 15.15 0 0 1 0.41 1.71 0.24 0.24 0 0 0 0.13 0.18 2.43 2.43 0 0 1 1.32 2.22v2.17a2 2 0 1 0 2 0Z"
          fill="#000000"
          strokeWidth={1}
        />
        <path
          d="M1.5 11.6v2.17a2 2 0 1 0 2 0V11.6a2.41 2.41 0 0 1 1.32 -2.22A0.24 0.24 0 0 0 5 9.2a15.15 15.15 0 0 1 0.41 -1.71 0.26 0.26 0 0 0 -0.06 -0.25 0.26 0.26 0 0 0 -0.24 -0.07A4.49 4.49 0 0 0 1.5 11.6Z"
          fill="#000000"
          strokeWidth={1}
        />
        <path
          d="M17 7.42a0.25 0.25 0 0 0 -0.23 -0.17H7.19a0.25 0.25 0 0 0 -0.19 0.17 12.16 12.16 0 0 0 0 8.16 0.25 0.25 0 0 0 0.23 0.17h9.62a0.25 0.25 0 0 0 0.23 -0.17A12.16 12.16 0 0 0 17 7.42Zm-2.5 4.08a1 1 0 1 1 1 -1 1 1 0 0 1 -1 1Z"
          fill="#000000"
          strokeWidth={1}
        />
      </g>
    </svg>
  );
};

const Bot2Icon = ({ className }) => {
  return (
    <svg
      viewBox="0 0 20 20"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      height={20}
      width={20}
    >
      <path
        d="M11.25 1.6666666666666667c0 0.3702083333333333 -0.16091666666666668 0.702825 -0.4166666666666667 0.9317083333333334V4.166666666666667h4.166666666666667c1.3807500000000001 0 2.5 1.1192916666666668 2.5 2.5v8.333333333333334c0 1.3807500000000001 -1.11925 2.5 -2.5 2.5H5c-1.3807083333333334 0 -2.5 -1.11925 -2.5 -2.5V6.666666666666667c0 -1.3807083333333334 1.1192916666666668 -2.5 2.5 -2.5h4.166666666666667V2.5983750000000003c-0.25575000000000003 -0.22888333333333336 -0.4166666666666667 -0.5615 -0.4166666666666667 -0.9317083333333334 0 -0.6903583333333334 0.5596666666666666 -1.25 1.25 -1.25s1.25 0.5596416666666667 1.25 1.25ZM5 5.833333333333334c-0.4602333333333333 0 -0.8333333333333334 0.37310000000000004 -0.8333333333333334 0.8333333333333334v8.333333333333334c0 0.46025000000000005 0.37310000000000004 0.8333333333333334 0.8333333333333334 0.8333333333333334h10c0.46025000000000005 0 0.8333333333333334 -0.3730833333333333 0.8333333333333334 -0.8333333333333334V6.666666666666667c0 -0.4602333333333333 -0.3730833333333333 -0.8333333333333334 -0.8333333333333334 -0.8333333333333334H5Zm-3.3333333333333335 2.5H0v5h1.6666666666666667v-5Zm16.666666666666668 0h1.6666666666666667v5h-1.6666666666666667v-5ZM7.5 12.083333333333334c0.6903583333333334 0 1.25 -0.5596666666666666 1.25 -1.25s-0.5596416666666667 -1.25 -1.25 -1.25 -1.25 0.5596666666666666 -1.25 1.25 0.5596416666666667 1.25 1.25 1.25Zm5 0c0.6903333333333334 0 1.25 -0.5596666666666666 1.25 -1.25s-0.5596666666666666 -1.25 -1.25 -1.25 -1.25 0.5596666666666666 -1.25 1.25 0.5596666666666666 1.25 1.25 1.25Z"
        strokeWidth={0.8333}
      />
    </svg>
  );
};
