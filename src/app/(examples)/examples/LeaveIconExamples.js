"use client";

import LeaveIcon from "@/icons/leaves/icons";
import Card from "@/ui/Card";
import H1 from "@/app/(examples)/examples/H1";
import VStack from "@/ui/VStack";
import useCopyToClipboard from "@/hooks/useCopyToClipboard";
import { toast } from "sonner";
import { useState } from "react";
import { Slider } from "radix-ui";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import { CheckIcon, CopyIcon } from "lucide-react";
import { FormRow, FormSection } from "@/ui/Form";
import Input from "@/ui/Input";
import { isEmpty } from "lodash";
import cn from "@/lib/cn";
import Link from "next/link";

const LEAVE_TYPES = [
  "AL",
  "ML",
  "WML",
  "HOSL",
  "WHOSL",
  "RL",
  "BDL",
  "MRL",
  "CPL",
  "MTNL",
  "P<PERSON>",
  "SPL",
  "CCL",
  "ECCL",
  "FCL",
  "ICT",
];

export default function LeaveIconExamples({
  minStrokeWidth = 1,
  maxStrokeWidth = 4,
  minSize = 12,
  maxSize = 36,
}) {
  const defaultForm = {
    leaveType: null,
    strokeWidth: 1.5,
    size: 24,
    color: "#000",
  };
  const [form, setForm] = useState(defaultForm);
  const { leaveType, strokeWidth, size, color } = form;

  const [_, copy] = useCopyToClipboard();
  const [copied, setCopied] = useState(false);

  const code = leaveType
    ? `<LeaveIcon type="${leaveType}" color="${color}" strokeWidth={${strokeWidth}} size={${size}} />`
    : null;

  const handleFormChange = (name, value) =>
    setForm((f) => ({ ...f, [name]: value }));

  const handleCopy = () => {
    copy(code).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 1000);
    });
    toast.success(`${leaveType} copied!`);
  };

  return (
    <Card id="leave-icons" className="flex flex-col space-y-4">
      <Link href="/examples#leave-icons" className="group/leave-icons">
        <H1>
          Leave Icons{" "}
          <span className="hidden group-hover/leave-icons:inline-block">#</span>
        </H1>
      </Link>

      <FormSection title="Props">
        <FormRow>
          <VStack>
            <p>Stroke width: {strokeWidth}</p>
            <HStack>
              <span className="text-sm">{minStrokeWidth}</span>
              <Slider.Root
                className="relative flex h-5 w-[200px] touch-none items-center select-none"
                defaultValue={[defaultForm.strokeWidth]}
                min={minStrokeWidth}
                max={maxStrokeWidth}
                step={0.5}
                onValueChange={(value) =>
                  handleFormChange("strokeWidth", value[0])
                }
              >
                <Slider.Track className="relative h-1 grow rounded-full bg-gray-400">
                  <Slider.Range className="absolute h-full rounded-full bg-blue-700" />
                </Slider.Track>
                <Slider.Thumb
                  className="hover:bg-violet block size-4 rounded-full bg-white outline-2 outline-gray-400 duration-100 active:outline-4 active:outline-blue-700"
                  aria-label="Volume"
                />
              </Slider.Root>
              <span className="text-sm">{maxStrokeWidth}</span>
            </HStack>
          </VStack>

          <VStack>
            <p>Size: {size}px</p>
            <HStack>
              <span className="text-sm">{minSize}</span>
              <Slider.Root
                className="relative flex h-5 w-[200px] touch-none items-center select-none"
                defaultValue={[defaultForm.size]}
                min={minSize}
                max={maxSize}
                step={2}
                onValueChange={(value) => handleFormChange("size", value[0])}
              >
                <Slider.Track className="relative h-1 grow rounded-full bg-gray-400">
                  <Slider.Range className="absolute h-full rounded-full bg-blue-700" />
                </Slider.Track>
                <Slider.Thumb
                  className="hover:bg-violet block size-4 rounded-full bg-white outline-2 outline-gray-400 duration-100 active:outline-4 active:outline-blue-700"
                  aria-label="Volume"
                />
              </Slider.Root>
              <span className="text-sm">{maxSize}</span>
            </HStack>
          </VStack>
        </FormRow>

        <FormRow>
          <VStack>
            <p>Color: {color}</p>

            <HStack className="gap-6">
              <HStack>
                {[
                  "#000",
                  "#6b7280",
                  "#3b82f6",
                  "#ef4444",
                  "#22c55e",
                  "#eab308",
                  "#f97316",
                ].map((color) => (
                  <button
                    key={color}
                    type="button"
                    className="h-6 w-6 cursor-pointer rounded-full outline-none"
                    style={{ backgroundColor: color }}
                    onClick={() => handleFormChange("color", color)}
                  />
                ))}
              </HStack>

              <div>
                <Input
                  label="HEX code"
                  value={color}
                  onChange={(e) => handleFormChange("color", e.target.value)}
                />
              </div>
            </HStack>
          </VStack>
        </FormRow>
      </FormSection>

      <div className="grid grid-cols-4 gap-4">
        {LEAVE_TYPES.map((leaveType) => (
          <Card
            key={leaveType}
            className={cn(
              "cursor-pointer duration-100 hover:bg-gray-50 active:scale-95",
              {
                "border-transparent outline-2": leaveType === form.leaveType,
              },
            )}
            onClick={() => handleFormChange("leaveType", leaveType)}
            style={{ outlineColor: color }}
          >
            <VStack className="items-center-safe gap-1">
              <LeaveIcon
                type={leaveType}
                color={color}
                strokeWidth={strokeWidth}
                size={size}
              />
              <p className="text-sm text-muted">{leaveType}</p>
            </VStack>
          </Card>
        ))}
      </div>

      <Card className="relative overflow-x-scroll border-transparent bg-gray-50">
        <pre className="text-xs text-gray-700">
          {'import LeaveIcon from "@/icons/leaves/icons";\n\n'}

          {leaveType === "MTNL"
            ? "// MTNL is taken from Streamline icon, stroke width might differ\n"
            : null}
          {code || "Select a leave type"}
        </pre>
      </Card>
      <HStack className="justify-between">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setForm(defaultForm)}
          outline
        >
          Reset
        </Button>
        <Button
          variant="secondary"
          size="sm"
          prefix={copied ? <CheckIcon size={16} /> : <CopyIcon size={16} />}
          onClick={handleCopy}
          outline
          disabled={isEmpty(leaveType)}
        >
          {copied ? "Copied" : "Copy component"}
        </Button>
      </HStack>
    </Card>
  );
}
