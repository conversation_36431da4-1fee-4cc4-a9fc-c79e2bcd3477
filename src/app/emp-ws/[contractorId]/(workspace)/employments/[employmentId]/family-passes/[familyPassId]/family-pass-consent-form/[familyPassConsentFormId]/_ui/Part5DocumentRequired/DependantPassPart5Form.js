import { useFormContext, useWatch } from "react-hook-form";
import Button from "@/ui/Button";
import { useEffect } from "react";
import { isEmpty, isNil } from "lodash";
import ageCalculator from "@/lib/ageCalculator";
import VStack from "@/ui/VStack";
import { useDropzone } from "react-dropzone";
import cn from "@/lib/cn";
import { UploadIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import XCircleFilledIcon from "@/icons/emp/XCircleFilledIcon";

export default function DependantPassPart5Form({
  spouseRelationship,
  childRelationship,
}) {
  const {
    control,
    register,
    clearErrors,
    formState: { errors },
  } = useFormContext();

  const watchPart1Q1Answer = useWatch({
    control,
    name: "particulars.0.answer",
  });
  const localDependant = useWatch({
    control,
    name: "localDependant",
  });
  const watchDob = useWatch({ control, name: "dob" });

  const watchHighestQualification = useWatch({
    control,
    name: "highestQualification",
  });
  const age = ageCalculator(watchDob);

  const unMarriedChildBelow12 =
    watchPart1Q1Answer === "Unmarried Child" &&
    localDependant === "No" &&
    !isNil(age) &&
    age <= 12;

  register("passport", {
    required: true,
  });

  register("marriageCertificate", {
    required: spouseRelationship,
  });

  register("dependantBirthCertificate", {
    required: childRelationship,
  });

  register("educationCertificate", {
    required:
      watchHighestQualification !== "No Formal Education/Primary Education",
  });

  register("hpbVaccination", {
    required: unMarriedChildBelow12,
  });

  // Education check
  useEffect(() => {
    if (!isEmpty(watchHighestQualification)) {
      if (
        watchHighestQualification === "No Formal Education/Primary Education"
      ) {
        clearErrors("educationCertificate");
      }
    }
  }, [clearErrors, watchHighestQualification]);

  // Education check
  useEffect(() => {
    if (!unMarriedChildBelow12) {
      clearErrors("hpbVaccination");
    }
  }, [clearErrors, unMarriedChildBelow12]);

  return (
    <table className="onboarding-table">
      <thead>
        <tr>
          <th>Documents</th>
          <th>Upload</th>
        </tr>
      </thead>

      <tbody>
        <tr className={cn({ "bg-red-50 text-red-600": errors.passport })}>
          <td>
            <strong>Passport</strong>
          </td>
          <td>
            <ClientUploader name={"passport"} />
          </td>
        </tr>

        {spouseRelationship && (
          <tr
            className={cn({
              "bg-red-50 text-red-600": errors.marriageCertificate,
            })}
          >
            <td>
              <strong>Marriage certificate</strong>
            </td>
            <td>
              <ClientUploader name={"marriageCertificate"} />
            </td>
          </tr>
        )}

        {childRelationship && (
          <tr
            className={cn({
              "bg-red-50 text-red-600": errors.dependantBirthCertificate,
            })}
          >
            <td>
              <strong>Birth certificate</strong>
            </td>
            <td>
              <ClientUploader name={"dependantBirthCertificate"} />
            </td>
          </tr>
        )}

        <tr
          className={cn({
            "bg-red-50 text-red-600": errors.educationCertificate,
          })}
        >
          <td>
            <strong>Education certificate</strong>{" "}
            {watchHighestQualification &&
              watchHighestQualification ===
                "No Formal Education/Primary Education" && (
                <span className="text-muted">(optional)</span>
              )}
          </td>
          <td>
            <ClientUploader name={"educationCertificate"} />
          </td>
        </tr>

        {unMarriedChildBelow12 && (
          <tr
            className={cn({ "bg-red-50 text-red-600": errors.hpbVaccination })}
          >
            <td>
              <VStack className="gap-0">
                <strong>Verrification of vaccination requirements</strong>
                <p
                  className={cn("text-muted text-sm", {
                    "text-red-600/70": errors.hpbVaccination,
                  })}
                >
                  (for entry to Singapore) document issued by Health Promotion
                  Board&apos;s (HPB)
                </p>
              </VStack>
            </td>
            <td>
              <ClientUploader name={"hpbVaccination"} />
            </td>
          </tr>
        )}

        <tr className={cn({ "bg-red-50 text-red-600": errors.currentPass })}>
          <td>
            <VStack className="gap-0">
              <strong>
                Current pass{" "}
                <span className="text-muted font-normal">(optional)</span>
              </strong>
              <p className="text-muted text-sm">
                Dependant Pass or Social Visit Pass
              </p>
            </VStack>
          </td>
          <td>
            <ClientUploader name={"currentPass"} />
          </td>
        </tr>
      </tbody>
    </table>
  );
}

const ClientUploader = ({ name }) => {
  const {
    control,
    setValue,
    clearErrors,
    formState: { errors },
  } = useFormContext();

  const watchFile = useWatch({
    control,
    name,
  });

  const hasError = errors[name];

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false,
    onDrop: (files) => {
      setValue(name, files[0]);
      clearErrors(name);
    },
  });

  const dropzoneClassName = cn(
    "text-muted relative rounded-lg text-sm transition-all",
    {
      "bg-gray-100": isDragActive && !hasError,
      "bg-red-100": isDragActive && hasError,
      "text-red-600/80": hasError,
    },
  );

  if (watchFile) {
    return (
      <HStack className="flex-nowrap justify-between">
        <div
          title={watchFile.name}
          className="w-[500px] truncate overflow-hidden"
        >
          {watchFile.name}
        </div>
        <Button
          variant="plainIcon"
          size="sm"
          onClick={() => {
            setValue(name, null);
          }}
          suffix={<XCircleFilledIcon color="#9ca3af" />}
        />
      </HStack>
    );
  }

  return (
    <div {...getRootProps()} className={dropzoneClassName}>
      <input {...getInputProps()} />
      <VStack className="items-center py-4 text-xs">
        <UploadIcon size={24} strokeWidth={1.5} />
        Drop file here (or click to browse)
      </VStack>
    </div>
  );
};
