import Card from "@/ui/Card";
import {
  BasicPropertyTable,
  Row,
} from "@/components/table-variants/BasicPropertyTable";

export default function Part3Submitted({ familyPassConsentForm }) {
  return (
    <>
      <h2 className="border-b py-2 text-lg leading-none font-semibold text-amber-800">
        Part 3: Dependant Highest Education Qualification
      </h2>

      <Card variant="light" className="space-y-4">
        <BasicPropertyTable>
          <Row
            property="Qualification"
            value={familyPassConsentForm.highestQualification}
          />
        </BasicPropertyTable>
      </Card>
    </>
  );
}
