import VStack from "@/ui/VStack";
import Card from "@/ui/Card";

export default function Part1Submitted({ familyPassConsentForm }) {
  const divClassNames = "grid grid-cols-[32px_1fr]";

  return (
    <>
      <h2 className="border-b py-2 text-lg leading-none font-semibold text-amber-800">
        Part 1: Dependant Particulars
      </h2>

      <Card variant="light">
        <table className="form-table">
          <tbody>
            <tr>
              <td className="space-y-2">
                <div className={divClassNames}>
                  1.{" "}
                  <strong>
                    {familyPassConsentForm.particulars[0].question}
                  </strong>
                </div>

                <div className={divClassNames}>
                  <div />
                  <div>{familyPassConsentForm.particulars[0].answer}</div>
                </div>
              </td>
            </tr>

            <tr>
              <td className="space-y-2">
                <div className={divClassNames}>
                  2.{" "}
                  <strong>
                    {familyPassConsentForm.particulars[1].question}
                  </strong>
                </div>

                <div className={divClassNames}>
                  <div />
                  <VStack>
                    <div>{familyPassConsentForm.particulars[1].answer}</div>

                    {familyPassConsentForm.finNumber && (
                      <div className="text-muted">
                        FIN number: {familyPassConsentForm.finNumber}
                      </div>
                    )}
                  </VStack>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </Card>
    </>
  );
}
