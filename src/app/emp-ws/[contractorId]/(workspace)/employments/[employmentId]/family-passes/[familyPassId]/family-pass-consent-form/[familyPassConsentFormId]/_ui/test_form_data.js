import { formatDateForDatePicker } from "@/ui/Form";

export const TEST_FORM_DATA = {
  particulars: [
    {
      question: "How is the dependant related to you?",
      answer: "Spouse",
      subAnswer: "Legally married spouse",
    },
    {
      question:
        "Has your dependant ever studied, worked or stayed long-term (not as a tourist) in Singapore before?",
      answer:
        "Yes, dependant has worked/studied/stayed in Singapore in the past",
    },
  ],
  hasFinNumber: "true",
  finNumber: "M4254419K",

  name: "<PERSON>",
  dob: formatDateForDatePicker(new Date(2000, 9, 15)),
  gender: "Male",
  nationality: "Malaysian",
  race: "Chinese",
  religion: "Buddhist",
  maritalStatus: "Married",

  email: "<EMAIL>",
  homeNumber: "",
  mobileNumber: "81740915",
  localAddress: "Blk 271 Bishan street 24 #02-224 Singapore 570271",

  travelDocumentType: "International Passport",
  travelDocumentNumber: "*********",
  travelDocumentIssueDate: formatDateForDatePicker(new Date(2010, 9, 1)),
  travelDocumentExpiryDate: formatDateForDatePicker(new Date(2020, 11, 1)),
  travelDocumentCountryOfBirth: "Malaysia",
  travelDocumentCountryOfOrigin: "Malaysia",
  travelDocumentStateProvinceOfBirth: "Perak",
  travelDocumentStateProvinceOfOrigin: "Perak",
  travelDocumentMalaysianIcNumber: "000915080521",

  highestQualification: "Bachelor's Degree",

  declarations: [
    {
      question: "Been refused entry into or deported from any country?",
      answer: "No",
    },
    {
      question: "Been convicted in a court of law in any country?",
      answer: "No",
    },
    {
      question: "Been prohibited from entering Singapore?",
      answer: "No",
    },
    {
      question:
        "Entered Singapore using a passport issued by a different country?",
      answer: "No",
    },
    {
      question: "Entered Singapore using a different name?",
      answer: "No",
    },
    {
      question: "Been a Singapore Citizen or Singapore Permanent Resident?",
      answer: "Yes",
      details: "Test",
    },
  ],

  ack1: true,
  ack2: true,
  ack3: true,
};

export const TEST_CHILD_FORM_DATA = {
  particulars: [
    {
      question: "How is the dependant related to you?",
      answer: "Unmarried Child",
      subAnswer: "Biological child under 21 years",
    },
    {
      question:
        "Has your dependant ever studied, worked or stayed long-term (not as a tourist) in Singapore before?",
      answer:
        "Yes, dependant has worked/studied/stayed in Singapore in the past",
    },
  ],
  localDependant: "No",
  hasFinNumber: "true",
  finNumber: "M4254419K",

  name: "Chen Yan Jia",
  dob: formatDateForDatePicker(new Date(2025, 9, 15)),
  gender: "Female",
  nationality: "Malaysian",
  race: "Chinese",
  religion: "Buddhist",
  maritalStatus: "Single",
  hpbVaccinationReferenceNumber: "1234567890",

  email: "<EMAIL>",
  mobileNumber: "81740915",
  localAddress: "Blk 271 Bishan street 24 #02-224 Singapore 570271",

  travelDocumentType: "International Passport",
  travelDocumentNumber: "*********",
  travelDocumentIssueDate: formatDateForDatePicker(new Date(2010, 9, 1)),
  travelDocumentExpiryDate: formatDateForDatePicker(new Date(2020, 11, 1)),
  travelDocumentCountryOfBirth: "Malaysia",
  travelDocumentCountryOfOrigin: "Malaysia",
  travelDocumentStateProvinceOfBirth: "Perak",
  travelDocumentStateProvinceOfOrigin: "Perak",
  travelDocumentMalaysianIcNumber: "000915080521",

  highestQualification: "Bachelor's Degree",

  declarations: [
    {
      question: "Been refused entry into or deported from any country?",
      answer: "No",
    },
    {
      question: "Been convicted in a court of law in any country?",
      answer: "No",
    },
    {
      question: "Been prohibited from entering Singapore?",
      answer: "No",
    },
    {
      question:
        "Entered Singapore using a passport issued by a different country?",
      answer: "No",
    },
    {
      question: "Entered Singapore using a different name?",
      answer: "No",
    },
    {
      question: "Been a Singapore Citizen or Singapore Permanent Resident?",
      answer: "Yes",
      details: "Test",
    },
  ],

  ack1: true,
  ack2: true,
  ack3: true,
};
