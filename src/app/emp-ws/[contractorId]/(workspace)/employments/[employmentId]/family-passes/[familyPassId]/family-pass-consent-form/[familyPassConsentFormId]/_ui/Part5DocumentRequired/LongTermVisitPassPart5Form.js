import { useFormContext, useWatch } from "react-hook-form";
import Button from "@/ui/Button";
import { useEffect } from "react";
import { isEmpty, isNil } from "lodash";
import ageCalculator from "@/lib/ageCalculator";
import VStack from "@/ui/VStack";
import { useDropzone } from "react-dropzone";
import cn from "@/lib/cn";
import { UploadIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import XCircleFilledIcon from "@/icons/emp/XCircleFilledIcon";

export default function LongTermVisitPassPart5Form({ familyPassConsentForm }) {
  const {
    control,
    register,
    setValue,
    clearErrors,
    formState: { errors },
  } = useFormContext();

  const watchPart1Q1Answer = useWatch({
    control,
    name: "particulars.0.answer",
  });

  const watchPart1Q1SubAnswer = useWatch({
    control,
    name: "particulars.0.subAnswer",
  });
  const localDependant = useWatch({
    control,
    name: "localDependant",
  });
  const watchDob = useWatch({ control, name: "dob" });

  const watchHighestQualification = useWatch({
    control,
    name: "highestQualification",
  });
  const age = ageCalculator(watchDob);

  const stepChild = watchPart1Q1SubAnswer === "Step-child under 21 years old";
  const handicappedChild =
    watchPart1Q1SubAnswer === "Handicapped child above 21 years old";
  const parent = ["Parent (Father)", "Parent (Mother)"].includes(
    watchPart1Q1Answer,
  );
  const spouse = ["Spouse (Wife)", "Spouse (Husband)"].includes(
    watchPart1Q1Answer,
  );

  const unMarriedChildBelow12 =
    watchPart1Q1Answer === "Unmarried Child" &&
    localDependant === "No" &&
    !isNil(age) &&
    age <= 12;

  register("passport", {
    required: true,
  });

  register("educationCertificate", {
    required:
      watchHighestQualification !== "No Formal Education/Primary Education",
  });

  register("childCustodyCourtOrderCopy", {
    required: stepChild,
  });
  register("otherBiologicalParentLetter", { required: stepChild });
  register("childMedicalReport", { required: handicappedChild });

  register("hpbVaccination", {
    required: unMarriedChildBelow12 && stepChild,
  });

  register("candidateBirthCertificate", { required: parent });
  register("commonLawMarriageCertificate", { required: spouse });

  // Education check
  useEffect(() => {
    if (!isEmpty(watchHighestQualification)) {
      if (
        watchHighestQualification === "No Formal Education/Primary Education"
      ) {
        clearErrors("educationCertificate");
      }
    }
  }, [clearErrors, watchHighestQualification]);

  // Education check
  useEffect(() => {
    if (!unMarriedChildBelow12 && !stepChild) {
      clearErrors("hpbVaccination");
    }
  }, [clearErrors, stepChild, unMarriedChildBelow12]);

  useEffect(() => {
    if (stepChild) {
      setValue("childMedicalReport", null);
      clearErrors("childMedicalReport");
    } else if (handicappedChild) {
      clearErrors("childCustodyCourtOrderCopy");
      clearErrors("otherBiologicalParentLetter");
      setValue("childCustodyCourtOrderCopy", null);
      setValue("otherBiologicalParentLetter", null);
    }
  }, [clearErrors, stepChild, handicappedChild, setValue]);

  console.log("hi");

  return (
    <table className="onboarding-table">
      <thead>
        <tr>
          <th>Documents</th>
          <th>Upload</th>
        </tr>
      </thead>

      <tbody>
        <tr className={cn({ "bg-red-50 text-red-600": errors.passport })}>
          <td>
            <strong>Passport</strong>
          </td>
          <td>
            <ClientUploader name={"passport"} />
          </td>
        </tr>

        <tr
          className={cn({
            "bg-red-50 text-red-600": errors.educationCertificate,
          })}
        >
          <td>
            <strong>Education certificate</strong>{" "}
            {watchHighestQualification &&
              watchHighestQualification ===
                "No Formal Education/Primary Education" && (
                <span className="text-muted">(optional)</span>
              )}
          </td>
          <td>
            <ClientUploader name={"educationCertificate"} />
          </td>
        </tr>

        {stepChild && (
          <>
            <tr
              className={cn({
                "bg-red-50 text-red-600": errors.childCustodyCourtOrderCopy,
              })}
            >
              <td>
                <VStack className="gap-0">
                  <strong>Court order on the child&apos;s custody</strong>
                  <p
                    className={cn("text-muted text-sm", {
                      "text-red-600/70": errors.childCustodyCourtOrderCopy,
                    })}
                  >
                    A copy of the court order on the child&apos;s custody.
                  </p>
                </VStack>
              </td>
              <td>
                <ClientUploader name={"childCustodyCourtOrderCopy"} />
              </td>
            </tr>

            <tr
              className={cn({
                "bg-red-50 text-red-600": errors.otherBiologicalParentLetter,
              })}
            >
              <td>
                <VStack className="gap-0">
                  <strong>Other biological parent consent letter</strong>
                  <p
                    className={cn("text-muted text-sm", {
                      "text-red-600/70": errors.otherBiologicalParentLetter,
                    })}
                  >
                    A letter from the other biological parent stating there is
                    no objection to bringing the child to Singapore.
                  </p>
                </VStack>
              </td>
              <td>
                <ClientUploader name={"otherBiologicalParentLetter"} />
              </td>
            </tr>

            {unMarriedChildBelow12 && (
              <tr
                className={cn({
                  "bg-red-50 text-red-600": errors.hpbVaccination,
                })}
              >
                <td>
                  <VStack className="gap-0">
                    <strong>Verrification of vaccination requirements</strong>
                    <p
                      className={cn("text-muted text-sm", {
                        "text-red-600/70": errors.hpbVaccination,
                      })}
                    >
                      (for entry to Singapore) document issued by Health
                      Promotion Board&apos;s (HPB)
                    </p>
                  </VStack>
                </td>
                <td>
                  <ClientUploader name={"hpbVaccination"} />
                </td>
              </tr>
            )}
          </>
        )}

        {handicappedChild && (
          <tr
            className={cn({
              "bg-red-50 text-red-600": errors.childMedicalReport,
            })}
          >
            <td>
              <VStack className="gap-0">
                <strong>Child medical report</strong>
                <p
                  className={cn("text-muted text-sm", {
                    "text-red-600/70": errors.childMedicalReport,
                  })}
                >
                  A letter or report from a medical practitioner on the
                  child&apos;s condition.
                </p>
              </VStack>
            </td>
            <td>
              <ClientUploader name={"childMedicalReport"} />
            </td>
          </tr>
        )}

        {parent && (
          <tr
            className={cn({
              "bg-red-50 text-red-600": errors.candidateBirthCertificate,
            })}
          >
            <td>
              <VStack className="gap-0">
                <strong>Birth certificate (work pass holder)</strong>
                <p
                  className={cn("text-muted text-sm", {
                    "text-red-600/70": errors.candidateBirthCertificate,
                  })}
                >
                  A copy of the work pass holder&apos;s birth certificate that
                  states the names of the parents.
                </p>
              </VStack>
            </td>
            <td>
              <ClientUploader name={"candidateBirthCertificate"} />
            </td>
          </tr>
        )}

        {spouse && (
          <tr
            className={cn({
              "bg-red-50 text-red-600": errors.commonLawMarriageCertificate,
            })}
          >
            <td>
              <VStack className="gap-0">
                <strong>Marriage certificate</strong>
                <p
                  className={cn("text-muted text-sm", {
                    "text-red-600/70": errors.commonLawMarriageCertificate,
                  })}
                >
                  A copy of the common-law marriage certificate.
                </p>
              </VStack>
            </td>
            <td>
              <ClientUploader name={"commonLawMarriageCertificate"} />
            </td>
          </tr>
        )}

        <tr className={cn({ "bg-red-50 text-red-600": errors.currentPass })}>
          <td>
            <VStack className="gap-0">
              <strong>
                Current pass{" "}
                <span className="text-muted font-normal">(optional)</span>
              </strong>
              <p className="text-muted text-sm">
                Dependant Pass or Social Visit Pass
              </p>
            </VStack>
          </td>
          <td>
            <ClientUploader name={"currentPass"} />
          </td>
        </tr>
      </tbody>
    </table>
  );
}

const ClientUploader = ({ name }) => {
  const {
    control,
    setValue,
    clearErrors,
    formState: { errors },
  } = useFormContext();

  const watchFile = useWatch({
    control,
    name,
  });

  const hasError = errors[name];

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false,
    onDrop: (files) => {
      setValue(name, files[0]);
      clearErrors(name);
    },
  });

  const dropzoneClassName = cn(
    "text-muted relative rounded-lg text-sm transition-all",
    {
      "bg-gray-100": isDragActive && !hasError,
      "bg-red-100": isDragActive && hasError,
      "text-red-600/80": hasError,
    },
  );

  if (watchFile) {
    return (
      <HStack className="flex-nowrap justify-between">
        <div
          title={watchFile.name}
          className="w-[500px] truncate overflow-hidden"
        >
          {watchFile.name}
        </div>
        <Button
          variant="plainIcon"
          size="sm"
          onClick={() => {
            setValue(name, null);
          }}
          suffix={<XCircleFilledIcon color="#9ca3af" />}
        />
      </HStack>
    );
  }

  return (
    <div {...getRootProps()} className={dropzoneClassName}>
      <input {...getInputProps()} />
      <VStack className="items-center py-4 text-xs">
        <UploadIcon size={24} strokeWidth={1.5} />
        Drop file here (or click to browse)
      </VStack>
    </div>
  );
};
