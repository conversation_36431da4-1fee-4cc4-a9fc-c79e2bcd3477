import Card from "@/ui/Card";
import apiQuery from "@/lib/apiQuery";
import { notFound } from "next/navigation";
import { List, PropertyList } from "@/ui/PropertyList";
import {
  formatDate,
  formatDateTime,
  FormatDateToggle,
} from "@/formatters/date";
import FamilyPassMilestoneAccordion from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/_ui/FamilyPassMilestoneAccordion";

const QUERY = `
  query empFamilyPass($id: ID!) {
    empFamilyPass(id: $id) {
      id
      fmId
      slug
      status
      relationship
      name

      passportNumber
      passportExpiredDate

      passType
      passStatus
      applicationType
      applicationNumber
      finNumber
      periodGranted
      passNumber

      isRenewal

      medicalRequirements

      passSubmittedAt

      familyPassConsentForm{
        id
        slug
        submittedAt
      }

      employmentFiles {
        id
        mainDescription
        description
        status
        fileUrl
        fileName
        fileSize
        fileType
        createdAt
        uploadedAt
      }
    }
  }
`;

export default async function page({ params }) {
  const { familyPassId } = await params;
  const res = await apiQuery(QUERY, { id: familyPassId });
  const familyPass = res.data.empFamilyPass;

  if (familyPass === null) notFound();

  return (
    <div className="mx-auto max-w-[900px] space-y-4 p-4">
      <Card className="space-y-4 border-transparent">
        <h1 className="text-2xl">
          {familyPass.fmId}: {familyPass.passType}
        </h1>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          <PropertyList>
            <List.Status label="Status">{familyPass.status}</List.Status>
            <List.Status label="Pass status">
              {familyPass.passStatus}
            </List.Status>
            <List label="Name">{familyPass.name}</List>
            <List label="Relationship">{familyPass.relationship}</List>
            <List label="Pass type">{familyPass.passType}</List>
            <List label="Application type">{familyPass.applicationType}</List>
            <List label="Application number">
              {familyPass.applicationNumber}
            </List>
            <List label="Period granted">
              {familyPass.periodGranted} months
            </List>
          </PropertyList>

          <PropertyList>
            <List label="Passport number">{familyPass.passportNumber}</List>
            <List label="Passport expiry">
              {formatDate(familyPass.passportExpiredDate)}
            </List>
            <List label="IPA expired date">
              <FormatDateToggle date={familyPass.ipaExpiredDate} />
            </List>
            <List label="Pass issuance date">
              <FormatDateToggle date={familyPass.issuanceDate} />
            </List>
            <List label="Pass expired date">
              <FormatDateToggle date={familyPass.passExpiredDate} />
            </List>
            {familyPass.cardCollectedAt && (
              <List label="Card collection date">
                {formatDateTime(familyPass.cardCollectedAt)}
              </List>
            )}
          </PropertyList>
        </div>
      </Card>

      <FamilyPassMilestoneAccordion familyPass={familyPass} />
    </div>
  );
}
