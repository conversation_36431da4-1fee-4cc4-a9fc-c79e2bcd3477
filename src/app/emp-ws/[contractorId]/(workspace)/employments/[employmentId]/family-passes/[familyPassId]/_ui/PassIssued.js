import HStack from "@/ui/HStack";
import StatusIndicator from "@/ui/StatusIndicator";
import DownloadFileWithIcon from "@/components/DownloadFileWithIcon";
import AlertCard from "@/ui/AlertCard";
import { Item } from "@/components/accordion-variants/left-arrow-basic";
import findEmploymentFile from "@/lib/employment-files/findEmploymentFile";

export default function PassIssued({ familyPass }) {
  return (
    <Item title="Pass Issued" value="3">
      <div className="mx-auto max-w-[500px] py-2 pr-1">
        <AlertCard variant="info">
          Your pass will be issued to you on your first day of work. Please
          remember to mark your attendance on that day.
        </AlertCard>
      </div>
    </Item>
  );
}
