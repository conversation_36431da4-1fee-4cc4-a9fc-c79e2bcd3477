import Card from "@/ui/Card";
import {
  BasicPropertyTable,
  Row,
} from "@/components/table-variants/BasicPropertyTable";
import { formatDate } from "@/formatters/date";

export default function Part2Submitted({ familyPassConsentForm }) {
  return (
    <>
      <h2 className="border-b py-2 text-lg leading-none font-semibold text-amber-800">
        Part 2: Dependant Personal Information
      </h2>

      <Card variant="light" className="space-y-4">
        <h1 className="font-bold text-sky-700">
          A. Dependant Personal Particulars
        </h1>

        <BasicPropertyTable>
          <Row property="Name" value={familyPassConsentForm.name} />
          <Row
            property="Date of birth"
            value={formatDate(familyPassConsentForm.dob)}
          />
          <Row property="Gender" value={familyPassConsentForm.gender} />
          <Row
            property="Nationality"
            value={familyPassConsentForm.nationality}
          />
          <Row
            property="Race"
            value={
              familyPassConsentForm.race === "Others"
                ? familyPassConsentForm.customRace
                : familyPassConsentForm.race
            }
          />
          <Row
            property="Religion"
            value={
              familyPassConsentForm.religion === "Others"
                ? familyPassConsentForm.customReligion
                : familyPassConsentForm.religion
            }
          />
          <Row
            property="Marital status"
            value={familyPassConsentForm.maritalStatus}
          />
        </BasicPropertyTable>

        <h1 className="font-bold text-sky-700">B. Contact Details</h1>

        <BasicPropertyTable>
          <Row property="Email" value={familyPassConsentForm.email} />
          <Row property="Home No." value={familyPassConsentForm.homeNumber} />
          <Row
            property="Mobile No."
            value={familyPassConsentForm.mobileNumber}
          />
          <Row
            property="S'pore address"
            value={familyPassConsentForm.localAddress}
          />
        </BasicPropertyTable>

        <h1 className="font-bold text-sky-700">
          C. Travel Document Information
        </h1>

        <BasicPropertyTable>
          <Row
            propertyClassName="w-[240px]"
            property="Travel document type"
            value={familyPassConsentForm.travelDocumentType}
          />
          <Row
            property="Travel document number"
            value={familyPassConsentForm.travelDocumentNumber}
          />
          <Row
            property="Travel document issued"
            value={formatDate(familyPassConsentForm.travelDocumentIssueDate)}
          />
          <Row
            property="Travel document expired"
            value={formatDate(familyPassConsentForm.travelDocumentExpiryDate)}
          />
          <Row
            property="Country of birth"
            value={familyPassConsentForm.travelDocumentCountryOfBirth}
          />
          <Row
            property="Country of origin"
            value={familyPassConsentForm.travelDocumentCountryOfOrigin}
          />
          <Row
            property="State/province of birth"
            value={familyPassConsentForm.travelDocumentStateProvinceOfBirth}
          />
          <Row
            property="State/province of origin"
            value={familyPassConsentForm.travelDocumentStateProvinceOfOrigin}
          />
          <Row
            property="Malaysian NRIC No."
            value={familyPassConsentForm.travelDocumentMalaysianIcNumber}
          />
        </BasicPropertyTable>
      </Card>
    </>
  );
}
