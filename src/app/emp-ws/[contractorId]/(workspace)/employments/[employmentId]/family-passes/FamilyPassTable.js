import { DataGrid } from "@/ui/DataGrid";
import DateTimeCell from "@/ui/DataGrid/cells/DateTimeCell";
import ResourceLinkCell from "@/ui/DataGrid/cells/ResourceLinkCell";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import Pagination from "@/ui/Pagination";
import { use } from "react";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "WPFPID",
    columnWidth: 120,
    propertyName: "fmId",
    visible: true,
  },
  {
    columnName: "Relationship",
    columnWidth: 120,
    propertyName: "relationship",
    removable: false,
    visible: true,
    href: "/emp-ws/${row.contractor.slug}/employments/${row.employment.slug}/family-passes/${row.slug}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Name",
    columnWidth: 120,
    propertyName: "name",
    visible: true,
    href: "/emp-ws/${row.contractor.slug}/employments/${row.employment.slug}/family-passes/${row.slug}",
    cellComponent: ResourceLinkCell,
  },
  {
    columnName: "Application type",
    columnWidth: 200,
    propertyName: "applicationType",
    visible: true,
  },
  {
    columnName: "Pass Status",
    columnWidth: 120,
    propertyName: "passStatus",
    visible: true,
  },
  {
    columnName: "Status",
    columnWidth: 120,
    propertyName: "status",
    visible: true,
  },
  {
    columnName: "Created",
    columnWidth: 220,
    propertyName: "createdAt",
    visible: true,
    cellComponent: DateTimeCell,
  },
];

export default function FamilyPassTable({ findFamilyPasses, currentPage }) {
  const res = use(findFamilyPasses);

  const {
    records: familyPasses,
    totalPages,
    totalCount,
    processingTimeMs,
  } = getTableMetadata(res.data.empFamilyPasses);

  return (
    <DataGrid.Root className="space-y-2">
      <Pagination currentPage={currentPage} totalPages={totalPages} />
      <DataGrid.TotalCountAndTime
        totalCount={totalCount}
        processingTimeMs={processingTimeMs}
      />
      <DataGrid.Content>
        <DataGridTable
          numRows={familyPasses.length}
          data={familyPasses}
          tableColumns={DEFAULT_TABLE_COLUMNS}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
}
