import { useFormContext, useWatch } from "react-hook-form";
import { FormSection } from "@/ui/Form";
import Input from "@/ui/Input";
import { RadioGroup, RadioGroupItem } from "@/ui/RadioGroup";

const QUESTIONS = [
  "Been refused entry into or deported from any country?",
  "Been convicted in a court of law in any country?",
  "Been prohibited from entering Singapore?",
  "Entered Singapore using a passport issued by a different country?",
  "Entered Singapore using a different name?",
  "Been a Singapore Citizen or Singapore Permanent Resident?",
];

export default function Part4Form({}) {
  return (
    <FormSection>
      <h1>Has your dependant ever:</h1>

      <table className="form-table">
        <tbody>
          {QUESTIONS.map((question, index) => (
            <Question key={index} question={question} index={index} />
          ))}
        </tbody>
      </table>
    </FormSection>
  );
}

const Question = ({ question, index }) => {
  const {
    control,
    register,
    setValue,
    formState: { errors },
  } = useFormContext();

  const watchAnswer = useWatch({
    control,
    name: `declarations.${index}.answer`,
  });

  setValue(`declarations.${index}.question`, question);

  return (
    <tr key={index}>
      <td>
        <div className="grid grid-cols-[32px_1fr]">
          {index + 1}. <strong>{question}</strong>
        </div>

        <div className="grid grid-cols-[32px_1fr] space-y-4">
          <div />
          <div className="space-y-2">
            {question.includes("insurance") && (
              <span className="text-sm text-rose-500">
                Please provide insurer name and policy holder name if answer is
                &quot;Yes&quot;
              </span>
            )}

            <RadioGroup
              name={`declarations.${index}.answer`}
              rules={{ required: true }}
            >
              <label className="inline-flex w-fit cursor-pointer items-center gap-2">
                <RadioGroupItem value="No" /> No
              </label>

              <label className="inline-flex w-fit cursor-pointer items-center gap-2">
                <RadioGroupItem value="Yes" /> Yes
              </label>
            </RadioGroup>
            {watchAnswer === "Yes" && (
              <Input
                {...register(`declarations.${index}.details`, {
                  required: watchAnswer === "Yes" && "Details is required!",
                })}
                size="sm"
                placeholder="Please provide details on your answer"
                error={errors?.declarations?.[index]?.details}
              />
            )}
          </div>
        </div>
      </td>
    </tr>
  );
};
