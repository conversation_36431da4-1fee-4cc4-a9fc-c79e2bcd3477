"use client";

import Card from "@/ui/Card";
import { formatDateTime } from "@/formatters/date";
import Part1Submitted from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/submitted/Part1Submitted";
import Part2Submitted from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/submitted/Part2Submitted";
import Part3Submitted from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/submitted/Part3Submitted";
import Part4Submitted from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/submitted/Part4Submitted";
import Part5Submitted from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/submitted/Part5Submitted";

export default function FamilyPassConsentFormSubmitted({
  familyPassConsentForm,
}) {
  return (
    <div className="mx-auto max-w-[1000px] space-y-2 p-4">
      <Card className="space-y-4 border-transparent px-12">
        <h1 className="hstack justify-between text-2xl">Family Pass Form</h1>

        <div>
          You submitted this form on{" "}
          <strong>{formatDateTime(familyPassConsentForm.submittedAt)}</strong>
        </div>
      </Card>

      <Part1Submitted familyPassConsentForm={familyPassConsentForm} />
      <Part2Submitted familyPassConsentForm={familyPassConsentForm} />
      <Part3Submitted familyPassConsentForm={familyPassConsentForm} />
      <Part4Submitted familyPassConsentForm={familyPassConsentForm} />
      <Part5Submitted familyPassConsentForm={familyPassConsentForm} />
    </div>
  );
}
