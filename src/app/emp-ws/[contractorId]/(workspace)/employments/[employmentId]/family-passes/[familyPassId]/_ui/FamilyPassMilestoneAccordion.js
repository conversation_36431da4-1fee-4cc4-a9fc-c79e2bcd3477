"use client";

import Card from "@/ui/Card";
import HStack from "@/ui/HStack";
import { useState } from "react";
import { Path } from "@blueprintjs/icons";
import { Accordion } from "@/ui/Accordion";
import ExpandCollapseButton from "@/components/accordion-variants/ExpandCollapseButton";
import ApplicationSubmission from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/_ui/ApplicationSubmission";
import IpaApproval from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/_ui/IpaApproval";
import PassIssued from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/_ui/PassIssued";

export default function FamilyPassMilestoneAccordion({ familyPass }) {
  const allAccordionValues = ["1", "2", "3", "4"];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  return (
    <>
      <HStack className="justify-between text-lg font-bold">
        <HStack>
          <div className="text-blue-600">
            <Path size={16} />
          </div>
          Pass milestones
        </HStack>

        <ExpandCollapseButton
          onExpand={() => setAccordionValues(allAccordionValues)}
          onCollapse={() => setAccordionValues([])}
          iconSize={20}
          iconStrokeWidth={2}
          defaultToggle={false}
        />
      </HStack>

      <Card className="border-transparent">
        <Accordion
          type="multiple"
          value={accordionValues}
          onValueChange={(values) => setAccordionValues(values)}
        >
          <ApplicationSubmission familyPass={familyPass} />
          <IpaApproval familyPass={familyPass} />
          <PassIssued familyPass={familyPass} />
        </Accordion>
      </Card>
    </>
  );
}
