import DownloadFileWithIcon from "@/components/DownloadFileWithIcon";
import { formatDateTime } from "@/formatters/date";
import { Item } from "@/components/accordion-variants/left-arrow-basic";
import findEmploymentFile from "@/lib/employment-files/findEmploymentFile";
import { useParams } from "next/navigation";

export default function ApplicationSubmission({ familyPass }) {
  const { slug, familyPassConsentForm, passSubmittedAt } = familyPass;

  const { contractorId, employmentId } = useParams();
  const baseUrl = `/emp-ws/${contractorId}/employments/${employmentId}/family-passes/${slug}`;

  const familyPassConsentFormFile = findEmploymentFile({
    files: familyPass.employmentFiles,
    mainDescription: "Family Pass Related",
    description: "Family Pass Form",
  });

  console.log(familyPass);

  return (
    <Item title="Application Submission" value="1">
      <div className="space-y-4">
        {familyPassConsentForm && !familyPassConsentForm.submittedAt && (
          <a
            href={`${baseUrl}/family-pass-consent-form/${familyPassConsentForm.slug}`}
            className="text-link inline-block underline underline-offset-4"
            target="_blank"
          >
            Family Pass Form (Please complete this form first!)
          </a>
        )}

        {familyPassConsentForm && familyPassConsentForm.submittedAt && (
          <DownloadFileWithIcon
            file={familyPassConsentFormFile}
            description="Family Pass Form (submitted)"
          />
        )}

        <div>
          <span className="text-muted">
            Application submitted to the MOM on:
          </span>{" "}
          <strong className="font-semibold">
            {formatDateTime(passSubmittedAt) || (
              <span className="text-amber-600">Not yet submitted.</span>
            )}
          </strong>
        </div>
      </div>
    </Item>
  );
}
