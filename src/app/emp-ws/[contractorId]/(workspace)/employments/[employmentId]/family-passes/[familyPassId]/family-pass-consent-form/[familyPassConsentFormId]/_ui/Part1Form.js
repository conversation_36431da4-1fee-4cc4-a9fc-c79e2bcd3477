import { useFormContext, useWatch } from "react-hook-form";
import { FormSection } from "@/ui/Form";
import Input from "@/ui/Input";
import { RadioGroup, RadioGroupItem } from "@/ui/RadioGroup";
import VStack from "@/ui/VStack";
import Card from "@/ui/Card";

export default function Part1Form({ spouseRelationship, childRelationship }) {
  const {
    control,
    register,
    getValues,
    formState: { errors },
  } = useFormContext();
  const getQ1Question = getValues("particulars.0.question");
  const getQ2Question = getValues("particulars.1.question");
  const watchQ2Answer = useWatch({ control, name: "particulars.1.answer" });
  const hasFinNumber = useWatch({ control, name: "hasFinNumber" });

  const radioGroupItemLabelClassNames =
    "inline-flex w-fit cursor-pointer items-center gap-2";

  return (
    <FormSection>
      <table className="form-table">
        <tbody>
          <tr>
            <td className="space-y-4">
              <div className="grid grid-cols-[32px_1fr]">
                1. <strong>{getQ1Question}</strong>
              </div>

              <div className="grid grid-cols-[32px_1fr]">
                <div />
                <div className="space-y-2">
                  {spouseRelationship && (
                    <VStack className="gap-4">
                      <div className="gap-1">
                        {getValues("particulars.0.answer")} -{" "}
                        <span className="text-muted">
                          {getValues("particulars.0.subAnswer")}
                        </span>
                      </div>
                    </VStack>
                  )}

                  {childRelationship && (
                    <VStack>
                      {getValues("particulars.0.answer")}

                      <div className="grid grid-cols-[16px_1fr]">
                        <div />
                        <RadioGroup
                          name="particulars.0.subAnswer"
                          rules={{ required: true }}
                          orientation="vertical"
                        >
                          <label className={radioGroupItemLabelClassNames}>
                            <RadioGroupItem value="Biological child under 21 years" />
                            Biological child under 21 years
                          </label>
                          <label className={radioGroupItemLabelClassNames}>
                            <RadioGroupItem value="Legally adopted child under 21 years" />
                            Legally adopted child under 21 years
                          </label>
                        </RadioGroup>
                      </div>

                      <Card className="border-transparent bg-gray-50">
                        <FormSection className="bg-gray-50">
                          <h1 className="font-semibold">
                            Is the dependant born in Singapore
                          </h1>

                          <RadioGroup
                            name="localDependant"
                            rules={{ required: true }}
                          >
                            <label className={radioGroupItemLabelClassNames}>
                              <RadioGroupItem value="Yes" /> Yes
                            </label>

                            <label className={radioGroupItemLabelClassNames}>
                              <RadioGroupItem value="No" /> No
                            </label>
                          </RadioGroup>
                        </FormSection>
                      </Card>
                    </VStack>
                  )}
                </div>
              </div>
            </td>
          </tr>

          <tr>
            <td className="space-y-4">
              <div className="grid grid-cols-[32px_1fr]">
                2. <strong>{getQ2Question}</strong>
              </div>

              <div className="grid grid-cols-[32px_1fr]">
                <div />
                <div className="space-y-2">
                  <RadioGroup
                    name="particulars.1.answer"
                    rules={{ required: true }}
                    orientation="vertical"
                  >
                    <label className={radioGroupItemLabelClassNames}>
                      <RadioGroupItem value="Yes, dependant is currently working/studying/staying in Singapore" />
                      Yes, dependant is currently working/studying/staying in
                      Singapore
                    </label>
                    <label className={radioGroupItemLabelClassNames}>
                      <RadioGroupItem value="Yes, dependant has worked/studied/stayed in Singapore in the past" />{" "}
                      Yes, dependant has worked/studied/stayed in Singapore in
                      the past
                    </label>
                    <label className={radioGroupItemLabelClassNames}>
                      <RadioGroupItem value="No" />
                      No
                    </label>
                    <label className={radioGroupItemLabelClassNames}>
                      <RadioGroupItem
                        value="No, but dependant has a
                      Foreign Identification Number (FIN) issued by Singapore"
                      />
                      No, but dependant has a Foreign Identification Number
                      (FIN) issued by Singapore
                    </label>
                  </RadioGroup>

                  {watchQ2Answer && watchQ2Answer !== "No" && (
                    <Card className="border-transparent bg-gray-50">
                      <VStack className="gap-4">
                        <h2 className="font-semibold">
                          What is your depandant&apos;s FIN?
                        </h2>

                        <RadioGroup
                          name="hasFinNumber"
                          rules={{ required: true }}
                          orientation="vertical"
                        >
                          <VStack className="gap-4">
                            <label className={radioGroupItemLabelClassNames}>
                              <RadioGroupItem value="true" />
                              FIN number
                            </label>

                            {hasFinNumber === "true" && (
                              <Input
                                size="sm"
                                label="FIN number"
                                labelClassNameProp="!bg-gray-50"
                                className="bg-white"
                                {...register("finNumber", {
                                  required:
                                    hasFinNumber && "FIN number is required!",
                                })}
                                error={errors?.finNumber}
                              />
                            )}
                          </VStack>

                          <label className={radioGroupItemLabelClassNames}>
                            <RadioGroupItem value="false" />
                            Dependant can&apos;t remember
                          </label>
                        </RadioGroup>
                      </VStack>
                    </Card>
                  )}
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </FormSection>
  );
}
