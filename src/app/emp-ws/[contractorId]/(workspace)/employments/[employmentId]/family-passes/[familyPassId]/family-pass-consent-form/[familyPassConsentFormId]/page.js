import apiQuery from "@/lib/apiQuery";
import FamilyPassConsentForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/FamilyPassConsentForm";
import FamilyPassConsentFormSubmitted from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/submitted/FamilyPassConsentFormSubmitted";
import { notFound } from "next/navigation";

const QUERY = `
  query empFamilyPassConsentForm($id: ID!) {
    empFamilyPassConsentForm(id: $id) {
      id
      isDp
      isLtvp
      name
      nationality
      finNumber
      gender
      race
      customRace
      religion
      customReligion
      dob
      email
      localAddress
      mobileNumber
      homeNumber

      travelDocumentType
      travelDocumentNumber
      travelDocumentIssueDate
      travelDocumentExpiryDate
      travelDocumentCountryOfBirth
      travelDocumentCountryOfOrigin
      travelDocumentStateProvinceOfBirth
      travelDocumentStateProvinceOfOrigin
      travelDocumentMalaysianIcNumber

      highestQualification
      hpbVaccinationReferenceNumber

      maritalStatus
      localDependant

      particulars {
        question
        answer
        finNumber
      }

      declarations {
        question
        answer
        details
      }

      passport
      marriageCertificate
      birthCertificate
      educationCertificate
      hpbVaccination
      currentPass

      familyPass {
        name
        relationship
      }

      contractor {
        name
      }

      submittedAt
    }
  }
`;

export default async function Page({ params }) {
  const { familyPassConsentFormId } = await params;
  const res = await apiQuery(QUERY, { id: familyPassConsentFormId });
  const familyPassConsentForm = res.data.empFamilyPassConsentForm;

  if (familyPassConsentForm === null) notFound();

  const submittedAt = familyPassConsentForm.submittedAt;

  if (submittedAt) {
    return (
      <FamilyPassConsentFormSubmitted
        familyPassConsentForm={familyPassConsentForm}
      />
    );
  }

  return (
    <FamilyPassConsentForm familyPassConsentForm={familyPassConsentForm} />
  );
}
