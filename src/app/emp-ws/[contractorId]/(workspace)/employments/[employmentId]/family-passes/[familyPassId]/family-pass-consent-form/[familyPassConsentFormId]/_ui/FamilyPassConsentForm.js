"use client";

import Button from "@/ui/Button";
import Card from "@/ui/Card";
import ExpandCollapseButton from "@/components/accordion-variants/ExpandCollapseButton";
import useAccordionValues from "@/components/accordion-variants/useAccordionValues";
import { Form, showFormErrors } from "@/ui/Form";
import { useForm } from "react-hook-form";
import { Accordion } from "@/ui/Accordion";
import { useMemo, useState } from "react";
import { Item } from "@/components/accordion-variants/declaration";
import Part1Form from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/Part1Form";
import Part2Form from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/Part2Form";
import Part3Form from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/Part3Form";
import Part4Form from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/Part4Form";
import Part5Form from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/Part5Form";
import Part6Form from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/Part6Form";
import Part7Form from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/Part7Form";
import {
  TEST_CHILD_FORM_DATA,
  TEST_FORM_DATA,
} from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/[familyPassId]/family-pass-consent-form/[familyPassConsentFormId]/_ui/test_form_data";
import VStack from "@/ui/VStack";
import { useWatch } from "react-hook-form";
import { useParams, useRouter } from "next/navigation";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { toast } from "sonner";
import { isEmpty } from "lodash";

const MUTATION = `
  mutation empSubmitFamilyPassConsentForm($familyPassConsentFormId: ID!, $input: FamilyPassConsentFormInput!) {
    execute:empSubmitFamilyPassConsentForm(familyPassConsentFormId: $familyPassConsentFormId, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on FamilyPassConsentForm {
          id
          slug
        }
      }
    }
  }
`;

export default function FamilyPassConsentForm({ familyPassConsentForm }) {
  const { familyPassConsentFormId } = useParams();
  const router = useRouter();

  const { familyPass } = familyPassConsentForm;
  const { relationship } = familyPass;
  const spouseRelationship = ["Wife", "Husband"].includes(relationship);
  const childRelationship = relationship === "Child";

  const allAccordionValues = useMemo(
    () => ["1", "2", "3", "4", "5", "6", "7"],
    [],
  );

  const defaultValues = () => {
    if (spouseRelationship) {
      return {
        particulars: [
          {
            question: "How is the dependant related to you?",
            answer: "Spouse",
            subAnswer: "Legally married spouse",
          },
          {
            question:
              "Has your dependant ever studied, worked or stayed long-term (not as a tourist) in Singapore before?",
            answer: "",
          },
        ],
        maritalStatus: "Married",
      };
    }

    if (childRelationship) {
      return {
        particulars: [
          {
            question: "How is the dependant related to you?",
            answer: "Unmarried Child",
            subAnswer: "",
          },
          {
            question:
              "Has your dependant ever studied, worked or stayed long-term (not as a tourist) in Singapore before?",
            answer: "",
          },
        ],
      };
    }
  };

  const [formData, setFormData] = useState(null);
  const form = useForm({
    // defaultValues: TEST_FORM_DATA,
    defaultValues: TEST_CHILD_FORM_DATA,
    // defaultValues: defaultValues(),
  });
  const {
    control,
    setError,
    formState: { errors },
  } = form;
  const [accordionValues, setAccordionValues] = useAccordionValues(
    allAccordionValues,
    Object.keys(errors).length > 0,
  );

  const watchAck1 = useWatch({ control, name: "ack1" });
  const watchAck2 = useWatch({ control, name: "ack2" });
  const watchAck3 = useWatch({ control, name: "ack3" });

  const hasErrors = Object.keys(errors).length > 0;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Form submitted successfully!");
      router.refresh();
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    const {
      ack1,
      ack2,
      ack3,
      travelDocumentMalaysianIcNumber,
      localDependant,
      hasFinNumber,
      finNumber,
      ...restOfInput
    } = input;

    const newInput = {
      localDependant: localDependant === "Yes",
      finNumber: hasFinNumber === "true" ? finNumber : "",
      travelDocumentMalaysianIcNumber:
        input.nationality === "Malaysian"
          ? travelDocumentMalaysianIcNumber
          : "",
      ...restOfInput,
    };

    setFormData(newInput);
    console.log(newInput);

    execute({
      familyPassConsentFormId,
      input: newInput,
    });
  };

  return (
    <div className="mx-auto max-w-[1000px] space-y-4 p-4">
      <Card className="space-y-4 border-transparent px-12">
        <h1 className="hstack justify-between text-2xl">
          Family Pass Form
          <ExpandCollapseButton
            onExpand={() => setAccordionValues(allAccordionValues)}
            onCollapse={() => setAccordionValues([])}
          />
        </h1>
      </Card>

      <Form
        form={form}
        onSubmit={onSubmit}
        formData={formData}
        className="space-y-4"
      >
        <Accordion
          type="multiple"
          value={accordionValues}
          onValueChange={(values) => setAccordionValues(values)}
        >
          <Item title="Part 1: Dependant Particulars" value="1">
            <Part1Form
              spouseRelationship={spouseRelationship}
              childRelationship={childRelationship}
            />
          </Item>
          <Item title="Part 2: Dependant Personal Information" value="2">
            <Part2Form spouseRelationship={spouseRelationship} />
          </Item>
          <Item
            title="Part 3: Dependant Highest Education Qualification"
            value="3"
          >
            <Part3Form />
          </Item>
          <Item
            title={
              <VStack className="gap-1">
                <div>Part 4: Dependant Declaration</div>
                <p className="text-sm font-normal opacity-90">
                  If your dependant is below 16 years old, you must declare on
                  his/her behalf.
                </p>
              </VStack>
            }
            value="4"
          >
            <Part4Form />
          </Item>
          <Item title="Part 5: Documents Required" value="5">
            <Part5Form
              spouseRelationship={spouseRelationship}
              childRelationship={childRelationship}
              familyPassConsentForm={familyPassConsentForm}
            />
          </Item>
          <Item
            title={
              <VStack className="gap-1">
                <div>Part 6: Dependant Acknowledgement</div>
                <p className="text-sm font-normal opacity-90">
                  Acknowledgment by Foreigner / Applicant
                </p>
              </VStack>
            }
            value="6"
          >
            <Part6Form familyPassConsentForm={familyPassConsentForm} />
          </Item>

          <Item
            title={
              <VStack className="gap-1">
                <div>Part 7: Candidate Acknowledgement</div>
                <p className="text-sm font-normal opacity-90">
                  Acknowledgment by Main Pass Candidate / Holder in support of
                  application for a Dependant’s Pass
                </p>
              </VStack>
            }
            value="7"
          >
            <Part7Form familyPassConsentForm={familyPassConsentForm} />
          </Item>
        </Accordion>

        <VStack className="items-center">
          {/* <Checkbox
            {...register("ack1")}
            suffix={
              <div>
                {" "}
                I,{" "}
                <span className="font-semibold">
                  {watchName || (
                    <span className="text-red-600">
                      [dependant&apos;s name]
                    </span>
                  )}{" "}
                </span>{" "}
                agree and accept Jobline Resources&apos;s terms and conditions
              </div>
            }
          />
          <Checkbox
            {...register("ack2")}
            suffix={
              <div>
                I,{" "}
                <span className="font-semibold">
                  {familyPassConsentForm.contractor.name}
                </span>{" "}
                agree and accept Jobline Resources&apos;s terms and conditions
              </div>
            }
          /> */}

          <Button
            type="submit"
            variant={hasErrors ? "danger" : "success"}
            disabled={!(watchAck1 && watchAck2 && watchAck3)}
            loading={pending}
          >
            Submit work pass application
          </Button>
        </VStack>
      </Form>
    </div>
  );
}
