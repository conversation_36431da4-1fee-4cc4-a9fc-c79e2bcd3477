import Card from "@/ui/Card";

export default function Part5Submitted({ familyPassConsentForm }) {
  const {
    passport,
    marriageCertificate,
    commonLawMarriageCertificate,
    dependantBirthCertificate,
    educationCertificate,
    childCustodyCourtOrderCopy,
    otherBiologicalParentLetter,
    childMedicalReport,
    candidateBirthCertificate,
    hpbVaccination,
    currentPass,
  } = familyPassConsentForm;

  const documentsRequired = [
    { columnName: "Passport", file: passport },
    { columnName: "Marriage certificate", file: marriageCertificate },
    { columnName: "Marriage certificate", file: commonLawMarriageCertificate },
    { columnName: "Birth certificate", file: dependantBirthCertificate },
    {
      columnName: "Birth certificate (work pass holder)",
      file: candidateBirthCertificate,
    },
    { columnName: "Education certificate", file: educationCertificate },
    {
      columnName: "Court order on the child&apos;s custody",
      file: childCustodyCourtOrderCopy,
    },
    {
      columnName: "Other biological parent consent letter",
      file: otherBiologicalParentLetter,
    },
    { columnName: "Child medical report", file: childMedicalReport },
    {
      columnName: "Verrification of vaccination Requirements",
      file: hpbVaccination,
    },
    { columnName: "Current pass", file: currentPass },
  ];

  return (
    <>
      <h2 className="border-b py-2 text-lg leading-none font-semibold text-amber-800">
        Part 5: Documents Required
      </h2>
      <Card variant="light">
        <table className="onboarding-table">
          <thead>
            <tr>
              <th>Documents</th>
              <th>Upload</th>
            </tr>
          </thead>

          <tbody>
            {documentsRequired.map(({ columnName, file }) => {
              if (!file) {
                return null;
              }

              return (
                <tr key={columnName}>
                  <td>
                    <strong>{columnName}</strong>
                  </td>
                  <td>
                    <DisplayFileName file={file} />
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </Card>
    </>
  );
}

const DisplayFileName = ({ file }) => {
  if (!file) return null;

  return (
    <div title={file.fileName} className="w-[500px] truncate overflow-hidden">
      {file.fileName}
    </div>
  );
};
