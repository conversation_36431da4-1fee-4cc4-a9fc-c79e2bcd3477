import Card from "@/ui/Card";

export default function Part5Submitted({ familyPassConsentForm }) {
  const {
    passport,
    marriageCertificate,
    birthCertificate,
    educationCertificate,
    hpbVaccination,
    currentPass,
  } = familyPassConsentForm;

  const documentsRequired = [
    { columnName: "Passport", file: passport },
    { columnName: "Marriage Certificate", file: marriageCertificate },
    { columnName: "Birth Certificate", file: birthCertificate },
    { columnName: "Education Certificate", file: educationCertificate },
    {
      columnName: "Verrification of Vaccination Requirements",
      file: hpbVaccination,
    },
    { columnName: "Current Pass", file: currentPass },
  ];

  return (
    <>
      <h2 className="border-b py-2 text-lg leading-none font-semibold text-amber-800">
        Part 5: Documents Required
      </h2>
      <Card variant="light">
        <table className="onboarding-table">
          <thead>
            <tr>
              <th>Documents</th>
              <th>Upload</th>
            </tr>
          </thead>

          <tbody>
            {documentsRequired.map(({ columnName, file }) => {
              if (!file) {
                return null;
              }

              return (
                <tr key={columnName}>
                  <td>
                    <strong>{columnName}</strong>
                  </td>
                  <td>
                    <DisplayFileName file={file} />
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </Card>
    </>
  );
}

const DisplayFileName = ({ file }) => {
  if (!file) return null;

  return (
    <div title={file.fileName} className="w-[500px] truncate overflow-hidden">
      {file.fileName}
    </div>
  );
};
