import Card from "@/ui/Card";
import Checkbox from "@/ui/Checkbox";
import VStack from "@/ui/VStack";
import { useFormContext, useWatch } from "react-hook-form";

export default function Part6Form({ familyPassConsentForm }) {
  const { isDp, isLtvp } = familyPassConsentForm;

  const { control, register } = useFormContext();

  const watchName = useWatch({ control, name: "name" });

  return (
    <div className="space-y-6 px-8">
      <h2 className="text-sm font-semibold">
        Please carefully go through the following terms and conditions before
        acknowledging this declaration.
      </h2>
      <div className="space-y-2 text-sm">
        <VStack className="gap-1">
          <Checkbox
            {...register("ack1")}
            suffix={
              <h2>
                I,{" "}
                <span className="font-semibold">
                  {watchName ? (
                    `${watchName}'s`
                  ) : (
                    <span className="text-red-600">
                      [dependant&apos;s name]
                    </span>
                  )}{" "}
                  (applicant* of this application)
                </span>{" "}
                wish to confirm that:
              </h2>
            }
          />

          <p className="text-xs text-red-600">
            *To be acknowledged by the parent (main pass applicant) if the
            dependent is below 16 years old.
          </p>
        </VStack>

        <Card className="border-transparent bg-gray-50 pl-0">
          {isDp && <DependantPassAcknowledgement />}
          {isLtvp && <LongTermVisitPassAcknowledgement />}
        </Card>
      </div>
    </div>
  );
}

const DependantPassAcknowledgement = () => (
  <ul className="ml-8 list-disc space-y-2">
    <li>
      I confirm that the information as set out in Part 1 to 5 were provided by
      me and that the said information is true and correct.
    </li>
    <li>
      I undertake not to misuse controlled drugs or to take part in any
      political or other activities during my stay in Singapore which would make
      me an undesirable or prohibited immigrant under the Immigration Act.
    </li>
    <li>
      I declare that I have not suffered and am not suffering from Acquired
      Immunodeficiency Syndrome (AIDS) or infected with Human Immunodeficiency
      Virus (HIV) or Tuberculosis. I acknowledge that during the period of
      validity of my <strong>Dependant&apos;s Pass</strong>, if I am found to be
      suffering from AIDS or infected with HIV or Tuberculosis, the{" "}
      <strong>Dependant&apos;s Pass</strong> issued to me will be cancelled and
      I will have to leave Singapore by the date specified by the Controller of
      Immigration.
    </li>
    <li>
      I understand that I may be prosecuted if I have provided any information,
      which is false in any material particular or is misleading by reason of
      the omission of any material particular.
    </li>
    <li>
      I consent for the Government of Singapore and its statutory authorities to
      display my information on the Ministry of Manpower&apos;s work pass
      systems, and to disclose such information to any relevant person or
      organisation for the administration of matters relating to work pass and
      passes for dependants.
    </li>
    <li>
      I consent to the Ministry of Manpower displaying my pass details when my
      card is scanned using the Ministry of Manpower&apos;s work pass mobile
      application.
    </li>
    <li>
      With reference to my application for{" "}
      <strong>Dependant&apos;s Pass</strong> and residence in Singapore, I give
      my consent to the Government of Singapore to obtain from and verify
      information with any person, organisation or any other source for
      assessing my application.
    </li>
    <li>
      I understand that a Singpass account will help me to access Government
      e-services in Singapore and I give my consent to the Ministry of Manpower
      to share my personal details with the Singpass issuing agency. This allows
      me to apply for a Singpass account at a later time if I am eligible for a
      Singpass.
    </li>
  </ul>
);

const LongTermVisitPassAcknowledgement = () => (
  <ul className="ml-8 list-disc space-y-2">
    <li>
      I confirm that the information as set out in Part 1 to 5 were provided by
      me and that the said information is true and correct.
    </li>
    <li>
      I undertake not to misuse controlled drugs or to take part in any
      political or other activities during my stay in Singapore which would make
      me an undesirable or prohibited immigrant under the Immigration Act.
    </li>
    <li>
      I declare that I have not suffered and am not suffering from Acquired
      Immunodeficiency Syndrome (AIDS) or infected with Human Immunodeficiency
      Virus (HIV) or Tuberculosis. I acknowledge that during the period of
      validity of my <strong>Long-Term Visit Pass</strong>, if I am found to be
      suffering from AIDS or infected with HIV or Tuberculosis, the{" "}
      <strong>Long-Term Visit Pass</strong> issued to me will be cancelled and I
      will have to leave Singapore by the date required.
    </li>
    <li>
      I undertake to comply with the provisions of the Immigration Act and any
      regulations made there under or any statutory modification or re-enactment
      thereof for the time being in force in Singapore.
    </li>
    <li>I undertake not to contravene any of the laws of Singapore.</li>
    <li>
      I undertake not to indulge in any activities which are inconsistent with
      the purpose for which the immigration pass has been issued.
    </li>
    <li>
      I further undertake not to be engaged in any form of employment, business
      or occupation whilst in Singapore without a work pass issued by the
      Controller of Work Passes.
    </li>
    <li>
      I am aware that overstaying or working illegally in Singapore is a serious
      offence and on conviction, the penalties may include mandatory
      imprisonment and caning.
    </li>
    <li>
      I understand that if the Controller of Immigration is satisfied that I or
      any member of my family breaches this undertaking or becomes an
      undesirable or prohibited immigrant, the Controller of Immigration will
      cancel my immigration pass and the passes of the members of my family, and
      we may be required to leave Singapore within 24 hours of such
      cancellation.
    </li>
    <li>
      I understand that this application for and possession of a visa does not
      guarantee entry into Singapore and permission to entry is entirely
      discretionary at the point of entry.
    </li>
    <li>
      I consent for the Government of Singapore and its statutory authorities to
      display my information on the Ministry of Manpower&apos;s work pass
      systems, and to disclose such information to any relevant person or
      organisation for the administration of matters relating to work pass and
      passes for dependants.
    </li>
    <li>
      I consent to the Ministry of Manpower displaying my pass details when my
      card is scanned using the Ministry of Manpower&apos;s work pass mobile
      application.
    </li>
    <li>
      I give my consent for your department to obtain and verify information
      from or with any source as you deem appropriate for the assessment of my
      application for immigration facilities.
    </li>
    <li>
      With reference to my application for <strong>Long-Term Visit Pass</strong>{" "}
      and residence in Singapore, I give my consent to the Government of
      Singapore to obtain from and verify information with any person,
      organisation or any other source for assessing my application.
    </li>
  </ul>
);
