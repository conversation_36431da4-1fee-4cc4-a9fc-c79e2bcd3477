import Card from "@/ui/Card";
import Checkbox from "@/ui/Checkbox";
import { useFormContext } from "react-hook-form";

export default function Part7Form({ familyPassConsentForm }) {
  const { isDp, isLtvp } = familyPassConsentForm;
  const contractorName = familyPassConsentForm.contractor.name;
  const { register } = useFormContext();

  return (
    <div className="space-y-6 px-8">
      <h2 className="text-sm font-semibold">
        Please carefully go through the following terms and conditions before
        acknowledging this declaration.
      </h2>

      <div className="space-y-2 text-sm">
        <Checkbox
          {...register("ack2")}
          suffix={
            <h2>
              I,{" "}
              <strong>
                {contractorName} (main applicant / holder in support of this
                application)
              </strong>{" "}
              wish to confirm that:
            </h2>
          }
        />

        <Card className="border-transparent bg-gray-50 pl-0">
          {isDp && <DependantPassAcknowledgement />}
          {isLtvp && <LongTermVisitPassAcknowledgement />}
        </Card>
      </div>

      <div className="space-y-2 text-sm">
        <h1 className="font-semibold">Terms & Conditions:</h1>

        <Checkbox
          {...register("ack3")}
          suffix={
            <h2>
              I,{" "}
              <strong>
                {contractorName} (main applicant / holder in support of this
                application)
              </strong>{" "}
              agree to undertake the followings:
            </h2>
          }
        />

        {isDp && <DependantPassTermsAndConditions />}
        {isLtvp && <LongTermVisitPassTermsAndConditions />}
      </div>
    </div>
  );
}

const DependantPassAcknowledgement = () => (
  <ul className="ml-8 list-disc space-y-2">
    <li>
      I hereby sponsor this application for a{" "}
      <strong>Dependant&apos;s Pass</strong>.
    </li>
    <li>
      I hereby certify that the application of the above foreigner for a{" "}
      <strong>Dependant&apos;s Pass</strong>(es) is/are to accompany me in
      Singapore and that the statements made by the foreigner in this
      application are true. I also undertake to ensure the compliance by the
      foreigner of any quarantine and medical surveillance imposed on the
      foreigner under regulation 8 (2A) of the Immigration Regulation.
    </li>
    <li>
      I undertake to bear responsibility for the foreigner&apos;s upkeep and
      maintenance in Singapore.
    </li>
    <li>
      I declare that should this application be approved, I will make an
      application to the Ministry of Manpower to allow the foreigner to enter
      Singapore subject to prevailing entry requirements at the point of entry
      into Singapore.
    </li>
    <li>
      I undertake to indemnify the Government of Singapore, Company and
      Company&apos;s customer (to the company which I will be deployed and
      working with) for any charges or expenses which may be incurred by the
      Government in respect of the repatriation of the said foreigner.
    </li>
    <li>
      I shall keep copies of the documents/ certificates showing the
      relationship between me and the foreigner throughout my employment. I
      understand the Ministry of Manpower can at any time request for these
      documents for verification and revoke the pass should the documents be
      inconsistent with the declaration furnished in the application form or if
      I am unable to produce the documents.
    </li>
    <li>
      I consent to the Ministry of Manpower displaying pass details when the
      pass holder&apos;s card is scanned using the Ministry of Manpower&apos;s
      work pass mobile application.
    </li>
    <li>
      I declare that should this application be approved, I will make an
      application to Ministry of Manpower to allow the foreigner to enter
      Singapore subject to prevailing entry requirements at the point of entry
      into Singapore.
    </li>
    <li>
      With reference to this application submitted for{" "}
      <strong>Dependant&apos;s Pass</strong> and residence in Singapore, I give
      my consent to the Government of Singapore, company and company&apos;s
      customer (to the company which I will be deployed and working with) to
      obtain from and verify information with any person, organisation or any
      other source for assessing the application.
    </li>
    <li>
      By providing the information set out above, I agree and consent, to the
      company, its customers, as well as their authorized service providers,
      collecting, using, disclosing and sharing amongst themselves my personal
      data, and disclosing such personal data to the company&apos;s authorized
      service providers and relevant third parties for purposes reasonably
      required by the company to evaluate my employment, conduct or other
      details relevant to this application.
    </li>
    <li>
      I shall not hold the company responsible for any liability, demand, claim,
      suit, proceeding, costs and expenses of any nature arising from the
      release of such information as aforesaid.
    </li>
  </ul>
);

const DependantPassTermsAndConditions = () => (
  <ul className="ml-8 list-disc space-y-2">
    <li>
      I will bear all of my DP-related fees such as MOM Application Fee,
      Issuance Fee & Multiple Journey Visa, Medical Fees and Jobline Processing
      Fee, etc and hereby authorise the Company to deduct the DP-related fees
      from my salary.
    </li>
    <li>
      I will bear full responsibility for my DP Applicant&apos;s upkeep &
      maintenance in Singapore.
    </li>
    <li>
      I will bear all cost of repatriation of my DP Applicant upon the
      completion and/or termination of my service.
    </li>
    <li>
      I will undertake the responsibility to ensure that all of my DP Applicant
      are well-insured with a personal medical and/or Healthshield insurance
      policy purchased in Singapore during my employment term.
    </li>
    <li>
      I agree that I will purchase the personal medical and/or Healthshield
      insurance policy for my DP Applicant and furnish a valid copy of the
      product summary / policy details to the company as documentation.
    </li>
  </ul>
);

const LongTermVisitPassAcknowledgement = () => (
  <ul className="ml-8 list-disc space-y-2">
    <li>
      I hereby sponsor this application for a{" "}
      <strong>Long-Term Visit Pass</strong>.
    </li>
    <li>
      I declare that all the information as set out in Part 1 to 4 is, to the
      best of my knowledge, true and correct. I understand that I may be
      prosecuted if I have provided any information, which is false in any
      material particular, or misleading by reason of the omission of any
      material particular.
    </li>
    <li>
      I undertake to be responsible for ensuring the compliance by the foreigner
      of any quarantine and medical surveillance imposed on the foreigner under
      regulations 8 (2A) of the Immigration Regulations.
    </li>
    <li>
      I undertake to bear responsibility for the foreigner&apos;s upkeep and
      maintenance in Singapore.
    </li>
    <li>
      I declare that should this application be approved, I will make an
      application to the Ministry of Manpower to allow the foreigner to enter
      Singapore subject to prevailing entry requirements at the point of entry
      into Singapore.
    </li>
    <li>
      I undertake to indemnify the Government of Singapore, Company and
      Company&apos;s customer (to the company which I will be deployed and
      working with) for any charges or expenses which may be incurred by the
      Government in respect of the repatriation of the said foreigner.
    </li>
    <li>
      I shall keep copies of the documents/ certificates showing the
      relationship between me and the foreigner throughout my employment. I
      understand the Ministry of Manpower can at any time request for these
      documents for verification and revoke the pass should the documents be
      inconsistent with the declaration furnished in the application form or if
      I am unable to produce the documents.
    </li>
    <li>
      I consent to the Ministry of Manpower displaying pass details when the
      pass holder&apos;s card is scanned using the Ministry of Manpower&apos;s
      work pass mobile application.
    </li>
    <li>
      I declare that should this application be approved, I will make an
      application to Ministry of Manpower to allow the foreigner to enter
      Singapore subject to prevailing entry requirements at the point of entry
      into Singapore.
    </li>
    <li>
      With reference to this application submitted for{" "}
      <strong>Long-Term Visit Pass</strong> and residence in Singapore, I give
      my consent to the Government of Singapore, Company and Company&apos;s
      customer (to the company which I will be deployed and working with) to
      obtain from and verify information with any person, organisation or any
      other source for assessing the application.
    </li>
    <li>
      By providing the information set out above, I agree and consent, to the
      company, its customers, as well as their authorized service providers,
      collecting, using, disclosing and sharing amongst themselves my personal
      data, and disclosing such personal data to the company&apos;s authorized
      service providers and relevant third parties for purposes reasonably
      required by the company to evaluate my employment, conduct or other
      details relevant to this application.
    </li>
    <li>
      I shall not hold the company responsible for any liability, demand, claim,
      suit, proceeding, costs and expenses of any nature arising from the
      release of such information as aforesaid.
    </li>
  </ul>
);

const LongTermVisitPassTermsAndConditions = () => (
  <ul className="ml-8 list-disc space-y-2">
    <li>
      I will bear all of my <strong>LTVP-related</strong> fees such as MOM
      Application Fee, Issuance Fee & Multiple Journey Visa, Medical fees and
      Jobline Processing Fee, etc and hereby authorise the Company to deduct the{" "}
      <strong>LTVP-related</strong> fees from my salary.
    </li>
    <li>
      I will bear full responsibility for my <strong>LTVP</strong>{" "}
      Applicant&apos;s upkeep & maintenance in Singapore.
    </li>
    <li>
      I will bear all cost of repatriation of my <strong>LTVP</strong> Applicant
      upon the completion and/or termination of my service.
    </li>
    <li>
      I will undertake the responsibility to ensure that all of my{" "}
      <strong>LTVP</strong>
      Applicants are well-insured with a personal medical and/or Healthshield
      insurance policy purchased in Singapore during my employment term.
    </li>
    <li>
      I agree that I will purchase the personal medical and/or Healthshield
      insurance policy for my <strong>LTVP</strong> Applicant and furnish a
      valid copy of the Product Summary / policy details to the company as
      documentation.
    </li>
  </ul>
);
