import Card from "@/ui/Card";
import Checkbox from "@/ui/Checkbox";
import { useFormContext } from "react-hook-form";

export default function Part7Form({ familyPassConsentForm }) {
  return (
    <div className="space-y-6 px-8">
      <h2 className="text-sm font-semibold">
        Please carefully go through the following terms and conditions before
        acknowledging this declaration.
      </h2>

      <ContractorAcknowledgement
        contractorName={familyPassConsentForm.contractor.name}
      />

      <TermsAndConditions
        contractorName={familyPassConsentForm.contractor.name}
      />
    </div>
  );
}

const ContractorAcknowledgement = ({ contractorName }) => {
  const { register } = useFormContext();

  return (
    <div className="space-y-2 text-sm">
      <Checkbox
        {...register("ack2")}
        suffix={
          <h2>
            I,{" "}
            <strong>
              {contractorName} (main applicant / holder in support of this
              application)
            </strong>{" "}
            wish to confirm that:
          </h2>
          // <div>
          //   I,{" "}
          //   <span className="font-semibold">
          //     {familyPassConsentForm.contractor.name}
          //   </span>{" "}
          //   agree and accept Jobline Resources&apos;s terms and conditions
          // </div>
        }
      />

      <Card className="border-transparent bg-gray-50 pl-0">
        <ul className="ml-8 list-disc space-y-2">
          <li>
            I hereby sponsor this application for a{" "}
            <strong>Dependant&apos;s Pass</strong>.
          </li>
          <li>
            I hereby certify that the application of the above foreigner for a{" "}
            <strong>Dependant&apos;s Pass</strong>(es) is/are to accompany me in
            Singapore and that the statements made by the foreigner in this
            application are true. I also undertake to ensure the compliance by
            the foreigner of any quarantine and medical surveillance imposed on
            the foreigner under regulation 8 (2A) of the Immigration Regulation.
          </li>
          <li>
            I undertake to bear responsibility for the foreigner&apos;s upkeep
            and maintenance in Singapore.
          </li>
          <li>
            I declare that should this application be approved, I will make an
            application to the Ministry of Manpower to allow the foreigner to
            enter Singapore subject to prevailing entry requirements at the
            point of entry into Singapore.
          </li>
          <li>
            I undertake to indemnify the Government of Singapore, Company and
            Company&apos;s customer (to the company which I will be deployed and
            working with) for any charges or expenses which may be incurred by
            the Government in respect of the repatriation of the said foreigner.
          </li>
          <li>
            I shall keep copies of the documents/ certificates showing the
            relationship between me and the foreigner throughout my employment.
            I understand the Ministry of Manpower can at any time request for
            these documents for verification and revoke the pass should the
            documents be inconsistent with the declaration furnished in the
            application form or if I am unable to produce the documents.
          </li>
          <li>
            I consent to the Ministry of Manpower displaying pass details when
            the pass holder&apos;s card is scanned using the Ministry of
            Manpower&apos;s work pass mobile application.
          </li>
          <li>
            I declare that should this application be approved, I will make an
            application to Ministry of Manpower to allow the foreigner to enter
            Singapore subject to prevailing entry requirements at the point of
            entry into Singapore.
          </li>
          <li>
            With reference to this application submitted for{" "}
            <strong>Dependant&apos;s Pass</strong> and residence in Singapore, I
            give my consent to the Government of Singapore, company and
            company&apos;s customer (to the company which I will be deployed and
            working with) to obtain from and verify information with any person,
            organisation or any other source for assessing the application.
          </li>
          <li>
            By providing the information set out above, I agree and consent, to
            the company, its customers, as well as their authorized service
            providers, collecting, using, disclosing and sharing amongst
            themselves my personal data, and disclosing such personal data to
            the company&apos;s authorized service providers and relevant third
            parties for purposes reasonably required by the company to evaluate
            my employment, conduct or other details relevant to this
            application.
          </li>
          <li>
            I shall not hold the company responsible for any liability, demand,
            claim, suit, proceeding, costs and expenses of any nature arising
            from the release of such information as aforesaid.
          </li>
        </ul>
      </Card>
    </div>
  );
};

const TermsAndConditions = ({ contractorName }) => {
  const { register } = useFormContext();

  return (
    <div className="space-y-2 text-sm">
      <h1 className="font-semibold">Terms & Conditions:</h1>

      <Checkbox
        {...register("ack3")}
        suffix={
          <h2>
            I,{" "}
            <strong>
              {contractorName} (main applicant / holder in support of this
              application)
            </strong>{" "}
            agree to undertake the followings:
          </h2>
        }
      />

      <ul className="ml-8 list-disc space-y-2">
        <li>
          I will bear all of my DP-related fees such as MOM Application Fee,
          Issuance Fee & Multiple Journey Visa, Medical Fees and Jobline
          Processing Fee, etc and hereby authorise the Company to deduct the
          DP-related fees from my salary.
        </li>
        <li>
          I will bear full responsibility for my DP Applicant&apos;s upkeep &
          maintenance in Singapore.
        </li>
        <li>
          I will bear all cost of repatriation of my DP Applicant upon the
          completion and/or termination of my service.
        </li>
        <li>
          I will undertake the responsibility to ensure that all of my DP
          Applicant are well-insured with a personal medical and/or Healthshield
          insurance policy purchased in Singapore during my employment term.
        </li>
        <li>
          I agree that I will purchase the personal medical and/or Healthshield
          insurance policy for my DP Applicant and furnish a valid copy of the
          product summary / policy details to the company as documentation.
        </li>
      </ul>
    </div>
  );
};
