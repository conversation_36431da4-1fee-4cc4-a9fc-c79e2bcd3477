import { useFormContext } from "react-hook-form";
import { FormBody, FormRow, FormSection } from "@/ui/Form";
import QualificationSelect from "@/components/select/QualificationSelect";

export default function Part3Form() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <FormBody className="space-y-8">
      <FormSection title="A. Education Details">
        <FormRow colsCount={2}>
          <QualificationSelect
            {...register("highestQualification", {
              required: "Qualification is required!",
            })}
            error={errors.highestQualification}
          />
        </FormRow>
      </FormSection>
    </FormBody>
  );
}
