import apiQuery from "@/lib/apiQuery";
import FamilyPassTable from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/family-passes/FamilyPassTable";

const QUERY = `
  query empFamilyPasses($employmentId: ID!, $sorts: [SortInput!], $page: Int!) {
    empFamilyPasses(employmentId: $employmentId, sorts: $sorts, page: $page) {
      nodes {
        id
        fmId
        slug
        name
        relationship
        employmentId
        contractor {
          slug
        }
        employment {
          slug
        }
        workPassId
        applicationType
        passType
        status
        passStatus
        createdAt
      }

      totalCount
      totalPages
      processingTimeMs
    }
  }
`;

export default async function Page({ searchParams, params }) {
  let { page, sorts } = await searchParams;
  const { employmentId } = await params;
  const currentPage = Number(page || 1);

  const promise = apiQuery(QUERY, {
    employmentId,
    sorts,
    page: currentPage,
  });

  return (
    <div
      className="bg-background h-screen space-y-4 p-4"
      style={{ height: "calc(100% - 106px)" }}
    >
      <FamilyPassTable findFamilyPasses={promise} currentPage={currentPage} />
    </div>
  );
}
