import { useFormContext, useWatch } from "react-hook-form";
import { FormBody, FormRow, FormSection } from "@/ui/Form";
import Input from "@/ui/Input";
import DobDatePickerInput from "@/ui/datetime/DobDatePickerInput";
import GenderSelect from "@/components/select/GenderSelect";
import MaritalStatusSelect from "@/components/select/MaritalStatusSelect";
import RaceSelect from "@/components/select/RaceSelect";
import ReligionSelect from "@/components/select/ReligionSelect";
import NationalitySelect from "@/components/select/NationalitySelect";
import TextArea from "@/ui/TextArea";
import CountrySelect from "@/components/select/CountrySelect";
import DatePickerInput from "@/ui/datetime/DatePickerInput";
import { sub, add } from "date-fns";
import TravelDocumentSelect from "@/components/select/TravelDocumentSelect";
import ageCalculator from "@/lib/ageCalculator";
import { isNil } from "lodash";

export default function Part2Form({ spouseRelationship }) {
  const {
    control,
    getValues,
    register,
    formState: { errors },
  } = useFormContext();

  const watchNationality = useWatch({ control, name: "nationality" });
  const watchPart1Q1Answer = useWatch({
    control,
    name: "particulars.0.answer",
  });
  const localDependant = useWatch({
    control,
    name: "localDependant",
  });
  const watchDob = useWatch({ control, name: "dob" });
  const age = ageCalculator(watchDob);

  const unMarriedChildBelow12 =
    watchPart1Q1Answer === "Unmarried Child" &&
    localDependant === "No" &&
    !isNil(age) &&
    age <= 12;

  return (
    <FormBody className="space-y-8">
      <FormSection title={`A. Dependant Personal Information`}>
        <FormRow>
          <Input
            data-1p-ignore
            label="Full name (in the same order of appearance as travel document)"
            {...register("name", { required: "Name is required!" })}
            error={errors.name}
          />
        </FormRow>

        <FormRow>
          <DobDatePickerInput
            name="dob"
            rules={{ required: "DOB is required!" }}
          />
          <GenderSelect
            {...register("gender", { required: "Gender is required!" })}
            error={errors.gender}
          />
        </FormRow>

        <FormRow>
          <NationalitySelect
            foreigner
            label="Nationality/Citizenship"
            {...register("nationality", {
              required: "Nationality is required!",
            })}
            error={errors.nationality}
          />

          <RaceSelect
            {...register("race", { required: "Race is required!" })}
            error={errors.race}
          />
          <ReligionSelect
            {...register("religion", { required: "Religion is required!" })}
            error={errors.religion}
          />
        </FormRow>

        <FormRow colsCount={2}>
          {spouseRelationship ? (
            <div>Marital status: {getValues("maritalStatus")}</div>
          ) : (
            <MaritalStatusSelect
              {...register("maritalStatus", {
                required: "Marital status is required!",
              })}
              error={errors.maritalStatus}
            />
          )}

          {unMarriedChildBelow12 && (
            <Input
              label="HPB reference number"
              {...register("hpbVaccinationReferenceNumber", {
                required:
                  unMarriedChildBelow12 && "Reference number is required!",
              })}
              error={errors.hpbVaccinationReferenceNumber}
            />
          )}
        </FormRow>
      </FormSection>

      <FormSection title="B. Contract Details">
        <FormRow>
          <Input
            data-1p-ignore
            label="Email"
            type="email"
            {...register("email", { required: "Email is required!" })}
            error={errors.email}
          />
          <Input
            type="tel"
            label="Home number (optional)"
            {...register("homeNumber")}
          />
          <Input
            type="tel"
            label="Mobile number"
            {...register("mobileNumber", {
              required: "Mobile number is required!",
            })}
            error={errors.mobileNumber}
          />
        </FormRow>

        <FormRow>
          <TextArea
            label="Address in Singapore"
            {...register("localAddress", { required: "Address is required!" })}
            error={errors.localAddress}
          />
        </FormRow>
      </FormSection>

      <FormSection title="C. Travel Document Information">
        <FormRow>
          <TravelDocumentSelect
            {...register("travelDocumentType", {
              required: "Type is required!",
            })}
            error={errors.travelDocumentType}
          />

          <Input
            label="Travel document number"
            {...register("travelDocumentNumber", {
              required: "Number is required!",
            })}
            error={errors.travelDocumentNumber}
          />

          <DatePickerInput
            label="Issue date"
            name="travelDocumentIssueDate"
            minDate={sub(new Date(), { years: 20 })}
            maxDate={add(new Date(), { years: 3 })}
            shortcuts={false}
            rules={{
              required: "Issue date is required!",
            }}
          />

          <DatePickerInput
            label="Expiry date"
            name="travelDocumentExpiryDate"
            minDate={sub(new Date(), { years: 5 })}
            maxDate={add(new Date(), { years: 20 })}
            shortcuts={false}
            rules={{
              required: "Expiry date is required!",
            }}
          />
        </FormRow>

        <FormRow>
          <CountrySelect
            label="Country of birth"
            {...register("travelDocumentCountryOfBirth", {
              required: "Country is required!",
            })}
            error={errors.travelDocumentCountryOfBirth}
          />

          <CountrySelect
            label="Country of origin"
            {...register("travelDocumentCountryOfOrigin", {
              required: "Country is required!",
            })}
            error={errors.travelDocumentCountryOfOrigin}
          />

          {watchNationality === "Malaysian" && (
            <Input
              label="Malaysian NRIC number"
              {...register("travelDocumentMalaysianIcNumber", {
                required: "NRIC is required!",
              })}
              error={errors.travelDocumentMalaysianIcNumber}
            />
          )}
        </FormRow>

        <FormRow>
          <Input
            data-1p-ignore
            label="State/province of birth"
            {...register("travelDocumentStateProvinceOfBirth", {
              required: "State/province is required!",
            })}
            error={errors.travelDocumentStateProvinceOfBirth}
          />

          <Input
            data-1p-ignore
            label="State/province of origin"
            {...register("travelDocumentStateProvinceOfOrigin", {
              required: "State/province is required!",
            })}
            error={errors.travelDocumentStateProvinceOfOrigin}
          />
        </FormRow>
      </FormSection>
    </FormBody>
  );
}
