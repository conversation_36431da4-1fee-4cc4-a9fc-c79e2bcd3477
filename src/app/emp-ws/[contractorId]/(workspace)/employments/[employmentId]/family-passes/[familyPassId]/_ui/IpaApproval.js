import Button from "@/ui/Button";
import { ChevronRightIcon } from "lucide-react";
import { ProgressBarLink } from "@/ui/ProgressBar";
import HStack from "@/ui/HStack";
import StatusIndicator from "@/ui/StatusIndicator";
import DownloadFileWithIcon from "@/components/DownloadFileWithIcon";
import { Item } from "@/components/accordion-variants/left-arrow-basic";
import findEmploymentFile from "@/lib/employment-files/findEmploymentFile";
import { useParams } from "next/navigation";

export default function IpaApproval({ familyPass }) {
  const {
    slug,
    isLocPass,
    ipaApproved,
    formalityCompletedAt,
    familyPassConsentForm,
    passSubmittedAt,
  } = familyPass;
  const { contractorId, employmentId } = useParams();

  const baseUrl = `/emp-ws/${contractorId}/employments/${employmentId}/family-passes/${slug}`;

  // const ipaLetterFile = findEmploymentFile({
  //   files: employmentFiles,
  //   mainDescription: "Family Pass Related",
  //   description: "IPA (Foreigner Copy)",
  // });

  if (isLocPass) return null;

  return (
    <Item
      title="In-Principle Approval"
      value="2"
      suffix={
        ipaApproved && (
          <Button
            variant="secondary"
            asChild
            suffix={<ChevronRightIcon size={20} />}
            className="text-rose-600"
          >
            <ProgressBarLink href={`${baseUrl}/family-pass-formalities`}>
              {formalityCompletedAt
                ? "View formalities"
                : "Complete formalities"}
            </ProgressBarLink>
          </Button>
        )
      }
    >
      <div className="space-y-4">
        {ipaApproved ? (
          <HStack>
            <StatusIndicator status={"APPROVED"} /> Pass has been approved.{" "}
            {!formalityCompletedAt && "Please complete your pass formalities."}
          </HStack>
        ) : (
          <HStack>
            <StatusIndicator status={"PENDING"} /> Waiting for IPA approval.
          </HStack>
        )}

        {/* <DownloadFileWithIcon file={ipaLetterFile} /> */}
      </div>
    </Item>
  );
}
