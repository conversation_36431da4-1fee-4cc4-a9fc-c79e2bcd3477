import Card from "@/ui/Card";
import VStack from "@/ui/VStack";

export default function Part4Submitted({ familyPassConsentForm }) {
  return (
    <>
      <h2 className="border-b py-2 text-lg leading-none font-semibold text-amber-800">
        Part 4: Dependant Declaration
      </h2>
      <Card variant="light">
        <table className="form-table">
          <tbody>
            {familyPassConsentForm.declarations.map((declaration, index) => {
              return (
                <tr key={index}>
                  <td className="space-y-2">
                    <div className="grid grid-cols-[32px_1fr]">
                      {index + 1}. <strong>{declaration.question}</strong>
                    </div>
                    <div className="grid grid-cols-[32px_1fr]">
                      <div />
                      <VStack>
                        <div>{declaration.answer}</div>
                        {declaration.details && (
                          <div className="text-muted">
                            {declaration.details}
                          </div>
                        )}
                      </VStack>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </Card>
    </>
  );
}
