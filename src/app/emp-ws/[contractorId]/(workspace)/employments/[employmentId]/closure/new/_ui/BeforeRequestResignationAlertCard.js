"use client";

import AlertCard from "@/ui/AlertCard";

export default function BeforeRequestResignationAlertCard({ employment }) {
  return (
    <div className="mx-auto max-w-[600px]">
      <AlertCard variant="error" ring={false} center>
        <div className="space-y-4">
          Before requesting your resignation as{" "}
          <span className="font-semibold">{employment.designation}</span> from{" "}
          <span className="font-semibold">{employment.company.name}</span>, do
          speak and flag your manager first to ensure they are aware of your
          intention.
        </div>
      </AlertCard>
    </div>
  );
}
