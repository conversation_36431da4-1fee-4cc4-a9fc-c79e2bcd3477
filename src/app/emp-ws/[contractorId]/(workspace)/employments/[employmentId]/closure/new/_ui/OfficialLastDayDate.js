"use client";

import { useWatch } from "react-hook-form";
import { toFormalDate } from "@/formatters/date";
import { parseDateFromDatePicker } from "@/ui/Form";
import VStack from "@/ui/VStack";

export default function OfficialLastDayDate() {
  const lastDayDate = useWatch({ name: "lastDayDate" });

  return (
    <VStack className="gap-0">
      <label htmlFor="lastDayDate" className="text-sm text-muted">
        Official last day
      </label>
      <p>{toFormalDate(parseDateFromDatePicker(lastDayDate))}</p>
    </VStack>
  );
}
