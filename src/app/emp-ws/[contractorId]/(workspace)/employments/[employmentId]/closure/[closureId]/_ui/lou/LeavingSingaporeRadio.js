"use client";

import { FormRow } from "@/ui/Form";
import { RadioGroup, RadioGroupItem } from "@/ui/RadioGroup";
import VStack from "@/ui/VStack";

export default function LeavingSingaporeRadio() {
  return (
    <FormRow>
      <VStack>
        <p>Are you leaving Singapore permanently?</p>
        <RadioGroup
          name="leavingSingaporePermanently"
          rules={{ required: true }}
        >
          <label className="inline-flex w-fit cursor-pointer items-center gap-2">
            <RadioGroupItem value="Yes" /> Yes
          </label>
          <label className="inline-flex w-fit cursor-pointer items-center gap-2">
            <RadioGroupItem value="No" /> No
          </label>
        </RadioGroup>
      </VStack>
    </FormRow>
  );
}
