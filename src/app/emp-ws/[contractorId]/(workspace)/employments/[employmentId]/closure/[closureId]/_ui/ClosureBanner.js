import { toFormalDateTime } from "@/formatters/date";
import AlertCard from "@/ui/AlertCard";

export default function ClosureBanner({ closure }) {
  if (closure.status === "CONFIRMED")
    return (
      <AlertCard
        variant="info"
        containerClassName="bg-blue-50/80 outline outline-blue-900 dark:outline-blue-200"
        ring={false}
        center
      >
        Please read through and acknowledge the closure terms.
      </AlertCard>
    );

  return (
    <AlertCard
      variant="info"
      containerClassName="bg-blue-50/80 outline outline-blue-900 dark:outline-blue-200"
      ring={false}
      center
    >
      You have acknowledged to this closure terms on{" "}
      {toFormalDateTime(closure.contractorAgreedAt)}.
    </AlertCard>
  );
}
