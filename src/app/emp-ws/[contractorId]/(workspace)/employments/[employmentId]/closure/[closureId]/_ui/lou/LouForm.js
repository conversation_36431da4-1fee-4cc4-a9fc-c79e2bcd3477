"use client";

import { useFormContext } from "react-hook-form";
import { FormSection } from "@/ui/Form";
import Card from "@/ui/Card";
import AlertCard from "@/ui/AlertCard";
import LeavingSingaporeRadio from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/LeavingSingaporeRadio";
import DeclarationDate from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/DeclarationDate";
import DeclareTo from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/DeclareTo";
import ContractorDetailInputs from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/ContractorDetailInputs";
import Paragraph1 from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/Paragraph1";
import Paragraph2 from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/Paragraph2";
import ContractorSignature from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/ContractorSignature";

export default function LouForm() {
  const { watch } = useFormContext();
  const leavingSingaporePermanently = watch("leavingSingaporePermanently");

  return (
    <>
      <h1 className="text-lg font-bold">Letter of Undertaking (IRAS)</h1>

      <Card className="mx-auto w-[900px] space-y-8 rounded-none px-8 py-8 shadow-md sm:px-16">
        <FormSection>
          <AlertCard variant="alert" center>
            You are required to complete this section.
          </AlertCard>

          <LeavingSingaporeRadio />
        </FormSection>

        {leavingSingaporePermanently !== null && (
          <div className="overflow-x-scroll border-t pt-6">
            <div className="space-y-8 px-1">
              <DeclarationDate />

              <DeclareTo />

              <ContractorDetailInputs />

              <Paragraph1 />

              <Paragraph2 />

              <ContractorSignature />
            </div>
          </div>
        )}
      </Card>
    </>
  );
}
