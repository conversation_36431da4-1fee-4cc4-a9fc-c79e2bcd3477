"use client";

import { useFormContext, useWatch } from "react-hook-form";
import TextArea from "@/ui/TextArea";

export default function SpecialRequestReasonTextArea() {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const [specialRequest, specialRequestType] = useWatch({
    name: ["specialRequest", "specialRequestType"],
  });
  const requiredSpecialRequestReason = specialRequest === "Yes";
  const placeholder =
    specialRequestType === "Early release"
      ? "Let us know your reason for early release"
      : "Provide any supporting details for your special request";

  return (
    <TextArea
      label="Reason of special request"
      placeholder={placeholder}
      {...register("specialRequestReason", {
        required: requiredSpecialRequestReason && "Reason is required!",
      })}
      error={errors.specialRequestReason}
    />
  );
}
