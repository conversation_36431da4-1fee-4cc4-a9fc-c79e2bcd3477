"use client";

import { List, PropertyList } from "@/ui/PropertyList";
import Card from "@/ui/Card";

export default function ContractDetailsPropertyCard({ employment }) {
  return (
    <Card>
      <PropertyList title="Contract Details">
        <List.Date label="Commencement date">
          {employment.commencementDate}
        </List.Date>
        <List label="Company">{employment.company.name}</List>
        <List label="Designation">{employment.designation}</List>
      </PropertyList>
    </Card>
  );
}
