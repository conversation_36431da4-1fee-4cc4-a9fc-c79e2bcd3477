"use client";

import { useWatch } from "react-hook-form";
import { parseISO } from "date-fns";
import FutureDatePickerInput from "@/ui/datetime/FutureDatePickerInput";

export default function RequestedLastDayDatePicker({ employment }) {
  const specialRequest = useWatch({ name: "specialRequest" });
  const requiredRequestedLastDayDate = specialRequest === "Yes";

  return (
    <FutureDatePickerInput
      label="Requested last day"
      name="requestedLastDayDate"
      shortcuts={false}
      rules={{
        required:
          requiredRequestedLastDayDate && "Requested last day is required!",
      }}
      maxDate={parseISO(employment.expiredDate)}
    />
  );
}
