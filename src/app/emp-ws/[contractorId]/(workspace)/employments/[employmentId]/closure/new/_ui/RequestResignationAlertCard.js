"use client";

import AlertCard from "@/ui/AlertCard";

export default function RequestResignationAlertCard() {
  return (
    <AlertCard variant="pause" ring={false}>
      <p>
        By submitting this resignation request, I hereby understand and agree
        with the following terms:
      </p>

      <ul className="list-outside list-disc pl-8">
        <li>
          Any special request that I may have submitted will be subject to
          company&apos;s discretion and approval;
        </li>
        <li>
          Meanwhile, I shall serve and comply with the default{" "}
          <span className="font-semibold">Official Last Day</span> while pending
          for official approval of my resignation request;
        </li>
        <li>
          In the event if I have requested for an early release and should my
          requested last day be approved, I agreed that I will compensate the
          remaining salary in-lieu of notice immediately upon company&apos;s
          request.
        </li>
        <li>
          Upon resignation, any leave application with applied dates beyond{" "}
          <span className="font-semibold">Official Last Day</span> will
          automatically be cancelled.
        </li>
      </ul>
    </AlertCard>
  );
}
