"use client";

import { useFormContext } from "react-hook-form";

export default function Paragraph1() {
  return (
    <p className="w-full leading-8">
      I, <Name /> (NRIC: <Nric />
      ), declare that <LeavingSingaporeDecision /> permanently after I have
      ceased my employment with the above-mentioned company.
    </p>
  );
}

function Name() {
  const { watch } = useFormContext();
  const name = watch("name");

  return (
    <label
      htmlFor="name"
      className="inline-block min-w-[200px] border-b border-b-black px-2 text-center leading-tight font-semibold"
    >
      {name}
    </label>
  );
}

function Nric() {
  const { watch } = useFormContext();
  const nric = watch("nric");

  return (
    <label
      htmlFor="nric"
      className="inline-block min-w-[120px] border-b border-b-black px-2 text-center leading-tight font-semibold"
    >
      {nric}
    </label>
  );
}

function LeavingSingaporeDecision() {
  const { watch } = useFormContext();
  const leavingSingaporePermanently = watch("leavingSingaporePermanently");

  return (
    <span className="font-semibold text-danger">
      I {leavingSingaporePermanently === "Yes" ? "am" : "am not"} leaving
      Singapore
    </span>
  );
}
