"use client";

import { H2 } from "@/ui/Typography";
import VStack from "@/ui/VStack";
import Card from "@/ui/Card";
import RawHtml from "@/ui/RawHtml";

export default function TerminationNoticeCard({ employment }) {
  return (
    <Card className="space-y-4 rounded-none bg-white px-8 py-8 shadow-md sm:px-16">
      <VStack className="gap-0 border-b pb-4">
        <H2>Termination Notice</H2>
        <p className="text-sm text-muted">
          Please read through this termination notice carefully before
          proceeding.
        </p>
      </VStack>

      <div className="max-h-[350px] overflow-y-scroll">
        <RawHtml className="text-sm">
          {employment.ent?.terminationTemplate}
        </RawHtml>
      </div>
    </Card>
  );
}
