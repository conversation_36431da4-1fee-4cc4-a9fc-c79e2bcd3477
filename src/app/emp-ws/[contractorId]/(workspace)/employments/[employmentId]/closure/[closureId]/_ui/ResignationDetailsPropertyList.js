"use client";

import pluralize from "pluralize";
import { List, PropertyList } from "@/ui/PropertyList";

export default function ResignationDetailsPropertyList({ closure }) {
  return (
    <>
      <PropertyList
        title={`${closure.closureType} - ${closure.prevDesignation}`}
      >
        <List label="Reference">
          {closure.employment.fmId} - {closure.fmId}
        </List>
        <List label="AL balance">
          {pluralize("day", closure.alBalanceDays, true)}
        </List>
        <List label="AL instruction">{closure.alInstruction}</List>
        <List.Date label="Official last day">{closure.lastDayDate}</List.Date>
        <List.Timestamp label="Submitted">{closure.createdAt}</List.Timestamp>
        <List.Status label="Status">{closure.status}</List.Status>
      </PropertyList>

      {closure.specialRequest && (
        <>
          <PropertyList title="Special Request">
            <List label="Request for">{closure.specialRequestType}</List>
            <List.Date label="Requested last day">
              {closure.requestedLastDayDate}
            </List.Date>
            <List.Date label="Approved last day">
              {closure.approvedLastDayDate}
            </List.Date>
          </PropertyList>
        </>
      )}
    </>
  );
}
