"use client";

import { currency } from "@/formatters/numeric";
import isNotEmpty from "@/lib/isNotEmpty";
import pluralize from "pluralize";
import { List, PropertyList } from "@/ui/PropertyList";

export default function ContractCompletionPropertyList({ closure }) {
  return (
    <PropertyList title="Contract Completion">
      <List label="AL balance">
        {pluralize("day", closure.alBalanceDays, true)}
      </List>
      <List label="AL instruction">{closure.alInstruction}</List>
      {isNotEmpty(closure.bonusesForContractor) && (
        <List label="Bonuses">
          {closure.bonusesForContractor.map(
            ({ chargeDescription, charges }, index) => {
              return (
                <p key={`closure-bonus-${index}`}>
                  {index + 1}. {chargeDescription} - {currency(charges)}
                </p>
              );
            },
          )}
        </List>
      )}
      <List.Date label="Official last day">{closure.lastDayDate}</List.Date>
    </PropertyList>
  );
}
