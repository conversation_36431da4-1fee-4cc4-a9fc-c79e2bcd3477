"use client";

import { useFormContext } from "react-hook-form";
import { toFormalDate } from "@/formatters/date";
import { parseDateFromDatePicker } from "@/ui/Form";

export default function DeclarationDate() {
  const { watch } = useFormContext();
  const declarationDate = watch("declarationDate");

  return (
    <p>
      Date:{" "}
      <span className="font-semibold">
        {toFormalDate(parseDateFromDatePicker(declarationDate))}
      </span>
    </p>
  );
}
