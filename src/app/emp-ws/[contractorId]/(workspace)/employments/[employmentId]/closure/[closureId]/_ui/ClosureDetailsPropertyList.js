"use client";

import EndOfContractDetailsPropertyList from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/EndOfContractDetailsPropertyList";
import ConversionDetailsPropertyList from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/ConversionDetailsPropertyList";
import ResignationDetailsPropertyList from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/ResignationDetailsPropertyList";
import TerminationDetailsPropertyList from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/TerminationDetailsPropertyList";

export default function ClosureDetailsPropertyList({ closure }) {
  switch (closure.closureType) {
    case "End of Contract":
      return <EndOfContractDetailsPropertyList closure={closure} />;
    case "Conversion":
      return <ConversionDetailsPropertyList closure={closure} />;
    case "Resignation":
      return <ResignationDetailsPropertyList closure={closure} />;
    case "Termination":
      return <TerminationDetailsPropertyList closure={closure} />;
    default:
      return null;
  }
}
