"use client";

import { List, PropertyList } from "@/ui/PropertyList";

export default function EndOfContractDetailsPropertyList({ closure }) {
  return (
    <PropertyList title={`${closure.closureType} - ${closure.prevDesignation}`}>
      <List label="Reference">
        {closure.employment.fmId} - {closure.fmId}
      </List>
      <List.Timestamp label="Submitted">{closure.createdAt}</List.Timestamp>
      <List.Status label="Status">{closure.status}</List.Status>
    </PropertyList>
  );
}
