"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useProgressBar } from "@/ui/ProgressBar";
import { startTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { startOfToday } from "date-fns";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { Form, formatDateForDatePicker } from "@/ui/Form";
import HStack from "@/ui/HStack";
import Card from "@/ui/Card";
import Button from "@/ui/Button";
import ClosureBanner from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/ClosureBanner";
import ClosureDetailsPropertyList from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/ClosureDetailsPropertyList";
import ContractCompletionPropertyList from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/ContractCompletionPropertyList";
import ClosureAcknowledgementContent from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/ClosureAcknowledgementContent";
import LouForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/[closureId]/_ui/lou/LouForm";

const MUTATION = `
  mutation empAcknowledgeClosure($employmentId: ID!, $id: ID!, $louInput: EmpClosureLouFormInput!) {
    execute: empAcknowledgeClosure(employmentId: $employmentId, id: $id, louInput: $louInput) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on EndOfContract {
          id
          contractorResourceUrl
        }

        ... on Conversion {
          id
          contractorResourceUrl
        }

        ... on Resignation {
          id
          contractorResourceUrl
        }

        ... on Termination {
          id
          contractorResourceUrl
        }
      }
    }
  }
`;

export default function AcknowledgementClosureForm({ closure }) {
  const { employmentId, closureId } = useParams();
  const router = useRouter();
  const progress = useProgressBar();
  const form = useForm({
    defaultValues: {
      leavingSingaporePermanently: null,
      declarationDate: formatDateForDatePicker(startOfToday()),
      name: closure.employment.contractorName,
      nric: closure.employment.contractorNric,
      contactNumber: "",
      residentialAddress: "",
    },
  });
  const { watch } = form;
  const leavingSingaporePermanently = watch("leavingSingaporePermanently");
  const canAcknowledge =
    closure.employment.residencyStatus !== "Permanent Resident (PR)" ||
    leavingSingaporePermanently !== null;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: ({ record }) => {
      toast.success("Closure acknowledged!");
      router.push(record.contractorResourceUrl);
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    progress.start();

    const newInput = {
      ...input,
      leavingSingaporePermanently: leavingSingaporePermanently === "Yes",
    };

    startTransition(() => {
      execute({ employmentId, id: closureId, louInput: newInput });
      progress.done();
    });
  };

  return (
    <Form form={form} onSubmit={onSubmit} className="relative space-y-4">
      <div className="relative mx-auto max-w-[700px] space-y-4">
        <div className="sticky top-[122px]">
          <ClosureBanner closure={closure} />
        </div>

        <Card className="space-y-4">
          <ClosureDetailsPropertyList closure={closure} />

          {closure.closureType === "End of Contract" && (
            <ContractCompletionPropertyList closure={closure} accordion />
          )}
        </Card>
      </div>

      <ClosureAcknowledgementContent closure={closure} />

      {closure.employment.residencyStatus === "Permanent Resident (PR)" && (
        <LouForm closure={closure} />
      )}

      <HStack>
        <Button
          type="submit"
          className="mx-auto min-w-[300px]"
          loading={pending}
          disabled={!canAcknowledge}
        >
          Acknowledge {closure.closureType}
        </Button>
      </HStack>
    </Form>
  );
}
