"use client";

import { useFormContext, useWatch } from "react-hook-form";
import Select from "@/ui/Select";

export default function SpecialRequestTypeSelect() {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const specialRequest = useWatch({ name: "specialRequest" });
  const hasSpecialRequest = specialRequest === "Yes";

  return (
    <Select
      label="I would like to request for"
      {...register("specialRequestType", {
        required: hasSpecialRequest && "Special request type is required!",
      })}
      error={errors.specialRequestType}
    >
      <option value=""></option>
      <option value="Early release">Early release</option>
      <option value="Others">Others</option>
    </Select>
  );
}
