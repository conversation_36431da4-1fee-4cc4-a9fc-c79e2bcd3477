"use client";

import { useFormContext } from "react-hook-form";
import TextArea from "@/ui/TextArea";

export default function ReasonForLeavingTextArea() {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <TextArea
      id="contractorReason"
      label="Reason for leaving"
      {...register("contractorReason", { required: "Reason is required!" })}
      error={errors.contractorReason}
    />
  );
}
