"use client";

import {
  JOBLINE_ADDRESS,
  JOBLINE_COMPANY_NAME,
  JOBLINE_CONTACT_NUMBER,
} from "@/lib/global";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";

export default function DeclareTo() {
  return (
    <HStack className="flex-nowrap justify-between gap-4">
      <VStack className="gap-0">
        <p>
          To: <span className="font-semibold">{JOBLINE_COMPANY_NAME}</span>
        </p>
        <p className="whitespace-pre-line">{JOBLINE_ADDRESS}</p>
      </VStack>

      <p className="self-end-safe">Tel: {JOBLINE_CONTACT_NUMBER}</p>
    </HStack>
  );
}
