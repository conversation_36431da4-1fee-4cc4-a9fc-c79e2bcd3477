"use client";

import { useFormContext } from "react-hook-form";

export default function Paragraph2() {
  const { watch } = useFormContext();
  const leavingSingaporePermanently = watch("leavingSingaporePermanently");

  return (
    <p className="w-full leading-8">
      {leavingSingaporePermanently === "Yes"
        ? "I agree to comply with the Tax Clearance formalities as per IRAS requirement."
        : "I undertake to inform Inland Revenue Authority of Singapore (IRAS) if I am leaving Singapore permanently and also update on any change in my correspondence address."}
    </p>
  );
}
