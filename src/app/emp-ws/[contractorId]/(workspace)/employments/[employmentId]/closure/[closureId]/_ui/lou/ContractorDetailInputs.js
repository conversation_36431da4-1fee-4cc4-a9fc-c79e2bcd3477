"use client";

import { useFormContext } from "react-hook-form";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import Input from "@/ui/Input";
import TextArea from "@/ui/TextArea";

export default function ContractorDetailInputs({}) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <HStack className="flex-nowrap justify-between gap-4">
      <VStack className="w-1/2 gap-4">
        <Input
          id="name"
          label="Name"
          {...register("name", { required: " " })}
          error={errors.name}
        />

        <Input
          id="nric"
          label="NRIC"
          {...register("nric", { required: " " })}
          error={errors.nric}
        />

        <TextArea
          label="Residential address"
          {...register("residentialAddress", { required: " " })}
          error={errors.residentialAddress}
        />
      </VStack>

      <div className="self-end-safe">
        <HStack className="flex-nowrap">
          <label htmlFor="contactNumber">Tel: </label>
          <Input
            id="contactNumber"
            {...register("contactNumber", { required: " " })}
            error={errors.contactNumber}
          />
        </HStack>
      </div>
    </HStack>
  );
}
