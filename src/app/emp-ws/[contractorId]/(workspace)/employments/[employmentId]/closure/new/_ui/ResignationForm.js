"use client";

import { startTransition } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { useProgressBar } from "@/ui/ProgressBar";
import { addDays, startOfToday } from "date-fns";
import { toast } from "sonner";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import {
  Form,
  formatDateForDatePicker,
  FormBody,
  FormRow,
  FormSection,
  showFormErrors,
} from "@/ui/Form";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import BeforeRequestResignationAlertCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/BeforeRequestResignationAlertCard";
import TerminationNoticeCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/TerminationNoticeCard";
import ContractDetailsPropertyCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/ContractDetailsPropertyCard";
import ResignationEffectiveDate from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/ResignationEffectiveDate";
import OfficialLastDayDate from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/OfficialLastDayDate";
import ReasonForLeavingTextArea from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/ReasonForLeavingTextArea";
import SpecialRequestInputs from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/SpecialRequestInputs";
import RequestResignationAlertCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/RequestResignationAlertCard";
import SupportingDocumentUploader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/SupportingDocumentUploader";

const MUTATION = `
  mutation empCreateResignation($employmentId: ID!, $input: ResignationInput!) {
    execute:empCreateResignation(
      employmentId: $employmentId,
      input: $input
    ) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Resignation {
          id
          slug
        }
      }
    }
  }
`;

export default function ResignationForm({ employment }) {
  const router = useRouter();
  const progress = useProgressBar();
  const { contractorId, employmentId } = useParams();
  const form = useForm({
    defaultValues: {
      effectiveFromDate: formatDateForDatePicker(startOfToday()),
      lastDayDate: formatDateForDatePicker(
        addDays(startOfToday(), employment.effectiveNoticeDays),
      ),
      contractorReason: "",
      specialRequest: "",
      specialRequestType: "",
      requestedLastDayDate: formatDateForDatePicker(
        addDays(startOfToday(), employment.effectiveNoticeDays),
      ),
      specialRequestReason: "",
      contractorSupportingDocument: null,
    },
  });
  const { setError } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      router.push(
        `/emp-ws/${contractorId}/employments/${employmentId}/closure`,
      );
      toast.success("Resignation submitted!");
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    const newInput = {
      ...input,
      specialRequest: input.specialRequest === "Yes",
    };

    progress.start();

    startTransition(() => {
      execute({ employmentId, input: newInput });
      progress.done();
    });
  };

  return (
    <div className="mx-auto max-w-[900px] space-y-4">
      <BeforeRequestResignationAlertCard employment={employment} />

      <TerminationNoticeCard employment={employment} />

      <Form form={form} onSubmit={onSubmit} className="bg-background">
        <FormSection className="mx-auto max-w-[700px]">
          <FormBody>
            <ContractDetailsPropertyCard employment={employment} />

            <FormSection title="Resignation Details">
              <FormRow>
                <ResignationEffectiveDate />

                <OfficialLastDayDate />
              </FormRow>

              <FormRow>
                <ReasonForLeavingTextArea />
              </FormRow>

              <FormRow>
                <SpecialRequestInputs employment={employment} />
              </FormRow>
            </FormSection>

            <FormSection
              title={
                <p>
                  Supporting Document{" "}
                  <span className="text-sm font-normal">(optional)</span>
                </p>
              }
              description="If you have already submitted your written resignation to your manager, please upload a copy for our record."
            >
              <SupportingDocumentUploader />
            </FormSection>

            <RequestResignationAlertCard />
          </FormBody>

          <HStack className="w-full justify-end gap-4">
            <Button
              variant="secondary"
              outline
              disabled={pending}
              onClick={() =>
                router.push(
                  `/emp-ws/${contractorId}/employments/${employmentId}/closure`,
                )
              }
            >
              Dismiss
            </Button>
            <Button type="submit" disabled={pending} loading={pending}>
              Submit
            </Button>
          </HStack>
        </FormSection>
      </Form>
    </div>
  );
}
