"use client";

import { isEmpty } from "lodash";
import { toSimpleDateTime } from "@/formatters/date";
import HStack from "@/ui/HStack";
import FileLink from "@/ui/file-management/FileLink";

export default function Documents({ closure }) {
  const { employmentFiles } = closure;
  if (closure.status !== "AGREED" || isEmpty(employmentFiles)) return null;

  return (
    <HStack className="gap-x-6 divide-y text-sm sm:divide-y-0">
      {employmentFiles.map((file, index) => {
        return (
          <FileLink
            key={`file-${index}`}
            title={`${file.fileName} (${file.fileSize})\n\nUploaded at ${toSimpleDateTime(file.uploadedAt)}`}
            className="w-full pb-2 last:pb-0 sm:w-fit sm:pb-0"
            file={file}
          />
        );
      })}
    </HStack>
  );
}
