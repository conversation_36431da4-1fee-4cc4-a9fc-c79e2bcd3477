"use client";

import { RadioGroup, RadioGroupItem } from "@/ui/RadioGroup";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";

export default function SpecialRequestRadio() {
  return (
    <HStack className="gap-x-8">
      <VStack className="gap-0">
        <p>Do you have any special request?</p>
        <p className="text-sm text-muted">Subject to approval</p>
      </VStack>

      <RadioGroup
        name="specialRequest"
        className="gap-4 sm:gap-4"
        rules={{ required: "Special request is required!" }}
      >
        <label className="inline-flex w-fit cursor-pointer items-center gap-2">
          <RadioGroupItem value="Yes" /> Yes
        </label>

        <label className="inline-flex w-fit cursor-pointer items-center gap-2">
          <RadioGroupItem value="No" /> No
        </label>
      </RadioGroup>
    </HStack>
  );
}
