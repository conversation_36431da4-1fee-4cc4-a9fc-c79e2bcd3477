"use client";

import { useWatch } from "react-hook-form";
import { FormRow, FormSection } from "@/ui/Form";
import Card from "@/ui/Card";
import SpecialRequestRadio from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/SpecialRequestRadio";
import SpecialRequestTypeSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/SpecialRequestTypeSelect";
import RequestedLastDayDatePicker from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/RequestedLastDayDatePicker";
import SpecialRequestReasonTextArea from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/closure/new/_ui/SpecialRequestReasonTextArea";

export default function SpecialRequestInputs({ employment }) {
  const specialRequest = useWatch({ name: "specialRequest" });
  const hasSpecialRequest = specialRequest === "Yes";

  return (
    <Card variant="transparent" className="bg-gray-50">
      <FormSection>
        <FormRow>
          <SpecialRequestRadio />
        </FormRow>

        {hasSpecialRequest && (
          <Card variant="transparent" className="rounded-md">
            <FormSection>
              <FormRow colsCount={2}>
                <SpecialRequestTypeSelect />

                <RequestedLastDayDatePicker employment={employment} />
              </FormRow>

              <FormRow>
                <SpecialRequestReasonTextArea />
              </FormRow>
            </FormSection>
          </Card>
        )}
      </FormSection>
    </Card>
  );
}
