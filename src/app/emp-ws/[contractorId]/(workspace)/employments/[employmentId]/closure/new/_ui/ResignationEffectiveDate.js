"use client";

import { useWatch } from "react-hook-form";
import { toFormalDate } from "@/formatters/date";
import { parseDateFromDatePicker } from "@/ui/Form";
import VStack from "@/ui/VStack";

export default function ResignationEffectiveDate() {
  const effectiveFromDate = useWatch({ name: "effectiveFromDate" });

  return (
    <VStack className="gap-0">
      <label htmlFor="effectiveFromDate" className="text-sm text-muted">
        Resignation effective date
      </label>
      <p>{toFormalDate(parseDateFromDatePicker(effectiveFromDate))}</p>
    </VStack>
  );
}
