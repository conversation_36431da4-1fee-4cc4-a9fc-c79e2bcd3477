"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { CalendarIcon, TableIcon } from "lucide-react";
import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";

export default function ViewModeSegmentedControl() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  params.set("view", params.get("view") || "table");
  const href = `${pathname}?${params.toString()}`;

  return (
    <SegmentedControlRoot value={href}>
      <Item value="table" />
      <Item value="calendar" />
    </SegmentedControlRoot>
  );
}

function Item({ value }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  params.set("view", value);
  const href = `${pathname}?${params.toString()}`;

  return (
    <SegmentedControlItem href={href}>
      {value === "table" ? <TableIcon size={20} /> : <CalendarIcon size={20} />}
    </SegmentedControlItem>
  );
}
