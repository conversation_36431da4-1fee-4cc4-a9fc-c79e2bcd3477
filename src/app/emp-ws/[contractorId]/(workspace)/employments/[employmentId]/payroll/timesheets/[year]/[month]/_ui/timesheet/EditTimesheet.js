import { Form, showFormErrors } from "@/ui/Form";
import { toast } from "sonner";
import VStack from "@/ui/VStack";
import { useState } from "react";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import useAction from "@/hooks/useAction";
import boundAction from "@/lib/boundAction";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import newTimesheetDays from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/newTimesheetDays";
import timesheetDaysForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/timesheetDaysForm";
import TimesheetHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheet/TimesheetHeader";
import EditTimesheetDay from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/edit/EditTimesheetDay";
import { validateTimesheetInput } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";
import { getTotalHours } from "@/lib/timesheet/calculator";

const MUTATION = `
  mutation empCreateTimesheet($employmentId: ID!, $input: TimesheetInput!) {
    execute:empCreateTimesheet(employmentId: $employmentId, input: $input) {
      userErrors {
        path
        message
      }

      success

      record {
        ... on Timesheet {
          id
          slug
        }
      }
    }
  }
`;

export default function EditTimesheet({ employment, timesheet }) {
  const router = useRouter();
  const { employmentId, year: urlYear, month: urlMonth } = useParams();

  const { currentLeaveTerm } = employment;

  const [formData, setFormData] = useState(null);

  const timesheetForm = timesheet
    ? timesheetDaysForm(timesheet.timesheetDays)
    : newTimesheetDays(employment, urlMonth, urlYear);

  const form = useForm({
    defaultValues: {
      timesheetDays: timesheetForm.map((timesheetDay) => {
        const { index, dayOfWeek, isWorkingDay, ...remainData } = timesheetDay;

        return { ...remainData };
      }),
    },
  });

  const {
    setValue,
    handleSubmit,
    setError,
    formState: { errors },
  } = form;

  const [execute, pending] = useAction(boundAction(MUTATION), {
    onSuccess: () => {
      toast.success("Timesheet submitted successfully!");
      router.refresh();
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    const validatedInput = validateTimesheetInput(input, urlYear, urlMonth);
    setFormData(validatedInput);

    console.log({
      employmentId,
      input: validatedInput,
    });
    // execute({
    //   employmentId,
    //   input: validatedInput,
    // });
  };

  const submitted = timesheet?.status === "SUBMITTED";

  return (
    <Form form={form} onSubmit={onSubmit} formData={formData}>
      <TimesheetHeader employment={employment} timesheet={timesheet}>
        <ActionButtons
          submitted={submitted}
          pending={pending}
          onActionClick={(action) => {
            setValue("action", action);
            handleSubmit(onSubmit)();
          }}
        />
      </TimesheetHeader>

      <div className="space-y-2 p-4 pt-2">
        <VStack className="mx-auto w-full max-w-[900px] gap-1 overflow-hidden overflow-x-scroll">
          {timesheetForm.map((timesheetDay, index) => {
            return (
              <EditTimesheetDay
                key={`date-${timesheetDay}-${index}`}
                index={index}
                timesheetDay={timesheetDay}
                currentLeaveTerm={currentLeaveTerm}
              />
            );
          })}
        </VStack>
      </div>
    </Form>
  );
}

const ActionButtons = ({ pending, submitted, onActionClick = () => {} }) => {
  if (submitted)
    return (
      <Button variant="success" disabled>
        Pending for approval
      </Button>
    );

  return (
    <HStack>
      <Button
        variant="ghost"
        type="button"
        onClick={() => {
          onActionClick("DRAFT");
        }}
        loading={pending}
      >
        Save as draft
      </Button>

      <Button
        variant="success"
        type="button"
        onClick={() => {
          onActionClick("SUBMITTED");
        }}
        loading={pending}
      >
        Submit for approval
      </Button>
    </HStack>
  );
};
