import EditTimesheetTime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/edit/EditTimesheetTime";
import { isEmpty } from "lodash";
import { useFormContext, useWatch } from "react-hook-form";
import {
  isFullDayLeave,
  isHalfDayLeave,
} from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";

export default function EditNormalTime({
  index,
  timesheetDay,
  currentLeaveTerm = {},
}) {
  const { control, setValue } = useFormContext();
  const watchTimeOff = useWatch({
    control,
    name: `timesheetDays.[${index}].timeOff`,
  });

  if (isEmpty(timesheetDay)) return null;

  const { leave } = timesheetDay;
  const hasFullDayLeave = isFullDayLeave(leave);
  const hasHalfDayLeave = isHalfDayLeave(leave);
  const fullDayTimeOff = watchTimeOff === "Full";
  const halfDayTimeOff = ["AM", "PM"].includes(watchTimeOff);

  // Full day leave or full day time-off, no normal time input
  if (hasFullDayLeave || fullDayTimeOff) {
    return null;
  }
  // Half day leave and half day time-off, no normal time input
  if (hasHalfDayLeave && halfDayTimeOff) {
    // clearNormalWorkingTime(index, setValue);
    return null;
  }
  if (!timesheetDay.isWorkingDay) return null;

  const fieldNames = {
    timeInName: `timesheetDays.[${index}].normalTimeIn`,
    timeOutName: `timesheetDays.[${index}].normalTimeOut`,
    breakDurationName: `timesheetDays.[${index}].normalBreakDuration`,
    hoursName: `timesheetDays.[${index}].normalHours`,
  };

  return (
    <EditTimesheetTime
      index={index}
      currentLeaveTerm={currentLeaveTerm}
      timesheetDay={timesheetDay}
      fieldNames={fieldNames}
    />
  );
}
