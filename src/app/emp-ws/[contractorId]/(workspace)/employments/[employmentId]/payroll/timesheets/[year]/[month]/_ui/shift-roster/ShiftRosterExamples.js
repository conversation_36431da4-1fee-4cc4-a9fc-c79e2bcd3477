const rosters = [
  {
    team: "A",
    dayShifts: ["03", "04", "11", "12", "19", "20", "27", "28"],
    noonShifts: ["01", "02", "09", "10", "17", "18", "25", "26"],
    nightShifts: ["05", "06", "13", "14", "21", "22", "29", "30"],
    off: ["07", "08", "15", "16", "23", "24", "31"],
    dayShiftTimeIn: "03:00 PM",
    dayShiftTimeOut: "11:00 PM",
    dayShiftDays: "5 days",
    noonShiftTimeIn: "07:00 AM",
    noonShiftTimeOut: "03:00 PM",
    noonShiftDays: "5 days",
    nightShiftTimeIn: "11:00 PM",
    nightShiftTimeOut: "07:00 AM",
    nightShiftDays: "5 days",
    offDays: "16 days",
  },
  {
    team: "B",
    dayShifts: ["07", "08", "15", "16", "23", "24", "31"],
    noonShifts: ["01", "02", "09", "10", "17", "18", "25", "26"],
    nightShifts: ["03", "04", "11", "12", "19", "20", "27", "28"],
    off: ["05", "06", "13", "14", "21", "22", "29", "30"],
    dayShiftTimeIn: "03:00 PM",
    dayShiftTimeOut: "11:00 PM",
    dayShiftDays: "5 days",
    noonShiftTimeIn: "07:00 AM",
    noonShiftTimeOut: "03:00 PM",
    noonShiftDays: "5 days",
    nightShiftTimeIn: "11:00 PM",
    nightShiftTimeOut: "07:00 AM",
    nightShiftDays: "6 days",
    offDays: "15 days",
  },
  {
    team: "C",
    dayShifts: ["05", "06", "13", "14", "21", "22", "29", "30"],
    noonShifts: ["07", "08", "15", "16", "23", "24", "31"],
    nightShifts: ["01", "02", "09", "10", "17", "18", "25", "26"],
    off: ["03", "04", "11", "12", "19", "20", "27", "28"],
    dayShiftTimeIn: "03:00 PM",
    dayShiftTimeOut: "11:00 PM",
    dayShiftDays: "6 days",
    noonShiftTimeIn: "07:00 AM",
    noonShiftTimeOut: "03:00 PM",
    noonShiftDays: "5 days",
    nightShiftTimeIn: "11:00 PM",
    nightShiftTimeOut: "07:00 AM",
    nightShiftDays: "5 days",
    offDays: "15 days",
  },
  {
    team: "D",
    dayShifts: ["03", "04", "11", "12", "19", "20", "27", "28"],
    noonShifts: ["05", "06", "13", "14", "21", "22", "29", "30"],
    nightShifts: ["07", "08", "15", "16", "23", "24", "31"],
    off: ["01", "02", "09", "10", "17", "18", "25", "26"],
    dayShiftTimeIn: "03:00 PM",
    dayShiftTimeOut: "11:00 PM",
    dayShiftDays: "5 days",
    noonShiftTimeIn: "07:00 AM",
    noonShiftTimeOut: "03:00 PM",
    noonShiftDays: "5 days",
    nightShiftTimeIn: "11:00 PM",
    nightShiftTimeOut: "07:00 AM",
    nightShiftDays: "5 days",
    offDays: "16 days",
  },
];

const ShiftRosterExamples = rosters;

export { ShiftRosterExamples };
