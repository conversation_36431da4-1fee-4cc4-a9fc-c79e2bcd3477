import {
  BabyI<PERSON>,
  BadgeDollarSignIcon,
  CalendarSyncIcon,
  HeartIcon,
  HospitalIcon,
  HouseIcon,
  PillIcon,
} from "lucide-react";
import { BabyCarriageIcon } from "@phosphor-icons/react";

export default function TimesheetLeaveTypeIcon({ leaveType }) {
  switch (leaveType) {
    case "Medical Leave":
      return <PillIcon size={16} />;
    case "Childcare Leave":
    case "Extended Childcare Leave":
      return <BabyIcon size={16} />;
    case "Hospitalization Leave":
      return <HospitalIcon size={16} />;
    case "Replacement Leave":
      return <CalendarSyncIcon size={16} />;
    case "Paternity Leave":
      return <BabyCarriageIcon size={18} />;
    case "Maternity Leave":
      return <BabyCarriageIcon size={18} />;
    case "Unpaid Leave":
      return <BadgeDollarSignIcon size={16} />;
    case "Profamily Leave":
      return <HouseIcon size={16} />;
    case "Family Leave":
      return <HouseIcon size={16} />;
    case "Marriage Leave":
      return <HeartIcon size={16} />;
    default:
      return null;
  }
}
