import cn from "@/lib/cn";
import Card from "@/ui/Card";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import Button from "@/ui/Button";
import { isEmpty } from "lodash";
import { useState } from "react";
import RawHtml from "@/ui/RawHtml";
import { useFormContext } from "react-hook-form";
import { Check, CirclePlusIcon } from "lucide-react";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import XCircleFilledIcon from "@/icons/emp/XCircleFilledIcon";
import AddCircleFilledIcon from "@/icons/emp/AddCircleFilledIcon";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import TimesheetDate from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDate";
import TimesheetTimeOff from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetTimeOff";
import EditTimesheetTimeOff from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/edit/_ui/EditTimesheetTimeOff";
import EditTimesheetTimeSlot from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/edit/_ui/EditTimesheetTimeSlot";
import TimesheetDaySlotCardHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDaySlotCardHeader";

export default function EditTimesheetDaySlotCard({ index, item }) {
  const { isWorkingDay, publicHoliday, leave, leaveType } = item;

  const { watch } = useFormContext();
  const hasOvertime = watch(`timesheetDays.[${index}].hasOvertime`);
  const hasTimeOff = watch(`timesheetDays.[${index}].timeOff`);

  const wrapperClassName = "grid grid-cols-[55px_630px_1fr] gap-4";

  return (
    <Card
      className={cn("p-2 pl-0", {
        "bg-gray-50": !isWorkingDay,
      })}
    >
      <TimesheetDaySlotCardHeader
        edit
        index={index}
        className={wrapperClassName}
      />

      <div className={wrapperClassName}>
        <TimesheetDate index={index} item={item} />

        <VStack className={cn("tabular-num my-auto", { "pt-2": isWorkingDay })}>
          {publicHoliday && (
            <TimesheetTimeOff
              publicHoliday
              label="PH"
              content={publicHoliday}
            />
          )}

          {leave && (
            <TimesheetTimeOff leave label={leave} content="leaveType" />
          )}

          {isWorkingDay && (
            <>
              <EditTimesheetTimeSlot index={index} />
            </>
          )}

          {hasOvertime && (
            <>
              <EditTimesheetTimeSlot overtime index={index} />
            </>
          )}

          {hasTimeOff && <EditTimesheetTimeOff timeOff index={index} />}

          <AddOvertimeAndTimeOffButtons
            index={index}
            isWorkingDay={isWorkingDay}
          />
        </VStack>

        <RemarkPopover index={index} />
      </div>
    </Card>
  );
}

const AddOvertimeAndTimeOffButtons = ({ index, isWorkingDay = true }) => {
  const { getValues, setValue } = useFormContext();
  const hasOvertime = getValues(`timesheetDays.[${index}].hasOvertime`);
  const getTimeOff = getValues(`timesheetDays.[${index}].timeOff`);

  const OutlineButton = ({
    overtime = false,
    timeOff = false,
    activated = false,
    onClick = () => {},
  }) => (
    <button
      className={cn(
        "w-full cursor-pointer rounded-md border border-gray-400 px-2 py-0.5 opacity-50 hover:opacity-100",
        {
          "border-amber-800 bg-amber-50 font-semibold text-amber-900 opacity-100":
            overtime && hasOvertime,
          "border-gray-700 bg-gray-50 font-semibold text-black opacity-80 hover:opacity-80":
            timeOff && getTimeOff,
        },
      )}
      onClick={onClick}
    >
      <HStack>
        {activated ? (
          <Check size={14} strokeWidth={2.5} />
        ) : (
          <CirclePlusIcon size={14} />
        )}
        {overtime && "Overtime"}
        {timeOff && "Time-off"}
      </HStack>
    </button>
  );

  return (
    <HStack className="px-2">
      <div className="w-[24px] text-sm" />

      <div className="text-muted grid w-[375px] grid-cols-2 gap-2 text-xs">
        <OutlineButton
          overtime
          activated={hasOvertime}
          onClick={() => {
            if (hasOvertime) {
              setValue(`timesheetDays.[${index}].overtimeBreak`, null);
            }
            setValue(`timesheetDays.[${index}].hasOvertime`, !hasOvertime);
          }}
        />

        {isWorkingDay && (
          <OutlineButton
            timeOff
            activated={getTimeOff}
            onClick={() => {
              setValue(
                `timesheetDays.[${index}].timeOff`,
                isEmpty(getTimeOff) ? "Full" : null,
              );
            }}
          />
        )}
      </div>
    </HStack>
  );
};

const RemarkPopover = ({ index }) => {
  const [open, setOpen] = useState(false);

  const { watch, setValue } = useFormContext();
  const watchRemark = watch(`timesheetDays.[${index}].remark`);
  const watchRemarkPreview = watch(`timesheetDays.[${index}].remarkPreview`);

  return (
    <HStack
      className={cn("flex-nowrap pt-2", {
        "relatives border-input rounded-md border px-2 py-1 text-left":
          watchRemark,
        "mb-auto": !watchRemark,
      })}
    >
      <div className={cn("h-full w-full", { "pt-2": !watchRemark })}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger>
            {watchRemark ? (
              <button className="line-clamp-2 h-full w-full cursor-pointer text-start">
                <RawHtml>{watchRemark}</RawHtml>
              </button>
            ) : (
              <Button
                variant="plainIcon"
                prefix={<AddCircleFilledIcon color="#9ca3af" />}
              />
            )}
          </PopoverTrigger>
          <PopoverContent align="center">
            <HStack className="min-w-[300px] p-2">
              <div className="flex-1 text-sm">
                <TiptapEditor
                  disableToolbar
                  defaultValue={watchRemark}
                  name={`timesheetDays.[${index}].remarkPreview`}
                />
              </div>

              <Button
                className="px-1 text-sm"
                variant="ghost"
                onClick={() => {
                  setValue(
                    `timesheetDays.[${index}].remark`,
                    watchRemarkPreview,
                  );
                  setOpen(false);
                }}
              >
                Save
              </Button>
            </HStack>
          </PopoverContent>
        </Popover>
      </div>

      {watchRemark && (
        <Button
          variant="plainIcon"
          className="ml-autos w-fit"
          onClick={() => {
            setValue(`timesheetDays.[${index}].remarkPreview`, null);
            setValue(`timesheetDays.[${index}].remark`, null);
          }}
        >
          <XCircleFilledIcon color="#9ca3af" />
        </Button>
      )}
    </HStack>
  );
};
