import TimesheetTime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/TimesheetTime";

export default function NormalTime({ timesheetDay }) {
  const { normalTimeIn, normalTimeOut, normalBreakDuration, normalHours } =
    timesheetDay;
  const noNormalTime = !normalTimeIn && !normalTimeOut;

  if (noNormalTime) return null;

  return (
    <TimesheetTime
      timeIn={normalTimeIn}
      timeOut={normalTimeOut}
      breakDuration={normalBreakDuration}
      totalHours={normalHours}
    />
  );
}
