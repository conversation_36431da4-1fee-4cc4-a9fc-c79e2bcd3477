import ForkSpoonIcon from "@/icons/emp/ForkKnifeIcon";
import MoonFilledIcon from "@/icons/filled/MoonFilledIcon";
import OffFilledIcon from "@/icons/filled/OffFilledIcon";
import SunFilledIcon from "@/icons/filled/SunFilledIcon";
import SunHorizonFilledIcon from "@/icons/filled/SunHorizonFilledIcon";
import cn from "@/lib/cn";
import Card from "@/ui/Card";
import HStack from "@/ui/HStack";
import { List, PropertyList } from "@/ui/PropertyList";
import StatusIndicator from "@/ui/StatusIndicator";
import VStack from "@/ui/VStack";
import { Person } from "@blueprintjs/icons";
import pluralize from "pluralize";

export default function TimesheetInformation({
  shiftRoster = false,
  employment,
  edit,
  totalWorkingHours = 0,
  totalOvertimeHours = 0,
}) {
  return (
    <Card className="max-w-[720px]s mx-auto overflow-hidden overflow-x-scroll bg-white bg-gradient-to-br from-blue-50 via-white via-50% to-white">
      <HStack className="justify-between">
        <PropertyList className="min-w-[340px] pr-4">
          <List label="Status">
            <HStack className="flex justify-end">
              <StatusIndicator status="NEW" />
              NEW
            </HStack>
          </List>
          <List label="Approver">
            <HStack className="justify-end gap-1">
              <div className="text-gray-500">
                <Person size={12} />
              </div>
              <p className="text-sm">Andy McKenzie</p>
            </HStack>
            <HStack className="justify-end gap-1">
              <div className="text-gray-500">
                <Person size={12} />
              </div>
              <p className="text-sm">Sen. Ariane Stracke</p>
            </HStack>
          </List>
          {!shiftRoster && (
            <List label="Working days">
              <p className="text-sm">5 days with half-day Saturdays</p>
            </List>
          )}
          {!shiftRoster && (
            <List label="Working hours">
              <HStack className="justify-end">09:00 AM &mdash; 06:00 PM</HStack>
            </List>
          )}
          <List label="Break">
            <HStack className="justify-end gap-1 text-sm">
              <ForkSpoonIcon />
              <div>
                60 <span className="text-xs">{pluralize("mins", 60)}</span>
              </div>
            </HStack>
          </List>
        </PropertyList>

        {!edit && shiftRoster && (
          <PropertyList
            className="min-w-[340px] pr-4"
            title="Working hours"
            accordion
          >
            <List
              label={
                <HStack className="gap-1">
                  <div className="flex w-[16px] justify-center">
                    <SunFilledIcon color="#d97706" />
                  </div>
                  Day
                </HStack>
              }
              labelClassName="my-auto text-center"
            >
              03:00 PM - 11:00 PM{" "}
              <span className="text-muted my-auto text-center text-xs">
                (5 days)
              </span>
            </List>
            <List
              label={
                <HStack className="gap-1">
                  <div className="flex w-[16px] justify-center">
                    <SunHorizonFilledIcon color="#ea580c" />
                  </div>
                  Noon
                </HStack>
              }
              labelClassName="my-auto text-center"
            >
              07:00 AM - 03:00 PM{" "}
              <span className="text-muted text-center text-xs">(5 days)</span>
            </List>
            <List
              label={
                <HStack className="gap-1">
                  <div className="flex w-[16px] justify-center">
                    <MoonFilledIcon color="#4f46e5" />
                  </div>
                  Night
                </HStack>
              }
              labelClassName="my-auto text-center"
            >
              11:00 PM - 07:00 AM{" "}
              <span className="text-muted text-center text-xs">(5 days)</span>
            </List>
            <List
              label={
                <HStack className="gap-1">
                  <div className="flex w-[16px] justify-center">
                    <OffFilledIcon color="#6b7280" />
                  </div>
                  Off
                </HStack>
              }
            >
              <span className="text-muted text-center text-xs">(16 days)</span>
            </List>
          </PropertyList>
        )}

        <Card
          className={cn(
            "flex flex-1 justify-center bg-gradient-to-tl from-blue-50 via-white via-50% to-white p-2",
          )}
        >
          <VStack className="w-full">
            <p className="text-muteds text-center">Total hrs</p>

            <HStack className="grid grid-cols-2 divide-x">
              <VStack className="gap-0 pr-2">
                <p className="text-muted font-semibolds text-center text-sm">
                  Normal
                </p>
                <span className="text-center">
                  {totalWorkingHours}{" "}
                  <span className="text-muted text-sm">hrs</span>{" "}
                  <span className="text-muted text-center text-xs">
                    (24 days)
                  </span>
                </span>
              </VStack>

              <VStack className="gap-0">
                <p className="text-muted font-semibolds text-center text-sm">
                  Overtime{" "}
                </p>
                <span className="text-center">
                  2 <span className="text-muted text-sm">hrs</span>{" "}
                  <span className="text-muted text-xs">(2 days)</span>
                </span>
              </VStack>
            </HStack>
          </VStack>
        </Card>
      </HStack>
    </Card>
  );
}
