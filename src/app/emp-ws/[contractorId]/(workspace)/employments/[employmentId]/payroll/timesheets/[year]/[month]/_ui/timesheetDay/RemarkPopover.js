import Card from "@/ui/Card";
import { isEmpty } from "lodash";
import { useState } from "react";
import RawHtml from "@/ui/RawHtml";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";

export default function RemarkPopover({ contractorRemark }) {
  const [open, setOpen] = useState(false);

  if (isEmpty(contractorRemark)) return null;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger>
        <Card className="w-[350px] cursor-pointer rounded-md">
          <button className="line-clamp-1 h-full w-full cursor-pointer text-start">
            <RawHtml>{contractorRemark}</RawHtml>
          </button>
        </Card>
      </PopoverTrigger>

      <PopoverContent align="end">
        <div className="p-4">
          <RawHtml>{contractorRemark}</RawHtml>
        </div>
      </PopoverContent>
    </Popover>
  );
}
