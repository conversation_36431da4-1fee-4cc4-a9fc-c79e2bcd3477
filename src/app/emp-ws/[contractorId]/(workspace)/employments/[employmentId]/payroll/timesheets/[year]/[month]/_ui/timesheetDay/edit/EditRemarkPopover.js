import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import { useState } from "react";
import RawHtml from "@/ui/RawHtml";
import { useFormContext, useWatch } from "react-hook-form";
import TiptapEditor from "@/ui/editors/TiptapEditor";
import XCircleFilledIcon from "@/icons/emp/XCircleFilledIcon";
import AddCircleFilledIcon from "@/icons/emp/AddCircleFilledIcon";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import VStack from "@/ui/VStack";
import { validateWorkingHoursMatchWithLeaveTerm } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";
import { useEffect } from "react";
import { isFullDayLeave } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";

export default function EditRemarkPopover({
  index,
  currentLeaveTerm,
  timesheetDay,
}) {
  const { isWorkingDay, leave } = timesheetDay;
  const fullDayLeave = isFullDayLeave(leave);
  const [open, setOpen] = useState(false);

  const {
    control,
    setValue,
    register,
    clearErrors,
    formState: { errors },
  } = useFormContext();

  const contractorRemarkName = `timesheetDays.[${index}].contractorRemark`;
  const remarkPreviewName = `timesheetDays.[${index}].remarkPreview`;

  const watchTimesheetDay = useWatch({
    control,
    name: `timesheetDays.[${index}]`,
  });
  const watchNormaTimeIn = watchTimesheetDay.normalTimeIn;
  const watchNormaTimeOut = watchTimesheetDay.normalTimeOut;
  const watchNormalHours = watchTimesheetDay.normalHours;
  const watchRemark = watchTimesheetDay.contractorRemark;
  const watchRemarkPreview = watchTimesheetDay.remarkPreview;
  const watchTimeOff = watchTimesheetDay.timeOff;

  // If not working day, no need to validate working hours
  const normalTimeIsDefault = validateWorkingHoursMatchWithLeaveTerm(
    currentLeaveTerm,
    timesheetDay,
    watchNormaTimeIn?.value,
    watchNormaTimeOut?.value,
  );

  index === 3 && normalTimeIsDefault;

  const checkRequireRemark = () => {
    if (watchRemark || watchNormalHours > 12) return true;

    if (isWorkingDay) {
      if (fullDayLeave || watchTimeOff || leave) {
        // If time-off is full day, return false, because no normal time in and time out for whole day
        // Thus, no need to validate remark
        // && watchTimeOff === "Full"
        return false;
      } else {
        return !normalTimeIsDefault; // If working hours is not default, must have remark
      }
    }
  };

  const showRemark = checkRequireRemark();

  const remarkError = errors?.timesheetDays?.[index]?.contractorRemark?.message;

  register(contractorRemarkName, {
    required: showRemark && "Remark is required",
  });

  // useEffect(() => {
  //   if (!showRemark) {
  //     console.log("Clear errors!");
  //     clearErrors(contractorRemarkName);
  //   }
  // }, [clearErrors, contractorRemarkName, showRemark]);

  return (
    <HStack
      className={cn("flex-nowrap pt-2", {
        "relatives rounded-md border border-input px-2 py-1 text-left":
          showRemark,
        "mb-auto": !showRemark,
        "border-2 border-red-500": remarkError,
      })}
    >
      <div className={cn("h-full w-full", { "pt-2": !watchRemark })}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger>
            {showRemark ? (
              <button className="line-clamp-2 h-full w-full cursor-pointer text-start">
                <RawHtml>{watchRemark}</RawHtml>
              </button>
            ) : (
              <Button
                variant="plainIcon"
                prefix={<AddCircleFilledIcon color="#9ca3af" />}
              />
            )}
          </PopoverTrigger>
          <PopoverContent align="center">
            <HStack className="min-w-[300px] p-2">
              <div className="flex-1 text-sm">
                <TiptapEditor
                  disableToolbar
                  defaultValue={watchRemark}
                  name={remarkPreviewName}
                />
              </div>

              <Button
                className="px-1 text-sm"
                variant="ghost"
                onClick={() => {
                  setValue(contractorRemarkName, watchRemarkPreview);
                  setOpen(false);
                }}
              >
                Save
              </Button>
            </HStack>
          </PopoverContent>
        </Popover>
      </div>

      {showRemark && (
        <Button
          variant="plainIcon"
          className="ml-autos w-fit"
          onClick={() => {
            setValue(remarkPreviewName, null);
            setValue(contractorRemarkName, null);
          }}
        >
          <XCircleFilledIcon color={remarkError ? "#ef4444" : "#9ca3af"} />
        </Button>
      )}
    </HStack>
  );
}
