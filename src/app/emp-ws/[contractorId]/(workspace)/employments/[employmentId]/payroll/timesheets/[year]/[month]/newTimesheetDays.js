import { eachDayOfInterval, endOfMonth, format, startOfMonth } from "date-fns";
import { toNumber } from "lodash";
import isValidMonth from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/isValidMonth";
import {
  getTotalHours,
  getWorkingDays,
  parsedDateFromMonthYear,
} from "@/lib/timesheet/calculator";

const DAY_ORDER = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

const findLeavesByDate = (leaves, date) => {
  return leaves.find(({ leaveDate }) => leaveDate === date) || null;
};

const getWorkingHour = (workingHour, leave) => {
  // If no working hour, it is non-working day
  // If there is leave, we don't prefill working hour
  if (!workingHour || leave)
    return {
      normalTimeIn: "",
      normalTimeOut: "",
      normalBreakDuration: 0,
    };

  const { normalTimeIn, normalTimeOut, normalBreakDuration } = workingHour;

  return {
    normalTimeIn,
    normalTimeOut,
    normalBreakDuration: toNumber(normalBreakDuration),
  };
};

export default function newTimesheetDays(employment, month, year) {
  if (!isValidMonth(month)) return [];

  const { currentLeaveTerm, leaves } = employment;
  const leaveTermWorkingDays = currentLeaveTerm.workingDays; // 5 / 5.5 / shift
  const leaveTermWorkingHours = currentLeaveTerm?.workingHours;
  const workingDays = getWorkingDays(leaveTermWorkingHours);

  const parsedDate = parsedDateFromMonthYear(year, month);
  const start = startOfMonth(parsedDate);
  const end = endOfMonth(parsedDate);
  const firstDayIndex = DAY_ORDER.indexOf(format(start, "EEE"));

  const data = eachDayOfInterval({ start, end }).map((day, index) => {
    const dayOfWeek = format(day, "EEE");
    const leave = findLeavesByDate(leaves, format(day, "yyyy-MM-dd"));

    const workingHour = workingDays[dayOfWeek];
    const isWorkingDay = workingHour ? true : false;
    const { normalTimeIn, normalTimeOut, normalBreakDuration } = getWorkingHour(
      workingHour,
      leave,
    );

    return {
      index: firstDayIndex + index,
      date: format(day, "dd/MM/yyyy"),
      isWorkingDay,
      dayOfWeek,
      normalTimeIn: normalTimeIn ? { value: normalTimeIn } : "",
      normalTimeOut: normalTimeOut ? { value: normalTimeOut } : "",
      normalBreakDuration,
      normalHours: getTotalHours(
        normalTimeIn,
        normalTimeOut,
        normalBreakDuration,
      ),
      leave,
    };
  });

  return data;
}
