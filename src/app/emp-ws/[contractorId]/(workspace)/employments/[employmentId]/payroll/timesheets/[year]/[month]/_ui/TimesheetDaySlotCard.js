import cn from "@/lib/cn";
import Card from "@/ui/Card";
import VStack from "@/ui/VStack";
import { isEmpty } from "lodash";
import { usePathname } from "next/navigation";
import TimesheetDate from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDate";
import TimesheetTimeOff from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetTimeOff";
import TimesheetTimeSlot from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetTimeSlot";
import TimesheetDaySlotCardHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDaySlotCardHeader";

export default function TimesheetDaySlotCard({ index, item, lastDay }) {
  const {
    isWorkingDay,
    dayOfWeek,
    date,
    halfDay,
    timeIn,
    timeOut,
    break: breakDuration,
    workingHours,
    overtimeTimeIn,
    overtimeTimeOut,
    overtimeBreak,
    overtimeHours,
    timeOff,
    remark,
    publicHoliday,
    leave,
    leaveType,
  } = item;

  const hasOT = !isEmpty(overtimeTimeIn);

  const wrapperClassName = "grid grid-cols-[55px_450px_1fr] gap-4";

  return (
    <Card
      className={cn("rounded-none p-2 pl-0", {
        "bg-gray-50": !isWorkingDay && !hasOT,
        "rounded-t-lg": index === 0,
        "rounded-b-lg": lastDay,
      })}
    >
      <TimesheetDaySlotCardHeader index={index} className={wrapperClassName} />

      <div className={wrapperClassName}>
        <TimesheetDate index={index} item={item} />

        <VStack className="my-auto gap-1">
          {publicHoliday && (
            <TimesheetTimeOff
              publicHoliday
              label="PH"
              content={publicHoliday}
            />
          )}

          {leave && (
            <TimesheetTimeOff leave label={leave} content={leaveType} />
          )}

          {timeOff === "AM" && <TimesheetTimeOff timeOff label={timeOff} />}

          {isWorkingDay && !leave && (
            <TimesheetTimeSlot
              timeIn={timeIn?.value}
              timeOut={timeOut?.value}
              breakDuration={breakDuration}
              totalHours={workingHours}
            />
          )}

          {timeOff === "PM" && <TimesheetTimeOff timeOff label={timeOff} />}

          {overtimeHours && (
            <TimesheetTimeSlot
              overtime
              timeIn={overtimeTimeIn.value}
              timeOut={overtimeTimeOut.value}
              breakDuration={overtimeBreak}
              totalHours={overtimeHours}
            />
          )}
        </VStack>
      </div>
    </Card>
  );
}
