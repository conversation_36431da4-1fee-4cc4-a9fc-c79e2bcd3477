import { formatDateTime } from "@/formatters/date";
import cn from "@/lib/cn";
import Card from "@/ui/Card";
import HStack from "@/ui/HStack";
import StatusIndicator from "@/ui/StatusIndicator";
import VStack from "@/ui/VStack";
import { parse } from "date-fns";
import { UploadIcon } from "lucide-react";
import { useState } from "react";
import { useDropzone } from "react-dropzone";
import FileIcon from "@/ui/file-management/icons/FileIcon";
import { ProgressBarLink, useProgressBarLink } from "@/ui/ProgressBar";
import AlertCard from "@/ui/AlertCard";
import Tooltip from "@/ui/Tooltip";
import { List, PropertyList } from "@/ui/PropertyList";
import { useParams } from "next/navigation";

const MONTHS = [
  {
    id: "TS166647",
    month: "Jan",
    value: "1",
    status: "APPROVED",
    submittedAt: "28/1/2025",
    approvedAt: "30/1/2025",
  },
  {
    id: "TS166648",
    month: "Feb",
    value: "2",
    status: "APPROVED",
    submittedAt: "25/2/2025",
    approvedAt: "26/2/2025",
  },
  {
    id: "TS166678",
    month: "Mar",
    value: "3",
    status: "APPROVED",
    submittedAt: "30/3/2025",
    approvedAt: "30/3/2025",
  },
  {
    id: "TS166679",
    month: "Apr",
    value: "4",
    status: "APPROVED",
    submittedAt: "28/4/2025",
    approvedAt: "29/4/2025",
  },
  { id: "", month: "May", value: "5", status: "NEW", submittedAt: "" },
  { id: "", month: "Jun", value: "6", status: "NEW", submittedAt: "" },
  { id: "", month: "Jul", value: "7", status: "NEW", submittedAt: "" },
  { id: "", month: "Aug", value: "8", status: "NEW", submittedAt: "" },
  { id: "", month: "Sep", value: "9", status: "NEW", submittedAt: "" },
  { id: "", month: "Oct", value: "10", status: "NEW", submittedAt: "" },
  { id: "", month: "Nov", value: "11", status: "NEW", submittedAt: "" },
  { id: "", month: "Dec", value: "12", status: "NEW", submittedAt: "" },
];

export default function TimesheetUploadCard({ baseUrl }) {
  const { month: urlMonth } = useParams();
  const [file, setFile] = useState();
  const [data, setData] = useState(MONTHS);

  const onDrop = async (files) => {
    if (files.length === 0) return;

    setFile(files[0]);
    setData((prevData) =>
      prevData.map((item) => {
        if (item.month === "May") {
          return {
            ...item,
            id: "TS166688",
            status: "APPROVED",
            submittedAt: "22/5/2025",
            approvedAt: "22/5/2025",
          };
        }
        return item;
      }),
    );
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false,
    onDrop,
  });

  const handleProgressBarLink = useProgressBarLink();

  return (
    <div className="space-y-2 p-2">
      <div className="mx-auto max-w-[650px] p-2">
        <AlertCard variant="info">
          If you don&apos;t have the timesheet template, you can download it{" "}
          <ProgressBarLink
            href={`${baseUrl}/files`}
            className="cursor-pointer text-blue-700 underline active:opacity-80"
          >
            here
          </ProgressBarLink>
          .
        </AlertCard>
      </div>

      <div className="gird-cols-1 mx-auto grid max-w-[1000px] gap-1 sm:grid-cols-2 md:grid-cols-3">
        {data.map(
          ({
            id,
            month,
            value,
            status,
            submittedAt,
            approvedAt,
            rejectedAt,
          }) => {
            const selected = urlMonth === month;
            const disabled = value > 5;
            const approved = status === "APPROVED";
            const submitted = status === "SUBMITTED";
            const ableToUpload = (status === "NEW" && !disabled) || submitted;

            return (
              <Card
                key={`${month}_card`}
                className={cn("flex flex-col space-y-4", {
                  "drop-shadow": selected,
                  "text-muted cursor-not-allowed bg-gray-50": disabled,
                })}
              >
                <HStack
                  className={cn("", {
                    "text-green-700": approvedAt,
                    "text-blue-600": submittedAt && !approvedAt,
                    "text-red-600": rejectedAt,
                  })}
                >
                  <HStack className="text-xs">
                    {status !== "NEW" ? (
                      <Tooltip
                        side="bottom"
                        sideOffset={4}
                        content={status}
                        className="bg-black/70 text-xs opacity-60 backdrop-blur-sm"
                      >
                        <StatusIndicator status={status} />
                      </Tooltip>
                    ) : (
                      <StatusIndicator status={status} />
                    )}
                  </HStack>

                  <HStack>
                    <h1 className="text-lg font-semibold">{month}</h1>
                  </HStack>
                </HStack>

                {ableToUpload && !file ? (
                  <div
                    {...getRootProps()}
                    className={cn(
                      "flex-grow cursor-pointer rounded-lg text-sm transition-all",
                      {
                        "bg-gray-100": isDragActive,
                      },
                    )}
                  >
                    <input {...getInputProps()} />
                    <VStack className="text-muted items-center p-2 text-center">
                      <UploadIcon size={20} strokeWidth={1.5} />
                      Drop file here to submit (or click to browse)
                    </VStack>
                  </div>
                ) : (
                  <VStack className="gap-4">
                    {status !== "NEW" && !disabled && (
                      <PropertyList>
                        {id && <List label="ID">{id}</List>}
                        <List label="Submitted">
                          {submittedAt &&
                            formatDateTime(
                              parse(submittedAt, "d/M/yyyy", new Date()),
                            )}
                        </List>
                      </PropertyList>
                    )}

                    {approved && (
                      <HStack className="cursor-pointer rounded-md bg-gray-100 p-2 active:opacity-80">
                        <FileIcon type="application/vnd.ms-excel" />
                        <div className="max-w-[280px] text-sm md:max-w-[220px]">
                          <p className="truncate text-ellipsis whitespace-nowrap">
                            Timesheet_{id}.xlsx
                          </p>

                          <p className="text-muted">54 KB</p>
                        </div>
                      </HStack>
                    )}

                    {ableToUpload && file && (
                      <div className="grid grid-cols-3 gap-1 text-sm">
                        <div className="col-span-2 flex gap-2 rounded-l-md bg-gray-100 p-2">
                          <div className="my-auto">
                            <FileIcon type={file.type} />
                          </div>

                          <VStack className="gap-0 overflow-hidden">
                            <p className="w-full overflow-hidden text-sm text-ellipsis whitespace-nowrap">
                              {file.name}
                            </p>
                            <p className="text-muted text-xs">
                              {renderFileSize(file.size)}
                            </p>
                          </VStack>
                        </div>

                        <div
                          {...getRootProps()}
                          className={cn(
                            "relative my-auto h-full w-full rounded-r-lg bg-gray-100 transition-all hover:bg-gray-200",
                            { "bg-gray-200": isDragActive },
                          )}
                        >
                          <input {...getInputProps()} />
                          <VStack className="text-muted flex h-full w-full flex-1 items-center justify-center gap-1 text-xs">
                            <UploadIcon size={16} strokeWidth={1.5} />
                            Replace
                          </VStack>
                        </div>
                      </div>
                    )}
                  </VStack>
                )}
              </Card>
            );
          },
        )}
      </div>
    </div>
  );
}

const renderFileSize = (size) => {
  if (size < 1024) {
    // B
    return `${size} B`;
  } else if (size >= 1024 && size < 900000) {
    // KB
    return `${parseFloat((size / 1000).toFixed(0))} KB`;
  } else if (size >= 900000 && size < 900000000) {
    // MB
    return `${parseFloat((size / 1000000).toFixed(1))} MB`;
  } else {
    // GB
    return `${parseFloat((size / 1000000000).toFixed(1))} GB`;
  }
};
