"use client";

import Button from "@/ui/Button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import { Ellipsis, PlusIcon } from "lucide-react";
import { useParams } from "next/navigation";

export default function ClaimActionMenu() {
  const { srid: employmentId } = useParams();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button
          variant="plainIcon"
          className="gap-0"
          prefix={<Ellipsis size={24} />}
        />
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuItem href={`/SR/${employmentId}/work-passes/new`}>
          <PlusIcon color="green" size={20} />
          New work pass
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
