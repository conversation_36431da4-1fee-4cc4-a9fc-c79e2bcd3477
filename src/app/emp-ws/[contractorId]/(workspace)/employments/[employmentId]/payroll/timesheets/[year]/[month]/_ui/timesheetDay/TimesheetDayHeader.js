import cn from "@/lib/cn";
import HStack from "@/ui/HStack";

export default function TimesheetDayHeader({
  index,
  className,
  hasRemark = false,
  shift = false,
}) {
  if (index !== 0) return null;

  return (
    <div className={cn("text-muted pb-4 text-sm", className)}>
      <div className="pl-3">Date</div>

      <HStack>
        <HStack className="px-2">
          <div className="w-[24px]" />
          <div className="w-[200px]">Working hours</div>
        </HStack>

        <div className="w-[80px] text-left">Break</div>

        <div className="w-[80px] text-left">Total hrs</div>
      </HStack>

      {shift && <div className="pl-4">Shift</div>}

      {hasRemark && <div>Remark</div>}
    </div>
  );
}
