"use client";

import cn from "@/lib/cn";
import VStack from "@/ui/VStack";
import { format } from "date-fns";
import { useState } from "react";
import { find, map, sumBy } from "lodash";
import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { Form } from "@/ui/Form";
import TimesheetHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetHeader";
import TimesheetShiftRosterHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/shift-roster/TimesheetShiftRosterHeader";
import TimesheetInformation from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetInformation";
import TimesheetDaySlotCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDaySlotCard";
import EditTimesheetDaySlotCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/edit/_ui/EditTimesheetDaySlotCard";
import { useRouter } from "next/navigation";
import { getDatesOfMonth } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/getDatesOfMonth";
import workingHoursCalculator from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/workingHoursCalculator";
import TimesheetUploadCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetUploadCard";
import HStack from "@/ui/HStack";
import Card from "@/ui/Card";
import { List, PropertyList } from "@/ui/PropertyList";
import { SunIcon } from "@phosphor-icons/react/dist/ssr";
import { MoonIcon, SunHorizonIcon } from "@phosphor-icons/react";
import Button from "@/ui/Button";
import { CirclePlusIcon } from "lucide-react";
import ShiftRosterCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/shift-roster/ShiftRosterCard";
import { ShiftRosterExamples } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/shift-roster/ShiftRosterExamples";
import TeamShiftCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/shift-roster/_ui/TeamShiftCard";
import ShiftDaySlotCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/shift-roster/_ui/ShiftDaySlotCard";

export default function Roster({ employment, edit = false }) {
  const {
    contractorId,
    employmentId,
    year: urlYear,
    month: urlMonth,
  } = useParams();
  const month = urlMonth || format(new Date(), "MMM");
  const baseUrl = `/emp-ws/${contractorId}/employments/${employmentId}/payroll/timesheets/${urlYear}${urlMonth}/shift-roster`;

  const datesOfMonth = getDatesOfMonth(month);
  const [formData, setFormData] = useState(datesOfMonth);
  const [selectedTeam, setSelectedTeam] = useState();

  const form = useForm({});
  const { watch } = form;

  const onSubmit = () => {};

  console.log(formData);

  if (edit) {
    return (
      <Form form={form} onSubmit={onSubmit} formData={formData}>
        <TimesheetHeader edit={edit} month={month} />

        <div className="divide-xs grid grid-cols-3 justify-center-safe gap-2 p-4">
          <VStack className="">
            {ShiftRosterExamples.map((roster, index) => {
              return (
                <TeamShiftCard
                  key={`roster_team_${index}`}
                  month={month}
                  roster={roster}
                  selectedTeam={selectedTeam}
                  setSelectedTeam={setSelectedTeam}
                  setFormData={setFormData}
                />
              );
            })}

            <Card
              className="w-full cursor-pointer space-y-4 rounded-md border-0 active:opacity-50"
              onClick={() => {
                setSelectedTeam(null);
              }}
            >
              <div className="my-auto flex h-full items-center justify-center">
                <Button
                  variant="plainIcon"
                  className="text-muted"
                  prefix={<CirclePlusIcon />}
                />
              </div>
            </Card>
          </VStack>

          <div className="col-span-2 space-y-2">
            <TimesheetInformation
              shiftRoster
              edit={edit}
              employment={employment}
              totalWorkingHours={"196"}
              totalOvertimeHours={"2"}
            />

            <VStack
              className={cn(
                "mx-autos w-full max-w-[550px] gap-1 overflow-hidden overflow-x-scroll",
                { "max-w-[900px]": true },
              )}
            >
              {formData.map((item, index) => {
                const lastDay = datesOfMonth.length === index + 1;

                return (
                  <ShiftDaySlotCard
                    key={`date-${item.index}`}
                    index={index}
                    item={item}
                    lastDay={lastDay}
                  />
                );
              })}
            </VStack>
          </div>
        </div>
      </Form>
    );
  }

  return (
    <Form form={form} onSubmit={onSubmit} formData={formData}>
      <TimesheetShiftRosterHeader edit={edit} month={month} />

      <div className="space-y-2 p-4 pt-2">
        <TimesheetInformation
          shiftRoster
          employment={employment}
          totalWorkingHours={totalWorkingHours}
          totalOvertimeHours={totalOvertimeHours}
        />
        {/* <div className="mx-auto flex snap-x snap-mandatory gap-2 overflow-x-auto overflow-y-hidden scroll-smooth px-1"></div> */}

        <VStack
          className={cn(
            "mx-auto w-full max-w-[550px] gap-1 overflow-hidden overflow-x-scroll",
            { "max-w-[900px]": true },
          )}
        >
          {datesOfMonth.map((item, index) => {
            return edit ? (
              <EditTimesheetDaySlotCard
                key={`date-${item.index}`}
                index={index}
                item={item}
              />
            ) : (
              <TimesheetDaySlotCard
                key={`date-${item.index}`}
                index={index}
                item={item}
              />
            );
          })}
        </VStack>
      </div>
    </Form>
  );
}
