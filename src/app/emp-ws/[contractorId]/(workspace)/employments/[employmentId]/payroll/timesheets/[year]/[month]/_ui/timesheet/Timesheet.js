"use client";

import cn from "@/lib/cn";
import VStack from "@/ui/VStack";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { isEmpty } from "lodash";
import TimesheetHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheet/TimesheetHeader";
import EditTimesheet from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheet/EditTimesheet";
import TimesheetDay from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/TimesheetDay";

export default function Timesheet({ employment, timesheet }) {
  const router = useRouter();
  const {
    contractorId,
    employmentId,
    year: urlYear,
    month: urlMonth,
  } = useParams();
  const baseUrl = `/emp-ws/${contractorId}/employments/${employmentId}/payroll/timesheets/${urlYear}/${urlMonth}`;

  if (isEmpty(timesheet) || timesheet.status === "DRAFT")
    return <EditTimesheet employment={employment} timesheet={timesheet} />;

  const { timesheetDays } = timesheet;
  const hasRemark = !isEmpty(
    timesheet.timesheetDays.find(({ contractorRemark }) => contractorRemark),
  );

  return (
    <>
      <TimesheetHeader employment={employment} timesheet={timesheet} />

      <div className="space-y-2 p-4 pt-2">
        <VStack
          className={cn(
            "mx-auto max-w-[550px] gap-1 overflow-hidden overflow-x-scroll",
            { "max-w-[900px]": hasRemark },
          )}
        >
          {timesheetDays.map((timesheetDay, index) => {
            const className = cn("grid grid-cols-[55px_1fr] gap-4", {
              "grid-cols-[55px_450px_1fr]": hasRemark,
            });

            return (
              <TimesheetDay
                key={`${index}_${timesheetDay.id}`}
                index={index}
                className={className}
                timesheetDay={timesheetDay}
              />
            );
          })}
        </VStack>
      </div>
    </>
  );
}
