import TimesheetTime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/TimesheetTime";

export default function Overtime({ timesheetDay }) {
  const {
    overtimeTimeIn,
    overtimeTimeOut,
    overtimeBreakDuration,
    overtimeHours,
  } = timesheetDay;
  const noOverTime = !overtimeTimeIn && !overtimeTimeOut;

  if (noOverTime) return null;

  return (
    <TimesheetTime
      overtime
      timeIn={overtimeTimeIn}
      timeOut={overtimeTimeOut}
      breakDuration={overtimeBreakDuration}
      totalHours={overtimeHours}
    />
  );
}
