import Card from "@/ui/Card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import Button from "@/ui/Button";
import {
  Wrench,
  ChevronDown,
  FileTypeIcon,
  CirclePlusIcon,
} from "lucide-react";
import { useState } from "react";
import { useParams } from "next/navigation";
import { Popover, PopoverTrigger, PopoverContent } from "@/ui/Popover";
import HStack from "@/ui/HStack";
import ShiftRosterCard from "./ShiftRosterCard";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
  DialogFooterWithCTA,
} from "@/ui/Dialog";
import { getDatesOfMonth } from "../../getDatesOfMonth";
import cn from "@/lib/cn";
import { ShiftRosterExamples } from "./ShiftRosterExamples";
import Tooltip from "@/ui/Tooltip";
import { SunIcon, MoonIcon, SunHorizonIcon } from "@phosphor-icons/react";
import VStack from "@/ui/VStack";
import { parse, startOfMonth, startOfWeek, getDay, format } from "date-fns";
import SunFilledIcon from "@/icons/filled/SunFilledIcon";
import SunHorizonFilledIcon from "@/icons/filled/SunHorizonFilledIcon";
import MoonFilledIcon from "@/icons/filled/MoonFilledIcon";
import { find, floor, includes, parseInt } from "lodash";
import { List, PropertyList } from "@/ui/PropertyList";
import Switch from "@/ui/Switch";
import pluralize from "pluralize";
import OffFilledIcon from "@/icons/filled/OffFilledIcon";
import EventCalendarView from "./EventCalendarView";

const getShift = (selectedShift, date) => {
  if (selectedShift?.dayShift?.includes(date)) {
    return "Day";
  } else if (selectedShift?.noonShift?.includes(date)) {
    return "Noon";
  } else if (selectedShift?.nightShift?.includes(date)) {
    return "Night";
  } else if (selectedShift?.off?.includes(date)) {
    return "Off";
  } else {
    return "";
  }
};

export default function ShiftRostersDialog({ month }) {
  const [open, setOpen] = useState(true);
  const [selectedShift, setSelectedShift] = useState(ShiftRosterExamples[0]);
  const [showPH, setShowPH] = useState(false);
  const [showLeaves, setShowLeaves] = useState(false);

  const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
  const parsedDate = parse(month, "MM", new Date());
  // const firstDay = startOfMonth(parsedDate).getDay();
  // Adjust so Monday = 0, Sunday = 6
  // const leadingBlanks = firstDay === 0 ? 6 : firstDay - 1;

  const datesOfMonth = getDatesOfMonth(month);
  const firstDay = datesOfMonth[0];
  const publicHolidaysCount = datesOfMonth.filter(
    ({ publicHoliday }) => publicHoliday,
  ).length;
  const leavesCount = datesOfMonth.filter(({ leaveType }) => leaveType).length;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant="secondary" outline suffix={<ChevronDown size={20} />}>
          Choose a roster
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-7xl">
        <DialogHeader>Choose a roster</DialogHeader>

        <div className="space-y-2 p-4 md:grid md:grid-cols-3 md:divide-x">
          <div className="flex gap-2 overflow-hidden overflow-x-scroll border-b pb-4 md:block md:space-y-2 md:overflow-y-scroll md:border-b-0 md:pr-4">
            {ShiftRosterExamples.map((roster, index) => {
              return (
                <ShiftRosterCard
                  key={`roster_team_${index}`}
                  roster={roster}
                  selectedShift={selectedShift}
                  setSelectedShift={setSelectedShift}
                />
              );
            })}
            <Card
              className="w-full cursor-pointer space-y-4 active:opacity-50"
              onClick={() => {
                setSelectedShift(null);
              }}
            >
              <div className="my-auto flex h-full items-center justify-center">
                <Button
                  variant="plainIcon"
                  className="text-muted"
                  prefix={<CirclePlusIcon />}
                />
              </div>
            </Card>
          </div>

          <div className="col-span-2">
            <div className="sticky top-[85px]">
              <HStack className="mb-auto justify-end gap-4 pb-4">
                <Switch
                  size="sm"
                  suffix="PH"
                  onChange={(e) => {
                    setShowPH(e.target.checked);
                  }}
                />
              </HStack>

              {selectedShift && (
                <EventCalendarView
                  datesOfMonth={datesOfMonth}
                  selectedShift={selectedShift}
                />
              )}
            </div>
          </div>
        </div>

        <DialogFooterWithCTA
          // onDismiss={() => router.refresh()}
          label="Plot"
        />
      </DialogContent>
    </Dialog>
  );
}
const Icon = ({ shift }) => {
  switch (shift) {
    case "Day":
    case "dayShift":
      return <SunFilledIcon color="#d97706" />;
    case "Noon":
    case "noonShift":
      return <SunHorizonFilledIcon color="#ea580c" />;
    case "Night":
    case "nightShift":
      return <MoonFilledIcon color="#4f46e5" />;
    case "Off":
    case "off":
      return <OffFilledIcon color="#6b7280" />;
    case "PH":
      return "PH";
    case "PH":
      return "PH";
    default:
      return null;
  }
};

const PropertyListLabel = ({ shift }) => (
  <HStack className={cn("tabular-numx gap-1 text-sm select-none")}>
    <div
      className={cn("rounded-sm p-2", {
        "bg-sky-100 text-sky-900": ["PH", "Leave"].includes(shift),
        "bg-yellow-100 text-yellow-900": shift === "Day",
        "bg-orange-100 text-orange-900": shift === "Noon",
        "bg-indigo-100 text-indigo-900": shift === "Night",
        "text-muted border-input bg-gray-200": shift === "Off",
      })}
    >
      <p className="invisible"></p>
    </div>
    <div>
      <Icon shift={shift} />
    </div>
  </HStack>
);

const PropertyListContent = ({ shift, selectedShift, publicHolidaysCount }) => {
  const {
    team,
    dayShiftWorkingHours,
    dayShiftDays,
    noonShiftWorkingHours,
    noonShiftDays,
    nightShiftWorkingHours,
    nightShiftDays,
    offDays,
  } = selectedShift || {};

  switch (shift) {
    case "Day":
      return (
        <>
          {dayShiftWorkingHours}{" "}
          {dayShiftDays && (
            <span className="text-muted my-auto text-center text-xs">
              ({dayShiftDays})
            </span>
          )}
        </>
      );
    case "Noon":
      return (
        <>
          {noonShiftWorkingHours}{" "}
          {noonShiftDays && (
            <span className="text-muted my-auto text-center text-xs">
              ({noonShiftDays})
            </span>
          )}
        </>
      );
    case "Night":
      return (
        <>
          {nightShiftWorkingHours}{" "}
          {nightShiftDays && (
            <span className="text-muted my-auto text-center text-xs">
              ({nightShiftDays})
            </span>
          )}
        </>
      );
    case "Off":
      return (
        <>
          {offDays && (
            <span className="text-muteds my-auto text-center text-xs">
              {offDays}
            </span>
          )}
        </>
      );
    case "PH":
      return (
        <>
          {publicHolidaysCount && (
            <span className="text-muteds my-auto text-center text-xs">
              {pluralize("day", publicHolidaysCount, true)}
            </span>
          )}
        </>
      );
    default:
      return null;
  }
};

const ShiftColors = ({ selectedShift, publicHolidaysCount }) => {
  return selectedShift ? (
    <PropertyList>
      {["Day", "Noon", "Night", "Off"].map((shift, index) => {
        return (
          <List
            key={`shift_${index}`}
            label={<PropertyListLabel shift={shift} />}
            labelClassName="my-auto text-center tabular-num"
          >
            <PropertyListContent shift={shift} selectedShift={selectedShift} />
          </List>
        );
      })}
    </PropertyList>
  ) : (
    <HStack className="justify-center gap-4 py-2">
      {["Day", "Noon", "Night", "Off", "PH"].map((shift, index) => {
        const Icon = () => {
          switch (shift) {
            case "Day":
              return <SunFilledIcon color="#78350f" />;
            case "Noon":
              return <SunHorizonFilledIcon color="#7c2d12" />;
            case "Night":
              return <MoonFilledIcon color="#312e81" />;
            case "Off":
              return <OffFilledIcon color="#6b7280" />;
            case "PH":
              return "PH";
            default:
              return null;
          }
        };

        return (
          <HStack
            key={index}
            className={cn("tabular-numx gap-1 text-sm select-none")}
          >
            <div
              className={cn("borders rounded-sm p-2", {
                "border-sky-500 bg-sky-50 text-sky-900": shift === "PH",
                "border-yellow-500 bg-yellow-100 text-yellow-900":
                  shift === "Day",
                "border-orange-500 bg-orange-100 text-orange-900":
                  shift === "Noon",
                "border-indigo-500 bg-indigo-100 text-indigo-900":
                  shift === "Night",
                "text-muted border-input bg-gray-50": shift === "Off",
              })}
            >
              <p className="invisible"></p>
            </div>
            <div>
              <Icon />
            </div>
          </HStack>
        );
      })}
    </HStack>
  );
};

// {
//   Array.from({ length: leadingBlanks }).map((_, idx) => (
//     <div key={`blank-${idx}`} />
//   ));
// }

// {
//   datesOfMonth.map((dateOfMonth, index) => {
//     const { date, isWorkingDay, publicHoliday, dayOfWeek, leave, leaveType } =
//       dateOfMonth;
//     const dayShiftDates = selectedShift?.dayShift || [];
//     const noonShiftDates = selectedShift?.noonShift || [];
//     const nightShiftDates = selectedShift?.nightShift || [];
//     const offDates = selectedShift?.off || [];

//     const dayShift = dayShiftDates.includes(date);
//     const noonShift = noonShiftDates.includes(date);
//     const nightShift = nightShiftDates.includes(date);
//     const off = offDates.includes(date);

//     const hasEvents =
//       publicHoliday || dayShift || noonShift || nightShift || off;

//     const shifts = ["dayShift", "noonShift", "nightShift", "off"];

//     const currentShift = selectedShift
//       ? find(shifts, (shift) => selectedShift[shift].includes(date))
//       : "";
//     const prevDate = (date - 1).toString().padStart(2, "0");
//     const nextDate = (date + 1).toString().padStart(2, "0");
//     const prevShift =
//       selectedShift &&
//       find(shifts, (shift) => selectedShift[shift].includes(prevDate)) ===
//         currentShift;
//     const nextShift =
//       selectedShift &&
//       find(shifts, (shift) => selectedShift[shift].includes(nextDate)) ===
//         currentShift;

//     return (
//       <div
//         key={date}
//         disabled={!isWorkingDay}
//         className={cn(
//           "tabular-num borders space-y-1 rounded-md border p-2 text-sm select-none",
//           {
//             "border-yellow-500 bg-yellow-100 text-yellow-900": dayShift,
//             "border-orange-500 bg-orange-50 text-orange-900": noonShift,
//             "border-indigo-500 bg-indigo-100 text-indigo-900": nightShift,
//             "border-inputs bg-gray-50": off,
//             // "bg-[linear-gradient(135deg,_transparent_50%,_white_50%)]":
//             //   halfDay,
//             // "cursor-pointer active:opacity-50": isWorkingDay,
//             // "cursor-not-allowed bg-gray-50": !isWorkingDay,
//           },
//         )}
//       >
//         <p>{date}</p>
//         <HStack className="mb-auto w-full justify-between">
//           {dayShift && (
//             <HStack className="borders items-center rounded-md border-yellow-500 bg-yellow-100 py-0.5 text-xs text-yellow-900">
//               <SunFilledIcon color="#78350f" />
//               <p>Day</p>
//             </HStack>
//           )}
//           {noonShift && (
//             <HStack className="borders items-center rounded-md border-orange-500 bg-orange-50 py-0.5 text-xs text-orange-900">
//               <SunHorizonFilledIcon color="#7c2d12" />
//               <p>Noon</p>
//             </HStack>
//           )}
//           {nightShift && (
//             <HStack className="borders items-center rounded-md border-indigo-500 bg-indigo-100 py-0.5 text-xs text-indigo-900">
//               <MoonFilledIcon color="#312e81" />
//               <p>Night</p>
//             </HStack>
//           )}
//           {(publicHoliday || leaveType) && (
//             <div className="w-full rounded-md border border-sky-500 bg-sky-50 px-1 py-0.5 text-xs text-sky-900">
//               {publicHoliday || leaveType}
//             </div>
//           )}
//         </HStack>
//         {/* {hasEvents && (
//                         <VStack className="gap-1">
//                           {dayShift && (
//                             <HStack className="items-center rounded-md border border-yellow-500 bg-yellow-100 px-1 py-0.5 text-xs text-yellow-900">
//                               <SunFilledIcon color="#78350f" />
//                               <p>Day</p>
//                             </HStack>
//                           )}
//                           {noonShift && (
//                             <HStack className="items-center rounded-md border border-orange-500 bg-orange-50 px-1 py-0.5 text-xs text-orange-900">
//                               <SunHorizonFilledIcon color="#7c2d12" />
//                               <p>Noon</p>
//                             </HStack>
//                           )}
//                           {nightShift && (
//                             <HStack className="items-center rounded-md border border-indigo-500 bg-indigo-100 px-1 py-0.5 text-xs text-indigo-900">
//                               <MoonFilledIcon color="#312e81" />
//                               <p>Night</p>
//                             </HStack>
//                           )}
//                         </VStack>
//                       )} */}
//       </div>
//     );
//   });
// }
