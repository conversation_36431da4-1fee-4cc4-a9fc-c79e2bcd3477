import { differenceInMinutes, parse } from "date-fns";

export default function workingHoursCalculator(
  timeIn,
  timeOut,
  breakMinutes = 0,
) {
  const format = "hh:mm a";
  const baseDate = new Date();

  const fromDate = parse(timeIn, format, baseDate);
  const toDate = parse(timeOut, format, baseDate);

  const totalMinutes = differenceInMinutes(toDate, fromDate);
  const netMinutes = totalMinutes - breakMinutes;
  const workingHours = (netMinutes / 60).toFixed(2);

  return parseFloat(workingHours);
}
