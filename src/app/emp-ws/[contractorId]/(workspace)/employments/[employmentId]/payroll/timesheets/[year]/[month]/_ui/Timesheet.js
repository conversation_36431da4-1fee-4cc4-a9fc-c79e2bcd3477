"use client";

import cn from "@/lib/cn";
import VStack from "@/ui/VStack";
import { format } from "date-fns";
import { useState } from "react";
import { find, map, sumBy } from "lodash";
import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { Form } from "@/ui/Form";
import TimesheetHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetHeader";
import TimesheetShiftRosterHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/shift-roster/TimesheetShiftRosterHeader";
import TimesheetInformation from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetInformation";
import TimesheetDaySlotCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDaySlotCard";
import EditTimesheetDaySlotCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/edit/_ui/EditTimesheetDaySlotCard";
import { useRouter } from "next/navigation";
import { getDatesOfMonth } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/getDatesOfMonth";
import workingHoursCalculator from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/workingHoursCalculator";
import TimesheetUploadCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetUploadCard";
import HStack from "@/ui/HStack";
import Card from "@/ui/Card";
import { List, PropertyList } from "@/ui/PropertyList";
import { SunIcon } from "@phosphor-icons/react/dist/ssr";
import { MoonIcon, SunHorizonIcon } from "@phosphor-icons/react";
import RosterTeamsPopover from "./shift-roster/ShiftRostersDialog";
import ShiftRosterCard from "./shift-roster/ShiftRosterCard";
import { ShiftRosterExamples } from "./shift-roster/ShiftRosterExamples";
import Button from "@/ui/Button";
import { CirclePlusIcon } from "lucide-react";

const ROSTERS = [
  {
    team: "A",
    dayShift: "03:00 PM - 11:00 PM",
    dayShiftDays: "5 days",
    noonShift: "07:00 AM - 03:00 PM",
    noonShiftDays: "5 days",
    nightShift: "11:00 PM - 07:00 AM",
    nightShiftDays: "5 days",
    offDays: "16 days",
  },
  {
    team: "B",
    dayShift: "03:00 PM - 11:00 PM",
    dayShiftDays: "5 days",
    noonShift: "07:00 AM - 03:00 PM",
    noonShiftDays: "5 days",
    nightShift: "11:00 PM - 07:00 AM",
    nightShiftDays: "6 days",
    offDays: "15 days",
  },
  {
    team: "C",
    dayShift: "03:00 PM - 11:00 PM",
    dayShiftDays: "6 days",
    noonShift: "07:00 AM - 03:00 PM",
    noonShiftDays: "5 days",
    nightShift: "11:00 PM - 07:00 AM",
    nightShiftDays: "5 days",
    offDays: "15 days",
  },
  {
    team: "D",
    dayShift: "03:00 PM - 11:00 PM",
    dayShiftDays: "5 days",
    noonShift: "07:00 AM - 03:00 PM",
    noonShiftDays: "5 days",
    nightShift: "11:00 PM - 07:00 AM",
    nightShiftDays: "5 days",
    offDays: "16 days",
  },
];

export default function Timesheet({
  employment,
  shiftRoster = false,
  uploadExcel = false,
  edit = false,
}) {
  const router = useRouter();
  const { contractorId, employmentId, month: urlMonth } = useParams();
  const month = urlMonth || format(new Date(), "MMM");
  const baseUrl = `/emp-ws/${contractorId}/employments/${employmentId}`;

  const datesOfMonth = getDatesOfMonth(month);
  const [data, setData] = useState(datesOfMonth);

  const [formData, setFormData] = useState(null);
  const [selectedShift, setSelectedShift] = useState(ShiftRosterExamples[0]);

  const form = useForm({
    defaultValues: {
      timesheetDays: datesOfMonth.map(
        ({ date, dayOfWeek, isWorkingDay, halfDay }) => {
          if (isWorkingDay) {
            return {
              date,
              timeIn: { value: "10:00 AM" },
              timeOut: { value: halfDay ? "01:00 PM" : "06:00 PM" },
              break: halfDay ? null : 60,
              workingHours: halfDay ? 4 : 8,
            };
          } else {
            return {
              date,
              timeIn: "",
              timeOut: "",
              break: null,
              workingHours: 0,
            };
          }
        },
      ),
    },
  });

  const { watch } = form;

  const totalWorkingHours = edit
    ? sumBy(map(watch("timesheetDays"), "workingHours"))
    : "196";
  const totalOvertimeHours = edit
    ? sumBy(map(watch("timesheetDays"), "overtimeHours"))
    : "2";

  const onSubmit = ({ timesheetDays }) => {
    const updatedList = monthDatesList.map((item) => {
      const findDate = find(timesheetDays, { date: item.date });
      const {
        timeIn,
        timeOut,
        break: breakDuration,
        hasOvertime,
        overtimeTimeIn,
        overtimeTimeOut,
        remark,
        timeOff,
        hasTimeOff,
      } = findDate;

      if (item.isWorkingDay) {
        return {
          ...item,
          timeIn,
          timeOut,
          break: breakDuration,
          workingHours: workingHoursCalculator(timeIn, timeOut, breakDuration),
          overtimeTimeIn: hasOvertime && overtimeTimeIn,
          overtimeTimeOut: hasOvertime && overtimeTimeOut,
          overtimeHours:
            hasOvertime &&
            workingHoursCalculator(overtimeTimeIn, overtimeTimeOut),
          timeOff: hasTimeOff && timeOff,
          remark,
        };
      }
    });
    console.log(updatedList);
    setFormData(updatedList);
    // router.push(formData);
  };

  if (shiftRoster) {
    if (edit) {
      return (
        <Form form={form} onSubmit={onSubmit} formData={formData}>
          <TimesheetHeader edit={edit} month={month} />

          <div className="divide-xs grid grid-cols-3 justify-center-safe gap-2 p-4">
            <VStack className="">
              {ShiftRosterExamples.map((roster, index) => {
                return (
                  <ShiftRosterCard
                    key={`roster_team_${index}`}
                    month={month}
                    roster={roster}
                    selectedShift={selectedShift}
                    setSelectedShift={setSelectedShift}
                    setData={setData}
                  />
                );
              })}

              <Card
                className="w-full cursor-pointer space-y-4 rounded-md border-0 active:opacity-50"
                onClick={() => {
                  setSelectedShift(null);
                }}
              >
                <div className="my-auto flex h-full items-center justify-center">
                  <Button
                    variant="plainIcon"
                    className="text-muted"
                    prefix={<CirclePlusIcon />}
                  />
                </div>
              </Card>
            </VStack>

            <div className="col-span-2 space-y-2">
              <TimesheetInformation
                shiftRoster
                edit={edit}
                employment={employment}
                totalWorkingHours={totalWorkingHours}
                totalOvertimeHours={totalOvertimeHours}
              />

              <VStack
                className={cn(
                  "mx-autos w-full max-w-[550px] gap-1 overflow-hidden overflow-x-scroll",
                  { "max-w-[900px]": true },
                )}
              >
                {data.map((item, index) => {
                  const lastDay = datesOfMonth.length === index + 1;

                  return (
                    <TimesheetDaySlotCard
                      key={`date-${item.index}`}
                      index={index}
                      item={item}
                      lastDay={lastDay}
                    />
                  );
                })}
              </VStack>
            </div>
          </div>
        </Form>
      );
    }

    return (
      <Form form={form} onSubmit={onSubmit} formData={formData}>
        <TimesheetShiftRosterHeader edit={edit} month={month} />

        <div className="space-y-2 p-4 pt-2">
          <TimesheetInformation
            shiftRoster
            employment={employment}
            totalWorkingHours={totalWorkingHours}
            totalOvertimeHours={totalOvertimeHours}
          />
          {/* <div className="mx-auto flex snap-x snap-mandatory gap-2 overflow-x-auto overflow-y-hidden scroll-smooth px-1"></div> */}

          <VStack
            className={cn(
              "mx-auto w-full max-w-[550px] gap-1 overflow-hidden overflow-x-scroll",
              { "max-w-[900px]": true },
            )}
          >
            {datesOfMonth.map((item, index) => {
              return edit ? (
                <EditTimesheetDaySlotCard
                  key={`date-${item.index}`}
                  index={index}
                  item={item}
                />
              ) : (
                <TimesheetDaySlotCard
                  key={`date-${item.index}`}
                  index={index}
                  item={item}
                />
              );
            })}
          </VStack>
        </div>
      </Form>
    );
  }

  if (uploadExcel) {
    return (
      <div>
        <VStack>
          <TimesheetUploadCard baseUrl={baseUrl} />
        </VStack>
      </div>
    );
  }

  return (
    <Form form={form} onSubmit={onSubmit} formData={formData}>
      <TimesheetHeader edit={edit} month={month} />

      <div className="space-y-2 p-4 pt-2">
        <TimesheetInformation
          edit={edit}
          employment={employment}
          totalWorkingHours={totalWorkingHours}
          totalOvertimeHours={totalOvertimeHours}
        />

        <VStack
          className={cn(
            "mx-auto w-full max-w-[550px] gap-1 overflow-hidden overflow-x-scroll",
            { "max-w-[900px]": true },
          )}
        >
          {datesOfMonth.map((item, index) => {
            return edit ? (
              <EditTimesheetDaySlotCard
                key={`date-${item.index}`}
                index={index}
                item={item}
              />
            ) : (
              <TimesheetDaySlotCard
                key={`date-${item.index}`}
                index={index}
                item={item}
              />
            );
          })}
        </VStack>
      </div>
    </Form>
  );
}
