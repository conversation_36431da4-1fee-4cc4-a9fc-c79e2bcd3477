import { redirect } from "next/navigation";
import { format, getYear } from "date-fns";

export default async function Home({ params }) {
  const { employmentId, contractorId } = await params;
  const today = new Date();
  const month = format(today, "MM");
  const year = getYear(today);

  const claimUrl = `/emp-ws/${contractorId}/employments/${employmentId}/payroll/claims/${year}/${month}`;

  redirect(claimUrl);
}
