import cn from "@/lib/cn";
import Card from "@/ui/Card";
import VStack from "@/ui/VStack";
import { useFormContext, useWatch } from "react-hook-form";
import DateArrow from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/DateArrow";
import EditNormalTime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/edit/EditNormalTime";
import EditOvertime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/edit/EditOvertime";
import EditTimeOff from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/edit/EditTimeOff";
import EditRemarkPopover from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/edit/EditRemarkPopover";
import EditTimesheetDayHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/edit/EditTimesheetDayHeader";
import AddOvertimeAndTimeOffButtons from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/edit/AddOvertimeAndTimeOffButtons";
import TimesheetLeave from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/TimesheetLeave";
import { isFullDayLeave } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";

export default function EditTimesheetDay({
  index,
  timesheetDay,
  currentLeaveTerm,
}) {
  const { isWorkingDay, publicHoliday } = timesheetDay;

  const { control } = useFormContext();

  const watchTimeOff = useWatch({
    control,
    name: `timesheetDays.[${index}].timeOff`,
  });

  const wrapperClassName = "grid grid-cols-[55px_630px_1fr] gap-4";

  return (
    <Card
      key={`date-${index}`}
      className={cn(
        "rounded-none py-2 pr-4 pl-0 first:rounded-t-2xl last:rounded-b-2xl",
        {
          "bg-gray-50": !isWorkingDay,
        },
      )}
    >
      <EditTimesheetDayHeader index={index} className={wrapperClassName} />

      <div className={wrapperClassName}>
        <DateArrow edit timesheetDay={timesheetDay} />

        <VStack className={cn("tabular-num my-auto", { "pt-2": isWorkingDay })}>
          <AMLeaveOrTimeOff
            index={index}
            timesheetDay={timesheetDay}
            watchTimeOff={watchTimeOff}
          />

          <EditNormalTime
            index={index}
            timesheetDay={timesheetDay}
            currentLeaveTerm={currentLeaveTerm}
          />
          <PMLeaveOrTimeOff
            index={index}
            timesheetDay={timesheetDay}
            watchTimeOff={watchTimeOff}
          />

          <EditOvertime
            index={index}
            timesheetDay={timesheetDay}
            currentLeaveTerm={currentLeaveTerm}
          />

          <AddOvertimeAndTimeOffButtons
            index={index}
            timesheetDay={timesheetDay}
          />
        </VStack>

        <EditRemarkPopover
          index={index}
          timesheetDay={timesheetDay}
          currentLeaveTerm={currentLeaveTerm}
        />
      </div>
    </Card>
  );
}

const AMLeaveOrTimeOff = ({ index, timesheetDay, watchTimeOff }) => {
  const { leave } = timesheetDay;
  const FullOrAMLeave =
    leave && (isFullDayLeave(leave) || leave.details === "AM");
  const FullOrAMTimeOff = ["AM", "Full"].includes(watchTimeOff);

  if (FullOrAMLeave) return <TimesheetLeave leave={leave} />;

  if (FullOrAMTimeOff) {
    return <EditTimeOff index={index} timesheetDay={timesheetDay} />;
  }
};

const PMLeaveOrTimeOff = ({ index, timesheetDay, watchTimeOff }) => {
  const { leave } = timesheetDay;
  const PMLeave = leave && leave.details === "PM";
  const PMTimeOff = watchTimeOff === "PM";

  if (PMLeave) return <TimesheetLeave leave={leave} />;

  if (PMTimeOff) {
    return <EditTimeOff index={index} timesheetDay={timesheetDay} />;
  }
};
