"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { toWorkMonth, WORK_MONTH_FORMAT } from "@/formatters/date";
import { useProgressBarLink } from "@/ui/ProgressBar";
import {
  addMonths,
  eachMonthOfInterval,
  endOfMonth,
  isAfter,
  isBefore,
  parse,
  parseISO,
  startOfMonth,
  startOfToday,
} from "date-fns";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import Select from "@/ui/Select";

const getSelectedMonthDate = (selectedTerm, searchParams) => {
  const urlYear = Number(searchParams.get("year") || new Date().getFullYear());
  const urlMonth = Number(
    searchParams.get("month") || new Date().getMonth() + 1,
  );
  const urlWorkMonth = new Date(urlYear, urlMonth - 1, 1);
  const termStartMonth = startOfMonth(parseISO(selectedTerm.startDate));
  const termEndMonth = endOfMonth(parseISO(selectedTerm.endDate));

  if (isBefore(urlWorkMonth, termStartMonth)) {
    return termStartMonth;
  } else if (isAfter(urlWorkMonth, termEndMonth)) {
    return termEndMonth;
  } else {
    return urlWorkMonth;
  }
};

const getNewUrl = (date, pathname, searchParams) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;

  const params = new URLSearchParams(searchParams.toString());
  params.set("year", year);
  params.set("month", month);

  return `${pathname}?${params.toString()}`;
};

const isBetween = (date, start, end) =>
  isAfter(date, start) && isBefore(date, end);

export default function NavigationBar({ selectedTerm, onChange }) {
  const handleProgressBarLink = useProgressBarLink({ scroll: false });
  const pathname = usePathname();
  const searchParams = useSearchParams();

  let { startDate, endDate } = selectedTerm;
  startDate = parseISO(startDate);
  endDate = parseISO(endDate);
  const options = eachMonthOfInterval({
    start: startDate,
    end: endDate,
  }).map((month) => toWorkMonth(month));
  const selectedMonthDate = getSelectedMonthDate(selectedTerm, searchParams);
  const selectedMonth = toWorkMonth(selectedMonthDate);

  const handleMonthChange = (e) => {
    const value = e.target.value;
    const date = parse(value, WORK_MONTH_FORMAT, new Date());
    const newUrl = getNewUrl(date, pathname, searchParams);
    onChange && onChange(date);
    handleProgressBarLink(newUrl)(e);
  };

  const handlePrevClick = (e) => {
    const date = addMonths(selectedMonthDate, -1);
    const newUrl = getNewUrl(date, pathname, searchParams);
    onChange && onChange(date);
    handleProgressBarLink(newUrl)(e);
  };

  const handleNextClick = (e) => {
    const date = addMonths(selectedMonthDate, 1);
    const newUrl = getNewUrl(date, pathname, searchParams);
    onChange && onChange(date);
    handleProgressBarLink(newUrl)(e);
  };

  const handleTodayClick = (e) => {
    const date = new Date();
    const newUrl = getNewUrl(date, pathname, searchParams);
    onChange && onChange(date);
    handleProgressBarLink(newUrl)(e);
  };

  const disablePrevButton = isBefore(
    addMonths(selectedMonthDate, -1),
    startDate,
  );
  const disableNextButton = isAfter(addMonths(selectedMonthDate, 1), endDate);
  const disableTodayButton = !isBetween(startOfToday(), startDate, endDate);

  return (
    <HStack className="justify-between">
      <Select value={selectedMonth} onChange={handleMonthChange}>
        {options.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </Select>
      <HStack className="flex-nowrap items-center-safe gap-4">
        <Button
          variant="plainIcon"
          prefix={<ChevronLeftIcon size={20} strokeWidth={2.5} />}
          onClick={handlePrevClick}
          disabled={disablePrevButton}
        />
        <Button
          variant="plainIcon"
          prefix={<ChevronRightIcon size={20} strokeWidth={2.5} />}
          onClick={handleNextClick}
          disabled={disableNextButton}
        />
        <Button
          variant="secondary"
          onClick={handleTodayClick}
          disabled={disableTodayButton}
          outline
        >
          Today
        </Button>
      </HStack>
    </HStack>
  );
}
