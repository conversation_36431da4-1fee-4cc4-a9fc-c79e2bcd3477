import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import { useParams } from "next/navigation";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import { ChevronDownIcon } from "lucide-react";
import ForkSpoonIcon from "@/icons/emp/ForkKnifeIcon";
import PeopleFilledIcon from "@/icons/filled/PeopleFilledIcon";
import CalendarFilledIcon from "@/icons/filled/CalendarFilledIcon";
import { List, PropertyList } from "@/ui/PropertyList";
import { isEmpty } from "lodash";
import { formatWorkMonth } from "@/formatters/date";

export default function TimesheetHeader({ employment, timesheet, children }) {
  const { year, month } = useParams();

  const { approvers, currentLeaveTerm } = employment;
  const timesheetApprovers = approvers.find(
    (a) => a.payrollModule === "TIMESHEET",
  );
  const workMonth = formatWorkMonth(new Date(year, month - 1, 1));
  const workingHours = currentLeaveTerm?.workingHours;

  return (
    <div className="payroll-shadow sticky top-[106px] z-20 space-y-2 border-b bg-background px-4 py-2">
      <HStack className="justify-between">
        <HStack className="gap-4">
          <VStack className="gap-0">
            <h1 className="text-xl font-semibold">Timesheet for {workMonth}</h1>

            {timesheet?.fmId && (
              <p className="text-xs text-muted">Ref ID: {timesheet.fmId}</p>
            )}
          </VStack>

          <HStack className="gap-1">
            <ApproversPopover approvers={timesheetApprovers} />
            <WorkingHoursPopover workingHours={workingHours} />
          </HStack>
        </HStack>

        {children}
      </HStack>
    </div>
  );
}

const PopoverTriggerButton = ({ prefix, children }) => (
  <PopoverTrigger>
    <Button
      variant="bgIcon"
      className="rounded-full bg-gray-100"
      size="sm"
      prefix={prefix}
      suffix={<ChevronDownIcon size="16" />}
    >
      {children}
    </Button>
  </PopoverTrigger>
);

const ApproversPopover = ({ approvers }) => {
  const defaultClaimApprover1 = approvers.approver1?.approver;
  const defaultClaimApprover2 = approvers.approver2?.approver;
  if (isEmpty(defaultClaimApprover1) && isEmpty(defaultClaimApprover2))
    return null;

  return (
    <Popover>
      <PopoverTriggerButton prefix={<PeopleFilledIcon color="#4b5563" />}>
        Approvers
      </PopoverTriggerButton>

      <PopoverContent align="start" className="max-w-lg p-2">
        <div className="space-y-1">
          <HStack>
            <h1 className="font-semibold">{defaultClaimApprover1.name}</h1> -
            <p className="text-xs text-muted">{defaultClaimApprover1.email}</p>
          </HStack>

          {defaultClaimApprover2 && (
            <HStack>
              <h1 className="font-semibold">{defaultClaimApprover2.name}</h1> -
              <p className="text-xs text-muted">
                {defaultClaimApprover2.email}
              </p>
            </HStack>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};

const WorkingHoursPopover = ({ workingHours }) => {
  if (isEmpty(workingHours)) return null;

  return (
    <Popover>
      <PopoverTriggerButton prefix={<CalendarFilledIcon color="#f97316" />}>
        Working days
      </PopoverTriggerButton>

      <PopoverContent align="start" className="max-w-lg space-y-4 p-2">
        {workingHours.map((workingHour, index) => {
          return (
            <PropertyList
              key={`working_hour_${index}`}
              className="min-w-[220px]"
            >
              <List label="Working days" labelClassName="my-auto text-center">
                {workingHour.formattedWorkingDays}
              </List>
              <List label="Working hours" labelClassName="my-auto text-center">
                {workingHour.workingHourFrom} &mdash;{" "}
                {workingHour.workingHourTo}
              </List>
              <List label="Break" labelClassName="my-auto text-center">
                <HStack className="justify-end-safe gap-1">
                  <ForkSpoonIcon size="16" />
                  {workingHour.formattedBreakDuration}
                </HStack>
              </List>
            </PropertyList>
          );
        })}
      </PopoverContent>
    </Popover>
  );
};
