import Card from "@/ui/Card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import Button from "@/ui/Button";
import {
  Wrench,
  ChevronDown,
  FileTypeIcon,
  CirclePlusIcon,
} from "lucide-react";
import { useState } from "react";
import { useParams } from "next/navigation";
import { Popover, PopoverTrigger, PopoverContent } from "@/ui/Popover";
import HStack from "@/ui/HStack";
import ShiftRosterCard from "./ShiftRosterCard";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
  DialogFooterWithCTA,
} from "@/ui/Dialog";
import { getDatesOfMonth } from "../../getDatesOfMonth";
import cn from "@/lib/cn";
import { ShiftRosterExamples } from "./ShiftRosterExamples";
import Tooltip from "@/ui/Tooltip";
import { SunIcon, MoonIcon, SunHorizonIcon } from "@phosphor-icons/react";
import VStack from "@/ui/VStack";
import { parse, startOfMonth, startOfWeek, getDay, format } from "date-fns";
import SunFilledIcon from "@/icons/filled/SunFilledIcon";
import SunHorizonFilledIcon from "@/icons/filled/SunHorizonFilledIcon";
import MoonFilledIcon from "@/icons/filled/MoonFilledIcon";
import { find, floor, includes, parseInt } from "lodash";
import { List, PropertyList } from "@/ui/PropertyList";
import Switch from "@/ui/Switch";
import pluralize from "pluralize";
import OffFilledIcon from "@/icons/filled/OffFilledIcon";

const getShift = (selectedShift, date) => {
  if (selectedShift?.dayShift?.includes(date)) {
    return "Day";
  } else if (selectedShift?.noonShift?.includes(date)) {
    return "Noon";
  } else if (selectedShift?.nightShift?.includes(date)) {
    return "Night";
  } else if (selectedShift?.off?.includes(date)) {
    return "Off";
  } else {
    return "";
  }
};

export default function EventCalendarView({ datesOfMonth, selectedShift }) {
  const [showPH, setShowPH] = useState(false);
  const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  const firstDay = datesOfMonth[0];
  const publicHolidaysCount = datesOfMonth.filter(
    ({ publicHoliday }) => publicHoliday,
  ).length;
  const leavesCount = datesOfMonth.filter(({ leaveType }) => leaveType).length;

  console.log(datesOfMonth);

  return (
    <div className="space-y-4 pl-4">
      <HStack className="mb-auto justify-end gap-4 pb-4">
        <Switch
          size="sm"
          suffix="PH"
          onChange={(e) => {
            setShowPH(e.target.checked);
          }}
        />
      </HStack>
      <div>
        <div className="max-w-[450px]s relative mx-auto grid grid-cols-7 gap-0">
          {daysOfWeek.map((day, index) => {
            return (
              <div key={index} className="px-2 py-1 text-center text-sm">
                {day}
              </div>
            );
          })}
        </div>
        <div className="max-w-[450px]s relative mx-auto grid grid-cols-7 gap-0 divide-x divide-y">
          {Array.from({
            length: firstDay.dayOfWeek === "Sun" ? 42 : 35,
          }).map((_, index) => {
            const findDate = datesOfMonth.find(
              (dateOfMonth) => dateOfMonth.index === index,
            );

            if (!findDate) {
              const blankFirstDay = index === 0;
              const blankLastDay =
                index === (firstDay.dayOfWeek === "Sun" ? 41 : 34);

              return (
                <div
                  key={index}
                  className={cn({
                    "border-t": index / 7 < 1,
                    "rounded-tl-lg border-l": blankFirstDay,
                    "rounded-br-lg border-r border-b": blankLastDay,
                  })}
                />
              );
            }

            const {
              date,
              publicHoliday,
              leaveType,
              index: dateIndex,
            } = findDate;

            const dayShiftDates = selectedShift?.dayShift || [];
            const noonShiftDates = selectedShift?.noonShift || [];
            const nightShiftDates = selectedShift?.nightShift || [];
            const offDates = selectedShift?.off || [];

            const dayShift = dayShiftDates.includes(date);
            const noonShift = noonShiftDates.includes(date);
            const nightShift = nightShiftDates.includes(date);
            const off = offDates.includes(date);

            const hasEvent =
              dayShift ||
              noonShift ||
              nightShift ||
              off ||
              publicHoliday ||
              leaveType;

            const shift = getShift(selectedShift, date);
            const prevShift = getShift(
              selectedShift,
              (parseInt(date) - 1).toString().padStart(2, "0"),
            );
            const nextShift = getShift(
              selectedShift,
              (parseInt(date) + 1).toString().padStart(2, "0"),
            );

            const sameShiftWithPrev = shift === prevShift;
            const sameShiftWithNext = shift === nextShift;

            const prevDate = datesOfMonth.find(
              (dateOfMonth) =>
                dateOfMonth.date ===
                (parseInt(date) - 1).toString().padStart(2, "0"),
            );
            const nextDate = datesOfMonth.find(
              (dateOfMonth) =>
                dateOfMonth.date ===
                (parseInt(date) + 1).toString().padStart(2, "0"),
            );
            const sameWeekWithPrev =
              floor(dateIndex / 7) === floor(prevDate?.index / 7);
            const sameWeekWithNext =
              floor(dateIndex / 7) === floor(nextDate?.index / 7);

            const week1 = index / 7 < 1;
            const lastWeek =
              index / 7 >= (firstDay.dayOfWeek === "Sun" ? 5 : 4);
            const firstDayOfWeek = index % 7 === 0;
            const lastDayOfWeek = index % 7 === 6;

            const Shift = () => {
              return (
                <div
                  className={cn("", {
                    "pr-0 pl-1": !sameShiftWithPrev,
                    "pr-1 pl-0": !sameShiftWithNext,
                    "px-1": !sameShiftWithPrev && !sameShiftWithNext,
                  })}
                >
                  <HStack
                    className={cn(
                      "rounded-mds borders items-center px-2 py-0.5 text-xs",
                      {
                        "rounded-l-md": !sameShiftWithPrev,
                        "rounded-r-md": !sameShiftWithNext,
                        "border-yellow-500 bg-yellow-100 text-yellow-900":
                          dayShift,
                        "border-orange-500 bg-orange-100 text-orange-900":
                          noonShift,
                        "border-indigo-500 bg-indigo-100 text-indigo-900":
                          nightShift,
                        "border-input bg-gray-100": off,
                      },
                    )}
                  >
                    <HStack
                      className={cn("gap-1", {
                        invisible: !sameShiftWithNext,
                        visible: !sameShiftWithPrev && !sameShiftWithNext,
                      })}
                    >
                      <Icon shift={shift} />
                      {shift}
                    </HStack>
                    {/* <p>Day</p> */}
                  </HStack>
                </div>
              );
            };

            return (
              <div
                key={index}
                className={cn(
                  "p-2s rounded-mds borders border-gray-300s w-full text-sm tabular-nums select-none",
                  {
                    // "rounded-tl-md": !prevShift,
                    // "rounded-br-md": !nextShift,
                    "border-t": week1,
                    "border-l": firstDayOfWeek,
                    "rounded-tl-lg": week1 && firstDayOfWeek,
                    "rounded-tr-lg": week1 && lastDayOfWeek,
                    "rounded-bl-lg": lastWeek && firstDayOfWeek,
                    "rounded-br-lg border-r border-b":
                      lastWeek && lastDayOfWeek,
                    //   "border-yellow-500 bg-yellow-100 text-yellow-900":
                    //     dayShift,
                    //   "border-orange-500 bg-orange-50 text-orange-900":
                    //     noonShift,
                    //   "border-indigo-500 bg-indigo-100 text-indigo-900":
                    //     nightShift,
                    //   "border-input bg-gray-50": off,
                  },
                )}
              >
                <p className="p-2">{date}</p>

                {hasEvent && (
                  <VStack className="pb-2">
                    <Shift />
                    {showPH && publicHoliday && (
                      <div className="px-1">
                        <div className="w-full rounded-md bg-sky-50 px-1 py-0.5 text-xs text-sky-900">
                          {publicHoliday}
                        </div>
                      </div>
                    )}
                  </VStack>
                )}

                {/* <HStack className="mb-auto w-full justify-between"></HStack> */}
              </div>
            );
          })}
        </div>
        {Array.from({
          length: firstDay.dayOfWeek === "Sun" ? 42 : 35,
        }).map((_, index) => {
          const findDate = datesOfMonth.find(
            (dateOfMonth) => dateOfMonth.index === index,
          );

          if (!findDate) {
            const blankFirstDay = index === 0;
            const blankLastDay =
              index === (firstDay.dayOfWeek === "Sun" ? 41 : 34);

            return (
              <div
                key={index}
                className={cn({
                  "border-t": index / 7 < 1,
                  "rounded-tl-lg border-l": blankFirstDay,
                  "rounded-br-lg border-r border-b": blankLastDay,
                })}
              />
            );
          }

          const { date, publicHoliday, leaveType, index: dateIndex } = findDate;

          const dayShiftDates = selectedShift?.dayShift || [];
          const noonShiftDates = selectedShift?.noonShift || [];
          const nightShiftDates = selectedShift?.nightShift || [];
          const offDates = selectedShift?.off || [];

          const dayShift = dayShiftDates.includes(date);
          const noonShift = noonShiftDates.includes(date);
          const nightShift = nightShiftDates.includes(date);
          const off = offDates.includes(date);

          const hasEvent =
            dayShift ||
            noonShift ||
            nightShift ||
            off ||
            publicHoliday ||
            leaveType;

          const shift = getShift(selectedShift, date);
          const prevShift = getShift(
            selectedShift,
            (parseInt(date) - 1).toString().padStart(2, "0"),
          );
          const nextShift = getShift(
            selectedShift,
            (parseInt(date) + 1).toString().padStart(2, "0"),
          );

          const sameShiftWithPrev = shift === prevShift;
          const sameShiftWithNext = shift === nextShift;

          const prevDate = datesOfMonth.find(
            (dateOfMonth) =>
              dateOfMonth.date ===
              (parseInt(date) - 1).toString().padStart(2, "0"),
          );
          const nextDate = datesOfMonth.find(
            (dateOfMonth) =>
              dateOfMonth.date ===
              (parseInt(date) + 1).toString().padStart(2, "0"),
          );
          const sameWeekWithPrev =
            floor(dateIndex / 7) === floor(prevDate?.index / 7);
          const sameWeekWithNext =
            floor(dateIndex / 7) === floor(nextDate?.index / 7);

          const week1 = index / 7 < 1;
          const lastWeek = index / 7 >= (firstDay.dayOfWeek === "Sun" ? 5 : 4);
          const firstDayOfWeek = index % 7 === 0;
          const lastDayOfWeek = index % 7 === 6;

          const Shift = () => {
            return (
              <div
                className={cn("", {
                  "pr-0 pl-1": !sameShiftWithPrev,
                  "pr-1 pl-0": !sameShiftWithNext,
                  "px-1": !sameShiftWithPrev && !sameShiftWithNext,
                })}
              >
                <HStack
                  className={cn(
                    "rounded-mds borders items-center px-2 py-0.5 text-xs",
                    {
                      "rounded-l-md": !sameShiftWithPrev,
                      "rounded-r-md": !sameShiftWithNext,
                      "border-yellow-500 bg-yellow-100 text-yellow-900":
                        dayShift,
                      "border-orange-500 bg-orange-100 text-orange-900":
                        noonShift,
                      "border-indigo-500 bg-indigo-100 text-indigo-900":
                        nightShift,
                      "border-input bg-gray-100": off,
                    },
                  )}
                >
                  <HStack
                    className={cn("gap-1", {
                      invisible: !sameShiftWithNext,
                      visible: !sameShiftWithPrev && !sameShiftWithNext,
                    })}
                  >
                    <Icon shift={shift} />
                    {shift}
                  </HStack>
                  {/* <p>Day</p> */}
                </HStack>
              </div>
            );
          };

          return (
            <div
              key={index}
              className={cn(
                "p-2s rounded-mds borders border-gray-300s w-full text-sm tabular-nums select-none",
                {
                  // "rounded-tl-md": !prevShift,
                  // "rounded-br-md": !nextShift,
                  "border-t": week1,
                  "border-l": firstDayOfWeek,
                  "rounded-tl-lg": week1 && firstDayOfWeek,
                  "rounded-tr-lg": week1 && lastDayOfWeek,
                  "rounded-bl-lg": lastWeek && firstDayOfWeek,
                  "rounded-br-lg border-r border-b": lastWeek && lastDayOfWeek,
                  //   "border-yellow-500 bg-yellow-100 text-yellow-900":
                  //     dayShift,
                  //   "border-orange-500 bg-orange-50 text-orange-900":
                  //     noonShift,
                  //   "border-indigo-500 bg-indigo-100 text-indigo-900":
                  //     nightShift,
                  //   "border-input bg-gray-50": off,
                },
              )}
            >
              <p className="p-2">{date}</p>

              {hasEvent && (
                <VStack className="pb-2">
                  <Shift />
                  {showPH && publicHoliday && (
                    <div className="px-1">
                      <div className="w-full rounded-md bg-sky-50 px-1 py-0.5 text-xs text-sky-900">
                        {publicHoliday}
                      </div>
                    </div>
                  )}
                </VStack>
              )}

              {/* <HStack className="mb-auto w-full justify-between"></HStack> */}
            </div>
          );
        })}
      </div>
    </div>
  );
}

const Icon = ({ shift }) => {
  switch (shift) {
    case "Day":
    case "dayShift":
      return <SunFilledIcon color="#d97706" />;
    case "Noon":
    case "noonShift":
      return <SunHorizonFilledIcon color="#ea580c" />;
    case "Night":
    case "nightShift":
      return <MoonFilledIcon color="#4f46e5" />;
    case "Off":
    case "off":
      return <OffFilledIcon color="#6b7280" />;
    case "PH":
      return "PH";
    case "PH":
      return "PH";
    default:
      return null;
  }
};
