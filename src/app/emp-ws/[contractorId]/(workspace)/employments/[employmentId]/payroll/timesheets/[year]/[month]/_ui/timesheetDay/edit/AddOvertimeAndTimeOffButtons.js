import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import { isEmpty } from "lodash";
import { CheckIcon, CirclePlusIcon } from "lucide-react";
import { useFormContext, useWatch } from "react-hook-form";
import {
  isFullDayLeave,
  isHalfDayLeave,
} from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";

export default function AddOvertimeAndTimeOffButtons({
  index,
  timesheetDay = {},
}) {
  const { isWorkingDay, leave } = timesheetDay;

  const { control, setValue, clearErrors } = useFormContext();

  const hasOvertime = useWatch({
    control,
    name: `timesheetDays.[${index}].hasOvertime`,
  });
  const watchTimeOff = useWatch({
    control,
    name: `timesheetDays.[${index}].timeOff`,
  });

  const hasFullDayLeave = isFullDayLeave(leave);
  const hasHalfDayLeave = isHalfDayLeave(leave);
  const timeOffMeridians = hasHalfDayLeave
    ? ["AM", "PM"].filter((meridian) => meridian !== leave.details)
    : ["AM", "PM", "Full"];

  return (
    <HStack className="px-2">
      <div className="w-[24px] text-sm" />

      <div className="grid w-[375px] grid-cols-2 gap-2 text-xs text-muted">
        <OutlineButton
          overtime
          activated={hasOvertime}
          hasOvertime={hasOvertime}
          watchTimeOff={watchTimeOff}
          onClick={() => {
            if (hasOvertime) {
              setValue(`timesheetDays.[${index}].overtimeTimeIn`, "", {
                shouldDirty: true,
              });
              setValue(`timesheetDays.[${index}].overtimeTimeOut`, "", {
                shouldDirty: true,
              });
              setValue(`timesheetDays.[${index}].overtimeBreakDuration`, 0);
            }
            setValue(`timesheetDays.[${index}].hasOvertime`, !hasOvertime);
            clearErrors(`timesheetDays.[${index}].overtimeTimeIn`);
            clearErrors(`timesheetDays.[${index}].overtimeTimeOut`);
          }}
        />

        {isWorkingDay && !hasFullDayLeave && (
          <OutlineButton
            timeOff
            activated={watchTimeOff}
            hasOvertime={hasOvertime}
            watchTimeOff={watchTimeOff}
            onClick={() => {
              const value = isEmpty(watchTimeOff) ? timeOffMeridians[0] : null;

              setValue(`timesheetDays.[${index}].timeOff`, value);

              // If Full day time-off is selected, clear normal time fields
              if (hasHalfDayLeave && value !== "Full") {
                setValue(`timesheetDays.[${index}].normalTimeIn`, "");
                setValue(`timesheetDays.[${index}].normalTimeOut`, "");
                setValue(`timesheetDays.[${index}].normalBreakDuration`, 0);
              }
            }}
          />
        )}
      </div>
    </HStack>
  );
}

const OutlineButton = ({
  overtime = false,
  timeOff = false,
  activated = false,
  hasOvertime = false,
  watchTimeOff = false,
  onClick = () => {},
}) => (
  <button
    type="button"
    className={cn(
      "w-full cursor-pointer rounded-md border border-gray-400 px-2 py-0.5 opacity-50 hover:opacity-100",
      {
        "border-amber-800 bg-amber-50 font-semibold text-amber-900 opacity-100":
          overtime && hasOvertime,
        "border-gray-700 bg-gray-50 font-semibold text-black opacity-80 hover:opacity-80":
          timeOff && watchTimeOff,
      },
    )}
    onClick={onClick}
  >
    <HStack>
      {activated ? (
        <CheckIcon size={14} strokeWidth={2.5} />
      ) : (
        <CirclePlusIcon size={14} />
      )}
      {overtime && "Overtime"}
      {timeOff && "Time-off"}
    </HStack>
  </button>
);
