import HourglassIcon from "@/icons/emp/HourglassIcon";
import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import pluralize from "pluralize";
import { useFormContext, useWatch } from "react-hook-form";
import {
  getTotalHours,
  getTotalHoursWithoutBreak,
  getWorkingDays,
} from "@/lib/timesheet/calculator";
import {
  Break,
  WorkingHours,
} from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/edit/TimesheetTimeFields";
import { useEffect } from "react";

export default function EditTimesheetTime({
  index,
  overtime = false,
  timesheetDay = {},
  currentLeaveTerm = {},
  fieldNames = {},
}) {
  const { timeInName, timeOutName, breakDurationName, hoursName } = fieldNames;
  const leaveTermWorkingHour = getWorkingDays(currentLeaveTerm?.workingHours)[
    timesheetDay.dayOfWeek
  ];
  const leaveTermBreak = leaveTermWorkingHour?.normalBreakDuration;

  const { control, setValue } = useFormContext();

  const timeIn = useWatch({ control, name: timeInName });
  const timeOut = useWatch({ control, name: timeOutName });
  const breakDuration = useWatch({ control, name: breakDurationName }) || 0;

  const totalHoursWithoutBreak = getTotalHoursWithoutBreak(
    timeIn?.value || timeIn,
    timeOut?.value || timeOut,
  );
  const totalHours = getTotalHours(
    timeIn?.value || timeIn,
    timeOut?.value || timeOut,
    breakDuration,
  );

  useEffect(() => {
    if (timeIn && timeOut) {
      if (timeIn.value && timeOut.value) {
        // If total hours is less than 6 hours. Set break duration to 0
        const updatedBreakDuration =
          totalHoursWithoutBreak < 6 ? 0 : leaveTermBreak;

        setValue(breakDurationName, updatedBreakDuration);
      }
    }
  }, [
    breakDurationName,
    leaveTermBreak,
    setValue,
    timeIn,
    timeOut,
    totalHoursWithoutBreak,
  ]);

  useEffect(() => {
    if (timeIn && timeOut) {
      if (timeIn.value && timeOut.value) {
        setValue(hoursName, totalHours);
      }
    }
  }, [hoursName, setValue, timeIn, timeOut, totalHours]);

  return (
    <HStack
      className={cn("tabular-num rounded-md pt-1.5", {
        "bg-amber-50 py-1.5": overtime,
      })}
    >
      <WorkingHours
        overtime={overtime}
        index={index}
        fieldNames={fieldNames}
        leaveTermWorkingHour={leaveTermWorkingHour}
      />

      <Break
        overtime={overtime}
        breakDuration={breakDuration}
        breakDurationName={breakDurationName}
        totalHours={totalHours}
      />

      {/* Total Hours */}
      <HStack className="w-[80px] gap-1">
        <HourglassIcon color="#000" />

        <div className="w-[60px]">
          {totalHours}{" "}
          <span className="text-xs">{pluralize("hrs", totalHours)}</span>
        </div>
      </HStack>
    </HStack>
  );
}
