import { CircleAlertIcon, PartyPopperIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import cn from "@/lib/cn";
import { isEmpty } from "lodash";
import TimesheetLeaveIcon from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/TimesheetLeaveIcon";
import Tooltip from "@/ui/Tooltip";

const Content = ({ leave }) => {
  const { longLeaveType, details, status } = leave;
  const polishedDetails = () => {
    if (details === "1") return "Full";

    return details;
  };

  return (
    <HStack className="flex-1 justify-between rounded-md bg-sky-50 px-2 text-sky-900">
      <p>
        {longLeaveType} <span className="text-sm">({polishedDetails()})</span>
      </p>

      {/* {publicHoliday && <PartyPopperIcon size={16} />} */}
      {leave && status !== "APPROVED" ? (
        <HStack>
          <p className="cursor-default text-sm font-semibold">{status}</p>
          <CircleAlertIcon size={16} />
        </HStack>
      ) : (
        <TimesheetLeaveIcon leaveType={longLeaveType} />
      )}
    </HStack>
  );
};

export default function TimesheetLeave({ leave = {} }) {
  if (isEmpty(leave)) {
    return null;
  }

  const { status } = leave;

  return (
    <Tooltip
      side="right"
      content={status}
      sideOffset={6}
      align="center"
      className="bg-black/70 text-xs opacity-80 backdrop-blur-sm"
    >
      <HStack className="tabular-num">
        <div className="w-[30px]" />
        <Content leave={leave} />
      </HStack>
    </Tooltip>
  );
}
