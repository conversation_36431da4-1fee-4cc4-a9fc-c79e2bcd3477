"use client";

import ClaimsAccordion from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/_ui/GroupedClaimAccordion";
import ExpandCollapseButton from "@/components/accordion-variants/ExpandCollapseButton";
import HStack from "@/ui/HStack";
import { groupBy } from "lodash";
import { useState } from "react";

export default function TodaySubmittedDrafts({ claims }) {
  const groupedClaims = groupBy(claims, "claimType");
  const allAccordionValues = Object.keys(groupedClaims);
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  return (
    <div className="space-y-4 border-t p-4">
      <HStack className="flex-nowrap justify-between gap-4">
        <h1 className="text-xl font-semibold">
          Today&#39;s submitted drafts (Non-Final)
        </h1>

        <ExpandCollapseButton
          onExpand={() => setAccordionValues(allAccordionValues)}
          onCollapse={() => setAccordionValues([])}
          iconSize={20}
          iconStrokeWidth={2}
        />
      </HStack>

      <div className="mx-auto max-w-6xl">
        <ClaimsAccordion
          groupedClaims={groupedClaims}
          accordionValues={accordionValues}
          setAccordionValues={setAccordionValues}
        />
      </div>
    </div>
  );
}
