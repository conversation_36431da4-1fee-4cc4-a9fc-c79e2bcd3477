"use client";

import Button from "@/ui/Button";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTrigger,
} from "@/ui/Dialog";
import { useState } from "react";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Type",
    columnWidth: 200,
    propertyName: "claimType",
    visible: true,
  },
  {
    columnName: "Sub Type",
    columnWidth: 200,
    propertyName: "claimSubType",
    visible: true,
  },
  {
    columnName: "Basis",
    columnWidth: 200,
    propertyName: "basis",
    visible: true,
  },
  {
    columnName: "Limit",
    columnWidth: 200,
    propertyName: "claimLimit",
    visible: true,
  },
];

const claimLimits = [
  {
    claimType: "Medical",
    claimSubType: "Medical",
    basis: "Monthly",
    claimLimit: "$30.00",
  },
  {
    claimType: "Meal",
    claimSubType: "Meal (Weekday)",
    basis: "Daily",
    claimLimit: "$12.00",
  },
  {
    claimType: "Meal",
    claimSubType: "Meal (Weekend)",
    basis: "Daily",
    claimLimit: "$15.00",
  },
  {
    claimType: "Transport",
    claimSubType: "Transport",
    basis: "Monthly",
    claimLimit: "$300.00",
  },
];

export default function ClaimLimitsDialog() {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button outline size="sm" variant="secondary">
          View claim limits
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>Claim Limits</DialogHeader>

        <DataGrid.Root>
          <DataGrid.Content>
            <DataGridTable
              numRows={claimLimits.length}
              data={claimLimits}
              tableColumns={DEFAULT_TABLE_COLUMNS}
              defaultTableColumns={DEFAULT_TABLE_COLUMNS}
              emptyText="No claims limit found."
            />
          </DataGrid.Content>
        </DataGrid.Root>

        <DialogFooter>
          <div className="flex w-full justify-center gap-3">
            <DialogDismissButton pill />
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
