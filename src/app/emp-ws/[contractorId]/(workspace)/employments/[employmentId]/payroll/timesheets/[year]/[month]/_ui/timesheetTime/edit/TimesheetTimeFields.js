import AutocompleteWorkingHour from "@/components/autocomplete/AutocompleteWorkingHour";
import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import Input from "@/ui/Input";
import { useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import { CheckIcon, ChevronDownIcon } from "lucide-react";
import ForkSpoonIcon from "@/icons/emp/ForkKnifeIcon";
import pluralize from "pluralize";
import {
  isHalfDayLeave,
  validateWorkingHourOverlapped,
} from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";
import { getCurrentLeaveTermNormalHours } from "@/lib/timesheet/calculator";

const WorkingHours = ({
  overtime = false,
  roster = false,
  index,
  fieldNames,
  leaveTermWorkingHour,
}) => {
  const { timeInName, timeOutName } = fieldNames;
  const { control, register } = useFormContext();

  const timeIn = useWatch({ control, name: timeInName });
  const timeOut = useWatch({ control, name: timeOutName });

  return (
    <HStack className="px-2">
      <p
        className={cn("w-[24px] text-center text-sm opacity-80", {
          "text-amber-900": overtime,
        })}
      >
        {overtime && "OT"}
      </p>

      <div className="grid w-[375px] grid-cols-2 gap-2">
        {roster ? (
          <>
            <WorkingHourInput
              label="Time-in"
              defaultValue={timeIn}
              {...register(timeInName)}
            />

            <WorkingHourInput
              label="Time-out"
              defaultValue={timeOut}
              {...register(timeOutName)}
            />
          </>
        ) : (
          <>
            <WorkingHourAutocomplete
              index={index}
              overtime={overtime}
              label="Time-in"
              name={timeInName}
              defaultValue={timeIn?.value || timeIn}
              valuesToHide={[timeOut]}
              leaveTermWorkingHour={leaveTermWorkingHour}
            />

            <WorkingHourAutocomplete
              index={index}
              overtime={overtime}
              label="Time-out"
              name={timeOutName}
              defaultValue={timeOut?.value || timeOut}
              valuesToHide={[timeIn]}
              leaveTermWorkingHour={leaveTermWorkingHour}
            />
          </>
        )}
      </div>
    </HStack>
  );
};

const BREAK_DURATIONS = [
  { duration: "0 mins", value: 0 },
  { duration: "30 mins", value: 30 },
  { duration: "42 mins", value: 42 },
  { duration: "45 mins", value: 45 },
  { duration: "60 mins", value: 60 },
  { duration: "120 mins", value: 120 },
];

const Break = ({ overtime, breakDuration, breakDurationName, totalHours }) => {
  const [open, setOpen] = useState(false);
  const { setValue } = useFormContext();

  if (overtime) {
    return (
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger>
          <button>
            <HStack className="w-[110px] cursor-pointer gap-1">
              <ForkSpoonIcon color="#000" />

              <HStack className="gap-1 rounded-md">
                <div>
                  {breakDuration}{" "}
                  <span className="text-xs">
                    {pluralize("min", breakDuration)}
                  </span>
                </div>

                <ChevronDownIcon size={18} />
              </HStack>
            </HStack>
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent sideOffset={0}>
          {BREAK_DURATIONS.map(({ duration, value }) => {
            const selected = breakDuration === value;

            return (
              <DropdownMenuItem
                key={duration}
                onClick={() => {
                  setValue(breakDurationName, value);
                }}
              >
                <div className="flex items-center gap-2">
                  <CheckMark selected={selected} />
                  {duration}
                </div>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <HStack className="w-[110px] gap-1">
      <ForkSpoonIcon color="#000" />

      <div>
        {breakDuration}{" "}
        <span className="text-xs">{pluralize("min", breakDuration)}</span>
      </div>
    </HStack>
  );
};

const WorkingHourInput = ({ label, defaultValue, ...props }) => (
  <Input
    readOnly
    className="cursor-default"
    size="sm"
    label={label}
    defaultValue={defaultValue}
    {...props}
  />
);

const WorkingHourAutocomplete = ({
  overtime = false,
  index,
  label,
  name,
  defaultValue,
  valuesToHide,
  leaveTermWorkingHour,
}) => {
  const { control, clearErrors, formState = { errors } } = useFormContext();
  const timesheetDay = useWatch({ control, name: `timesheetDays.[${index}]` });
  const timeOff = timesheetDay?.timeOff;
  const normalHours = timesheetDay?.normalHours;

  const hasHalfDayLeave = isHalfDayLeave(timesheetDay?.leave);
  const halfDayTimeOff = timeOff === "AM" || timeOff === "PM";
  const currentLeaveTermNormalHours =
    getCurrentLeaveTermNormalHours(leaveTermWorkingHour);

  return (
    <AutocompleteWorkingHour
      size="sm"
      label={label}
      name={name}
      defaultValue={defaultValue}
      valuesToHide={valuesToHide}
      suffix={<div />}
      labelClassNameProp={overtime && "!bg-amber-50 rounded"}
      rules={{
        required: true,
        validate: () => {
          if (
            (hasHalfDayLeave || halfDayTimeOff) &&
            normalHours >= currentLeaveTermNormalHours
          ) {
            return `Cannot exceed ${currentLeaveTermNormalHours} hrs.`;
          }

          if (overtime && validateWorkingHourOverlapped(timesheetDay)) {
            return "Working hours overlapped";
          }
        },
      }}
    />
  );
};

const CheckMark = ({ selected }) => {
  return selected ? (
    <CheckIcon size={16} strokeWidth={2} />
  ) : (
    <div className="invisible w-4" />
  );
};

export { WorkingHours, Break };
