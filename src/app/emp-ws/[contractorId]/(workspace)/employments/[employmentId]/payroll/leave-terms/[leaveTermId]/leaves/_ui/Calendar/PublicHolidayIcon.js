import Image from "next/image";

// Import all public holiday images
import christmasPic from "@/public/images/public-holidays/christmas.png";
import cnyPic from "@/public/images/public-holidays/cny.png";
import deepavaliPic from "@/public/images/public-holidays/deepavali.png";
import goodFridayPic from "@/public/images/public-holidays/good-friday.png";
import hariRayaHajiPic from "@/public/images/public-holidays/hari-raya-haji.png";
import hariRayaPuasaPic from "@/public/images/public-holidays/hari-raya-puasa.png";
import labourDayPic from "@/public/images/public-holidays/labour-day.png";
import nationalDayPic from "@/public/images/public-holidays/national-day.png";
import newYearPic from "@/public/images/public-holidays/new-year.png";
import pollingDayPic from "@/public/images/public-holidays/polling-day.png";
import vesakDayPic from "@/public/images/public-holidays/vesak-day.png";

// Create a mapping object for easy access
const publicHolidayImages = {
  christmas: christmasPic,
  cny: cnyPic,
  "chinese-new-year": cnyPic,
  deepavali: deepavaliPic,
  "good-friday": goodFridayPic,
  "hari-raya-haji": hariRayaHajiPic,
  "hari-raya-puasa": hariRayaPuasaPic,
  "labour-day": labourDayPic,
  "national-day": nationalDayPic,
  "new-year": newYearPic,
  "polling-day": pollingDayPic,
  "vesak-day": vesakDayPic,
};

// Function to get holiday image based on event name
function getHolidayImageByEvent(eventName) {
  if (!eventName) return newYearPic;

  const eventLower = eventName.toLowerCase();

  // Map event names to image keys
  if (eventLower.includes("new year")) return publicHolidayImages["new-year"];
  if (eventLower.includes("chinese new year") || eventLower.includes("cny"))
    return publicHolidayImages.cny;
  if (eventLower.includes("substitute holiday") && eventLower.includes("cny"))
    return publicHolidayImages.cny;
  if (eventLower.includes("good friday"))
    return publicHolidayImages["good-friday"];
  if (eventLower.includes("hari raya puasa"))
    return publicHolidayImages["hari-raya-puasa"];
  if (eventLower.includes("labour day"))
    return publicHolidayImages["labour-day"];
  if (eventLower.includes("vesak day")) return publicHolidayImages["vesak-day"];
  if (eventLower.includes("hari raya haji"))
    return publicHolidayImages["hari-raya-haji"];
  if (eventLower.includes("national day"))
    return publicHolidayImages["national-day"];
  if (eventLower.includes("deepavali")) return publicHolidayImages.deepavali;
  if (eventLower.includes("christmas")) return publicHolidayImages.christmas;
  if (eventLower.includes("polling day"))
    return publicHolidayImages["polling-day"];

  // Default fallback
  return newYearPic;
}

export default function PublicHolidayIcon({ event, holidayType = "new-year" }) {
  // Get the appropriate image based on holidayType, fallback to new-year
  const holidayImage = publicHolidayImages[holidayType] || newYearPic;

  return (
    <Image src={holidayImage} alt={event || "Holiday"} width={24} height={24} />
  );
}

// Export the images mapping for external use if needed
export { publicHolidayImages };
