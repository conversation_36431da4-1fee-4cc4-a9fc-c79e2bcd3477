import cn from "@/lib/cn";
import VStack from "@/ui/VStack";
import { format, isValid, parse } from "date-fns";
import { isString } from "lodash";
import { validateWorkingDay } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";

export default function DateArrow({
  edit,
  className,
  arrowClassName,
  timesheetDay,
}) {
  const { date, day, dayOfWeek, normalTimeIn, normalTimeOut } = timesheetDay;

  const isWorkingDay = validateWorkingDay(
    normalTimeIn,
    normalTimeOut,
    edit,
    timesheetDay.isWorkingDay,
  );

  const convertDate = () => {
    if (isString(date)) {
      const parsedDate = parse(date, "dd/MM/yyyy", new Date());

      return isValid(parsedDate) ? format(parsedDate, "dd") : "";
    }
  };

  return (
    <VStack
      className={cn(
        "sticky left-0 z-10 gap-4 rounded-2xl pl-2",
        {
          "bg-background": isWorkingDay,
        },
        className,
      )}
    >
      <ArrowWrapper
        className={arrowClassName}
        timesheetDay={timesheetDay}
        isWorkingDay={isWorkingDay}
      >
        <VStack className={cn("min-w-[40px] items-center gap-0 pr-2")}>
          <p className="tabular-num text-sm font-semibold">{convertDate()}</p>
          <p className="text-xs">{dayOfWeek || day}</p>
        </VStack>
      </ArrowWrapper>
    </VStack>
  );
}

const ArrowWrapper = ({ className, timesheetDay, isWorkingDay, children }) => {
  const { normalTimeIn, normalTimeOut, halfDay } = timesheetDay;

  return (
    <div
      className={cn(
        "my-auto w-fit rounded-md bg-amber-100 px-2 py-1 text-amber-900",
        {
          "bg-gray-100 text-black": !isWorkingDay,
          "bg-[linear-gradient(135deg,_transparent_50%,_#f3f4f6_50%)]": halfDay,
        },
        className,
      )}
      style={{
        clipPath:
          "polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%)",
      }}
    >
      {children}
    </div>
  );
};
