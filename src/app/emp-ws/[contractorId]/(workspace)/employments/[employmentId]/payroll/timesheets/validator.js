import totalWorkingHours from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/totalWorkingHours";
import {
  checkIfNextDay,
  getWorkingDays,
  parseDateFromTime,
} from "@/lib/timesheet/calculator";
import { parse } from "date-fns";
import { isEmpty, sumBy, toNumber } from "lodash";

const isHalfDayLeave = (leave) => leave && leave.day === 0.5;
const isFullDayLeave = (leave) => leave && leave.day === 1;

const validateWorkingDay = (
  normalTimeIn = "",
  normalTimeOut = "",
  edit = false,
  isWorkingDay = false,
) => {
  if (edit) return isWorkingDay;

  return normalTimeIn && normalTimeOut;
};

const validateNormalBreakDuration = (normalBreakDuration, normalHours) => {
  if (normalHours < 6) return 0;

  return toNumber(normalBreakDuration);
};

const validateTimesheetInput = (input, year, month) => {
  if (isEmpty(input) || !year || !month) return null;

  const { action } = input;
  const workMonthY = toNumber(year);
  const workMonthM = toNumber(month);
  const timesheetDays = validateTimesheetDaysInput(input.timesheetDays);

  return {
    status: action,
    workMonthY,
    workMonthM,
    totalNormalHours: sumBy(timesheetDays, "normalHours"),
    totalOvertimeHours: sumBy(timesheetDays, "overtimeHours"),
    timesheetDays,
  };
};

const validateNormalWorkingTime = (timesheetDay) => {
  const {
    normalTimeIn,
    normalTimeOut,
    timeOff,
    normalHours,
    normalBreakDuration,
  } = timesheetDay;

  if (timeOff === "Full") {
    return {
      normalTimeIn: "",
      normalTimeOut: "",
      normalHours: 0,
      normalBreakDuration: 0,
    };
  }

  return {
    normalTimeIn: normalTimeIn?.value || "",
    normalTimeOut: normalTimeOut?.value || "",
    normalHours,
    normalBreakDuration,
  };
};

const validateTimesheetDaysInput = (timesheetDays) => {
  return timesheetDays.map((timesheetDay) => {
    const {
      day,
      leave,
      hasOvertime,
      remarkPreview,
      normalTimeIn,
      normalTimeOut,
      overtimeTimeIn,
      overtimeTimeOut,
      normalHours: _,
      normalBreakDuration: inputNormalBreakDuration,
      overtimeHours: __,
      overtimeBreakDuration: ___,
      ...rest
    } = timesheetDay;

    const { normalHours, overtimeHours, overtimeBreakDuration } =
      totalWorkingHours(timesheetDay);
    const normalBreakDuration = validateNormalBreakDuration(
      inputNormalBreakDuration,
      normalHours,
    );

    return {
      ...rest,
      normalTimeIn: normalTimeIn?.value,
      normalTimeOut: normalTimeOut?.value,
      normalBreakDuration,
      normalHours,
      overtimeTimeIn: overtimeTimeIn?.value,
      overtimeTimeOut: overtimeTimeOut?.value,
      overtimeBreakDuration,
      overtimeHours,
    };
  });
};

const validateWorkingHoursMatchWithLeaveTerm = (
  currentLeaveTerm,
  timesheetDay,
  normalTimeIn = "",
  normalTimeOut = "",
) => {
  const { isWorkingDay, dayOfWeek } = timesheetDay;
  // Match validation is not required for non-working day
  if (!isWorkingDay) return true;

  const leaveTermWorkingHour = getWorkingDays(currentLeaveTerm?.workingHours)[
    dayOfWeek
  ];

  const timeInMatchedDefault =
    normalTimeIn === leaveTermWorkingHour?.normalTimeIn;
  const timeOutMatchedDefault =
    normalTimeOut === leaveTermWorkingHour?.normalTimeOut;

  return timeInMatchedDefault && timeOutMatchedDefault;
};

// Find at jobline-six - app/javascript/legacy_payroll/new_timesheet/TimesheetDayModel.js:328
const validateWorkingHourOverlapped = (timesheetDay) => {
  if (isEmpty(timesheetDay)) return false;

  const normalTimeIn = parseDateFromTime(timesheetDay.normalTimeIn?.value);
  // Check overnight
  const normalTimeOut = checkIfNextDay(
    normalTimeIn,
    parseDateFromTime(timesheetDay.normalTimeOut?.value),
  );
  const overtimeTimeIn = parseDateFromTime(timesheetDay.overtimeTimeIn?.value);
  const overtimeTimeOut = checkIfNextDay(
    overtimeTimeIn,
    parseDateFromTime(timesheetDay.overtimeTimeOut?.value),
  );

  return !(normalTimeOut <= overtimeTimeIn || normalTimeIn >= overtimeTimeOut);
};

export {
  isHalfDayLeave,
  isFullDayLeave,
  validateWorkingDay,
  validateTimesheetInput,
  validateTimesheetDaysInput,
  validateWorkingHoursMatchWithLeaveTerm,
  validateWorkingHourOverlapped,
};
