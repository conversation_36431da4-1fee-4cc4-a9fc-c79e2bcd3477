"use client";

import ClaimsIcon from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/new/ClaimsIcon";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/ui/Accordion";
import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import { Edit } from "@blueprintjs/icons";
import { Trash2Icon } from "lucide-react";
import ExpandCollapseButton from "@/components/accordion-variants/ExpandCollapseButton";

export default function ClaimsListing({
  claims,
  setOpen,
  setClaimSelected,
  claimRefs,
  accordionValues,
  setAccordionValues,
  allAccordionValues,
}) {
  const handleRowClick = (claim) => {
    Object.values(claimRefs.current).forEach((el) => {
      el?.classList.remove(
        "bg-blue-100",
        "drop-shadow-blue-200",
        "border-blue-200",
      );
      el?.classList.add("bg-white");
    });

    const el = claimRefs.current[claim.publicId];
    if (el) {
      el.classList.remove("bg-white");
      el.classList.add(
        "bg-blue-100",
        "drop-shadow-blue-200",
        "border-blue-200",
      );
    }

    setClaimSelected(claim);
    setOpen(true);
  };

  if (claims.length === 0) return null;

  return (
    <>
      <HStack className="justify-between">
        <h2 className="text-lg font-semibold">Composed Claims (10)</h2>
        <ExpandCollapseButton
          onExpand={() => setAccordionValues(allAccordionValues)}
          onCollapse={() => setAccordionValues([])}
        />
      </HStack>

      <div className="mx-auto max-w-[800px] space-y-2 px-4">
        <Accordion
          type="multiple"
          value={accordionValues}
          onValueChange={(values) => setAccordionValues(values)}
        >
          {Object.entries(claims).map(([claimType, claims]) => {
            return (
              <AccordionItem
                value={claimType}
                outline={false}
                key={claimType}
                className="pb-2"
              >
                <AccordionTrigger className="gap-2 border-b py-2">
                  <div className="grid grid-cols-[24px_1fr] items-center-safe">
                    <ClaimsIcon type={claimType} />

                    <span>{claimType}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="space-y-2 pt-4 pl-[23px]">
                  {claims.map((claim, index) => (
                    <div
                      key={index}
                      className="mx-auto grid grid-rows-1 border-1 p-2 first:rounded-t-lg last:rounded-b-lg md:grid-cols-[1fr_auto_auto_auto_auto]"
                      ref={(el) => (claimRefs.current[claim.publicId] = el)}
                    >
                      <span className="my-auto box-border overflow-hidden px-2 text-nowrap text-ellipsis">
                        {claim.description}
                      </span>

                      <VStack className="gap-0 px-2">
                        <span className="text-right text-sm text-gray-500">
                          Amount
                        </span>
                        <span className="text-right">
                          ${claim.amountClaimed}.00
                        </span>
                      </VStack>

                      <VStack className="gap-0 px-2">
                        <span className="text-sm text-gray-500">
                          Incurred At
                        </span>
                        <span>05/06/2025</span>
                      </VStack>

                      <HStack className="px-2">
                        <Button
                          variant="plainIcon"
                          onClick={() => handleRowClick(claim)}
                        >
                          <Edit size="20" />
                        </Button>
                        <Button variant="plainIcon">
                          <Trash2Icon color="red" size="20" />
                        </Button>
                      </HStack>
                    </div>
                  ))}
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>
    </>
  );
}
