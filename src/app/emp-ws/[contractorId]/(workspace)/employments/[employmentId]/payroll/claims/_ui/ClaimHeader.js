"use client";

import ClaimMonthsSegmentedControl from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/_ui/ClaimMonthsSegmentedControl";
import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import { ProgressBarLink } from "@/ui/ProgressBar";
import VStack from "@/ui/VStack";
import { useParams } from "next/navigation";

const MONTHS = [
  { month: "Jan", value: "01" },
  { month: "Feb", value: "02" },
  { month: "Mar", value: "03" },
  { month: "Apr", value: "04" },
  { month: "May", value: "05" },
  { month: "Jun", value: "06" },
  { month: "Jul", value: "07" },
  { month: "Aug", value: "08" },
  { month: "Sep", value: "09" },
  { month: "Oct", value: "10" },
  { month: "Nov", value: "11" },
  { month: "Dec", value: "12" },
];

export default function ClaimHeader() {
  const { contractorId, employmentId, year, month } = useParams();
  const newClaimUrl = `/emp-ws/${contractorId}/employments/${employmentId}/payroll/claims/new`;
  const getMonth = MONTHS.find((item) => item.value === month)?.month;

  return (
    <div className="sticky top-[106px] z-20">
      <ClaimMonthsSegmentedControl
        baseUrl={`/emp-ws/${contractorId}/employments/${employmentId}/payroll/claims`}
      />
      <div className="space-y-2 border-b bg-white px-4 py-2 drop-shadow-md drop-shadow-gray-200">
        <HStack className="justify-between">
          <VStack className="gap-0">
            <h1 className="text-2xl font-semibold">
              Claims for {getMonth} {year}
            </h1>
          </VStack>

          <ProgressBarLink href={`${newClaimUrl}`}>
            <Button variant="primary">Create claims</Button>
          </ProgressBarLink>
        </HStack>
      </div>
    </div>
  );
}
