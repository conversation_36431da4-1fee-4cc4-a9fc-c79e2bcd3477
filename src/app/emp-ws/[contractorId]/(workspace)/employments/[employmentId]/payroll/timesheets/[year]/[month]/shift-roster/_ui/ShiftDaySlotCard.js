import cn from "@/lib/cn";
import Card from "@/ui/Card";
import VStack from "@/ui/VStack";
import { isEmpty } from "lodash";
import { usePathname } from "next/navigation";
import TimesheetDate from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDate";
import TimesheetTimeOff from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetTimeOff";
import TimesheetTimeSlot from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetTimeSlot";
import TimesheetDaySlotCardHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetDaySlotCardHeader";
import { isSameDay, lastDayOfMonth } from "date-fns";
import ShiftTimesheetDate from "./ShiftTimesheetDate";
import SunFilledIcon from "@/icons/filled/SunFilledIcon";
import SunHorizonFilledIcon from "@/icons/filled/SunHorizonFilledIcon";
import MoonFilledIcon from "@/icons/filled/MoonFilledIcon";
import OffFilledIcon from "@/icons/filled/OffFilledIcon";
import Tooltip from "@/ui/Tooltip";

export default function ShiftDaySlotCard({ index, item, lastDay }) {
  const {
    shift,
    isWorkingDay,
    dayOfWeek,
    date,
    halfDay,
    timeIn,
    timeOut,
    break: breakDuration,
    workingHours,
    overtimeTimeIn,
    overtimeTimeOut,
    overtimeBreak,
    overtimeHours,
    timeOff,
    remark,
    publicHoliday,
    leave,
    leaveType,
  } = item;

  const dayShift = shift === "Day";
  const noonShift = shift === "Noon";
  const nightShift = shift === "Night";

  const hasOT = !isEmpty(overtimeTimeIn);

  const wrapperClassName = "grid grid-cols-[80px_450px_1fr] gap-4";

  return (
    <Card
      className={cn("rounded-none p-2 pl-0", {
        // "bg-gray-50": !isWorkingDay && !hasOT,
        // "bg-amber-50": dayShift,
        // "bg-orange-50": noonShift,
        // "bg-indigo-50": nightShift,
        "rounded-t-lg": index === 0,
        "rounded-b-lg": lastDay,
      })}
    >
      <TimesheetDaySlotCardHeader index={index} className={wrapperClassName} />

      <div className={wrapperClassName}>
        <Tooltip content={shift} className="z-20">
          <ShiftTimesheetDate
            index={index}
            item={item}
            arrowClassName={cn({
              "bg-amber-50": dayShift,
              "bg-orange-50 text-orange-900": noonShift,
              "bg-indigo-50 text-indigo-900": nightShift,
            })}
            icon={
              shift && (
                <div className="w-[14px]">
                  <Icon shift={shift} />
                </div>
              )
            }
          />
        </Tooltip>

        <VStack className="my-auto gap-1">
          {publicHoliday && (
            <TimesheetTimeOff
              publicHoliday
              label="PH"
              content={publicHoliday}
            />
          )}

          {leave && (
            <TimesheetTimeOff leave label={leave} content={leaveType} />
          )}

          {timeOff === "AM" && <TimesheetTimeOff timeOff label={timeOff} />}

          {isWorkingDay && !leave && (
            <TimesheetTimeSlot
              timeIn={timeIn?.value}
              timeOut={timeOut?.value}
              breakDuration={breakDuration}
              totalHours={workingHours}
            />
          )}

          {timeOff === "PM" && <TimesheetTimeOff timeOff label={timeOff} />}

          {overtimeHours && (
            <TimesheetTimeSlot
              overtime
              timeIn={overtimeTimeIn.value}
              timeOut={overtimeTimeOut.value}
              breakDuration={overtimeBreak}
              totalHours={overtimeHours}
            />
          )}
        </VStack>
      </div>
    </Card>
  );
}

const Icon = ({ shift }) => {
  switch (shift) {
    case "Day":
    case "dayShift":
      return <SunFilledIcon color="#d97706" />;
    case "Noon":
    case "noonShift":
      return <SunHorizonFilledIcon color="#ea580c" />;
    case "Night":
    case "nightShift":
      return <MoonFilledIcon color="#4f46e5" />;
    case "Off":
    case "off":
      return <OffFilledIcon color="#6b7280" />;
    default:
      return null;
  }
};
