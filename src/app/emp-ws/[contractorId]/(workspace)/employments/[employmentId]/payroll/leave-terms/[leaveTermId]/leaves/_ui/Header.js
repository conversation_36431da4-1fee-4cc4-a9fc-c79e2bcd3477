"use client";

import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import LeaveTermSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/LeaveTermSelect";
import LeaveTermActionButton from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/LeaveTermActionButton";

export default function Header({ selectedTerm, leaveTerms }) {
  return (
    <HStack className="flex-nowrap justify-between gap-4">
      <VStack className="gap-0">
        <h1 className="text-xl font-semibold">Entitlements</h1>
        <p className="text-sm text-muted">
          Track your leave entitlements and history
        </p>
      </VStack>

      {/* Bigger screen */}
      <HStack className="hidden sm:flex">
        <LeaveTermSelect
          leaveTerms={leaveTerms}
          defaultValue={selectedTerm.slug}
        />
        <LeaveTermActionButton selectedTerm={selectedTerm} />
      </HStack>

      {/* Smaller screen */}
      <HStack className="flex justify-end-safe sm:hidden sm:justify-between">
        <LeaveTermSelect
          size="sm"
          leaveTerms={leaveTerms}
          defaultValue={selectedTerm.slug}
        />
        <LeaveTermActionButton size="sm" selectedTerm={selectedTerm} />
      </HStack>
    </HStack>
  );
}
