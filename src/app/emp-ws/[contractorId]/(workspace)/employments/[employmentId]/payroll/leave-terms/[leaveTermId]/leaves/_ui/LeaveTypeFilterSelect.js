"use client";

import { useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { useProgressBarLink } from "@/ui/ProgressBar";
import cn from "@/lib/cn";
import isNotEmpty from "@/lib/isNotEmpty";
import pluralize from "pluralize";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import { ChevronDownIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import Button from "@/ui/Button";
import Checkbox from "@/ui/Checkbox";

export default function LeaveTypeFilterSelect({
  leaveEnts,
  fetching,
  size = "sm",
}) {
  const ents = [
    { leaveType: "ALL", shortLeaveType: "All" },
    ...leaveEnts,
    // ...leaveEnts.filter((ent) => ent.used > 0),
  ];
  const handleProgressBarLink = useProgressBarLink({ scroll: false });
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const types = searchParams.get("type")
    ? searchParams.get("type").split(",")
    : ["ALL"];
  const [open, setOpen] = useState(false);

  const handleFilterChange = (e, type) => {
    const params = new URLSearchParams(searchParams.toString());
    let newTypes = [...types];

    if (type === "ALL") {
      if (newTypes.includes("ALL")) {
        newTypes = [];
      } else {
        newTypes = ["ALL"];
      }
    } else {
      if (newTypes.includes(type)) {
        newTypes = newTypes.filter((s) => s !== type);
      } else {
        newTypes = [...newTypes.filter((s) => s !== "ALL"), type];
      }
    }

    if (isNotEmpty(newTypes)) {
      params.set("type", newTypes.join(","));
    } else {
      params.delete("type");
    }

    params.set("page", 1);

    const newUrl = `${pathname}?${params.toString()}`;
    handleProgressBarLink(newUrl)(e);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger>
        <Button
          size={size}
          variant="secondary"
          className="w-[130px] justify-between"
          suffix={<ChevronDownIcon size={size === "sm" ? 18 : 20} />}
          outline
        >
          {types.includes("ALL")
            ? "All types"
            : pluralize("leave type", types.length, true)}
        </Button>
      </PopoverTrigger>

      <PopoverContent
        className={cn(
          "max-h-[300px] space-y-2 overflow-y-scroll p-2 sm:max-h-[400px]",
          {
            "space-y-1": size === "sm",
          },
        )}
        align="start"
      >
        {ents.map((ent) => {
          return (
            <Checkbox
              key={ent.leaveType}
              className={cn({ "size-4 rounded-sm text-sm": size === "sm" })}
              labelClassName="w-full pr-0"
              checked={types.includes(ent.leaveType)}
              onChange={(e) => handleFilterChange(e, ent.leaveType)}
              suffix={
                <HStack className="w-full items-center-safe justify-between gap-x-6">
                  <p>{ent.shortLeaveType}</p>
                  {ent.shortLeaveType !== "All" && (
                    <p className="text-sm text-muted">{ent.used}d</p>
                  )}
                </HStack>
              }
              suffixClassName="w-full"
              disabled={fetching}
            />
          );
        })}
      </PopoverContent>
    </Popover>
  );
}
