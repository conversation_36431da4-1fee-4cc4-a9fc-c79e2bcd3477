"use client";

import HStack from "@/ui/HStack";
import { SecondaryTabs, Tab } from "@/ui/SecondaryTabs";
import Select from "@/ui/Select";
import { format } from "date-fns";
import { useParams, usePathname, useSearchParams } from "next/navigation";

const MONTHS = [
  { month: "Jan", value: "1" },
  { month: "Feb", value: "2" },
  { month: "Mar", value: "3" },
  { month: "Apr", value: "4" },
  { month: "May", value: "5" },
  { month: "Jun", value: "6" },
  { month: "Jul", value: "7" },
  { month: "Aug", value: "8" },
  { month: "Sep", value: "9" },
  { month: "Oct", value: "10" },
  { month: "Nov", value: "11" },
  { month: "Dec", value: "12" },
];

export default function ClaimMonthSecondaryTabs() {
  const { contractorId, employmentId } = useParams();
  const claimUrl = `/emp-ws/${contractorId}/employments/${employmentId}/claims`;
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const getMonth = searchParams.get("m") || format(new Date(), "MMM");
  const pathIncludedClaims = pathname.includes("claims");

  if (!pathIncludedClaims) {
    return null;
  }

  return (
    <HStack className="gap-2 border-b px-2">
      <Select
        size="sm"
        wrapperClassName="border-none focus-within:ring-0 "
        className="pl-2"
      >
        <option>2025</option>
        <option>2024</option>
        <option>2023</option>
      </Select>

      <SecondaryTabs className="bg-background sticky top-[147px] flex-1 border-none px-0 pt-2">
        {MONTHS.map(({ month, value }, index) => {
          const selected = getMonth === month;

          return (
            <div key={index} className="mx-auto">
              <Tab href={`${claimUrl}?m=${month}`} activeTab={selected}>
                {month}
              </Tab>
            </div>
          );
        })}
      </SecondaryTabs>
    </HStack>
  );
}
