import AutocompleteWorkingHour from "@/components/autocomplete/AutocompleteWorkingHour";
import ForkSpoonIcon from "@/icons/emp/ForkKnifeIcon";
import HourglassIcon from "@/icons/emp/HourglassIcon";
import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import pluralize from "pluralize";
import { useFormContext } from "react-hook-form";
import { Check, ChevronDownIcon } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import { useState } from "react";
import workingHoursCalculator from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/workingHoursCalculator";
import { parse } from "date-fns";

const BREAK_DURATIONS = [
  { duration: "0 mins", value: 0 },
  { duration: "30 mins", value: 30 },
  { duration: "42 mins", value: 42 },
  { duration: "45 mins", value: 45 },
  { duration: "60 mins", value: 60 },
  { duration: "120 mins", value: 120 },
];

export default function EditTimesheetTimeSlot({ index, overtime = false }) {
  const { watch } = useFormContext();

  const timeIn = watch(
    `timesheetDays.[${index}].${overtime ? "overtimeTimeIn" : "timeIn"}`,
  );
  const timeOut = watch(
    `timesheetDays.[${index}].${overtime ? "overtimeTimeOut" : "timeOut"}`,
  );
  const breakDuration =
    watch(`timesheetDays.[${index}].${overtime ? "overtimeBreak" : "break"}`) ||
    0;

  const totalHours = workingHoursCalculator(
    timeIn?.value,
    timeOut?.value,
    breakDuration,
  );

  return (
    <HStack
      className={cn("tabular-num rounded-md", {
        "bg-amber-50 py-1.5": overtime,
      })}
    >
      <HStack className="px-2">
        <p
          className={cn("w-[24px] text-center text-sm opacity-80", {
            "text-amber-900": overtime,
          })}
        >
          {overtime && "OT"}
        </p>

        <div className="grid w-[375px] grid-cols-2 gap-2">
          <AutocompleteWorkingHour
            label="Time-in"
            name={`timesheetDays.[${index}].${overtime ? "overtimeTimeIn" : "timeIn"}`}
            defaultValue={timeIn}
            suffix={<div />}
            labelClassNameProp={overtime && "!bg-amber-50 rounded"}
          />

          <AutocompleteWorkingHour
            label="Time-out"
            name={`timesheetDays.[${index}].${overtime ? "overtimeTimeOut" : "timeOut"}`}
            defaultValue={timeOut}
            suffix={<div />}
            labelClassNameProp={overtime && "!bg-amber-50 rounded"}
          />
        </div>
      </HStack>

      <Break index={index} breakDuration={breakDuration} overtime={overtime} />

      <HStack className="w-[80px] gap-1">
        <HourglassIcon color="#000" />

        <div className="w-[60px]">
          {totalHours || 0}{" "}
          <span className="text-xs">{pluralize("hrs", totalHours)}</span>
        </div>
      </HStack>
    </HStack>
  );
}

const Break = ({ index, breakDuration, overtime }) => {
  const [open, setOpen] = useState(false);
  const { setValue } = useFormContext();

  if (overtime) {
    return (
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger>
          <button>
            <HStack className="w-[110px] cursor-pointer gap-1">
              <ForkSpoonIcon color="#000" />

              <HStack className="gap-1 rounded-md">
                <div>
                  {breakDuration}{" "}
                  <span className="text-xs">
                    {pluralize("min", breakDuration)}
                  </span>
                </div>

                <ChevronDownIcon size={18} />
              </HStack>
            </HStack>
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent sideOffset={0}>
          {BREAK_DURATIONS.map(({ duration, value }) => {
            const selected = breakDuration === value;

            return (
              <DropdownMenuItem
                key={duration}
                onClick={() => {
                  setValue(`timesheetDays.[${index}].overtimeBreak`, value);
                }}
              >
                <div className="flex items-center gap-2">
                  <CheckMark selected={selected} />
                  {duration}
                </div>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <HStack className="w-[110px] gap-1">
      <ForkSpoonIcon color="#000" />

      <div>
        {breakDuration}{" "}
        <span className="text-xs">{pluralize("min", breakDuration)}</span>
      </div>
    </HStack>
  );
};

const CheckMark = ({ selected }) => {
  return selected ? (
    <Check size={16} strokeWidth={2} />
  ) : (
    <div className="invisible w-4" />
  );
};
