import { getTotalHours } from "@/lib/timesheet/calculator";
import { addDays, differenceInMinutes, isDate, parse } from "date-fns";

export default function totalWorkingHours(day) {
  const {
    normalTimeIn: dayNormalTimeIn,
    normalTimeOut: dayNormalTimeOut,
    overtimeTimeIn: dayOvertimeTimeIn,
    overtimeTimeOut: dayOvertimeTimeOut,
    normalBreakDuration,
    overtimeBreakDuration,
    hasOvertime,
  } = day;

  const normalTimeIn = dayNormalTimeIn?.value || dayNormalTimeIn;
  const normalTimeOut = dayNormalTimeOut?.value || dayNormalTimeOut;
  const overtimeTimeIn = dayOvertimeTimeIn?.value || dayOvertimeTimeIn;
  const overtimeTimeOut = dayOvertimeTimeOut?.value || dayOvertimeTimeOut;

  const normalHours = getTotalHours(
    normalTimeIn,
    normalTimeOut,
    normalBreakDuration,
  );
  const overtimeHours = hasOvertime
    ? getTotalHours(overtimeTimeIn, overtimeTimeOut)
    : 0;

  return {
    normalHours,
    overtimeHours,
    overtimeBreakDuration,
  };
}
