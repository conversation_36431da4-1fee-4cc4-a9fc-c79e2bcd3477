import { TransportSelectOptions } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/_ui/ClaimsConstant";
import Select from "@/ui/Select";

export default function TransportSubTypeSelect({
  label = "Subtype",
  transportRate,
  ...props
}) {
  const OPTIONS = TransportSelectOptions(transportRate);

  return (
    <Select label={label} {...props}>
      <option value=""></option>
      {OPTIONS.map((option) => {
        return (
          <option key={option.label} value={option.value}>
            {option.label}
          </option>
        );
      })}
    </Select>
  );
}
