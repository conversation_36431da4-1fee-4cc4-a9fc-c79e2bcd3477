import apiQuery from "@/lib/apiQuery";
import Header from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/Header";
import LeaveEntTable from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/LeaveEntTable";
import LeaveHistoryCard from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/LeaveHistoryCard";

const QUERY = `
  query empLeaveTermsAndLeaveEntitlements($employmentId: ID!, $leaveTermId: ID!) {
    empLeaveTerms(employmentId: $employmentId) {
      slug
      contractorResourceUrl
      startDate
      endDate
      displayPeriod
      displayPeriodShort: displayPeriod(format: "%-d/%-m/%Y")
      status
    }

    empLeaveEntitlements(employmentId: $employmentId, leaveTermId: $leaveTermId) {
      leaveType
      shortLeaveType
      longLeaveType
      totalEntitled
      used
      balance
      unpaid
    }
  }
`;

export async function generateMetadata() {
  return {
    title: "My Leaves",
  };
}

export default async function Page({ params, searchParams }) {
  const { employmentId, leaveTermId } = await params;
  const res = await apiQuery(QUERY, { employmentId, leaveTermId });
  const leaveTerms = res.data.empLeaveTerms;
  const leaveEnts = res.data.empLeaveEntitlements;
  const selectedTerm = leaveTerms.find((t) => t.slug === leaveTermId);

  return (
    <div className="space-y-4 p-4">
      <Header selectedTerm={selectedTerm} leaveTerms={leaveTerms} />

      <LeaveEntTable leaveEnts={leaveEnts} />

      <h2 className="text-xl font-semibold">Leave History</h2>

      <LeaveHistoryCard
        selectedTerm={selectedTerm}
        leaveEnts={leaveEnts}
        params={params}
        searchParams={searchParams}
      />
    </div>
  );
}
