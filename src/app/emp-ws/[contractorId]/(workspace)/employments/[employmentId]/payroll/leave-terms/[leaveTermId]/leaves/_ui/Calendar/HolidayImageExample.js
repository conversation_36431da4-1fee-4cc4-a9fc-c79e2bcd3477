import PublicHolidayIcon, { getHolidayImageByEvent } from "./PublicHolidayIcon";

// Your holiday data array
const holidayData = [
  { date: "2024-01-01", event: "New Year's Day" },
  { date: "2024-02-10", event: "Chinese New Year Day 1" },
  { date: "2024-02-11", event: "Chinese New Year Day 2" },
  { date: "2024-02-12", event: "Substitute Holiday (CNY)" },
  { date: "2024-03-29", event: "Good Friday" },
  { date: "2024-04-10", event: "Hari Raya Puasa" },
  { date: "2024-05-01", event: "Labour Day" },
  { date: "2024-05-22", event: "Vesak Day" },
  { date: "2024-06-17", event: "Hari Raya Haji" },
  { date: "2024-08-09", event: "National Day" },
  { date: "2024-10-31", event: "Deepavali" },
  { date: "2024-12-25", event: "Christmas Day" },

  { date: "2025-01-01", event: "New Year's Day" },
  { date: "2025-01-29", event: "Chinese New Year Day 1" },
  { date: "2025-01-30", event: "Chinese New Year Day 2" },
  { date: "2025-03-31", event: "<PERSON>a Puasa" },
  { date: "2025-04-18", event: "Good Friday" },
  { date: "2025-05-01", event: "Labour Day" },
  { date: "2025-05-12", event: "Vesak Day" },
  { date: "2025-06-07", event: "Hari Raya Haji" },
  { date: "2025-08-09", event: "National Day" },
  { date: "2025-10-20", event: "Deepavali" },
  { date: "2025-12-25", event: "Christmas Day" },

  { date: "2026-01-01", event: "New Year's Day" },
  { date: "2026-02-17", event: "Chinese New Year Day 1" },
  { date: "2026-02-18", event: "Chinese New Year Day 2" },
  { date: "2026-03-21", event: "Hari Raya Puasa" },
  { date: "2026-04-03", event: "Good Friday" },
  { date: "2026-05-01", event: "Labour Day" },
  { date: "2026-05-27", event: "Hari Raya Haji" },
  { date: "2026-05-31", event: "Vesak Day" },
  { date: "2026-08-09", event: "National Day" },
  { date: "2026-11-08", event: "Deepavali" },
  { date: "2026-12-25", event: "Christmas Day" }
];

export default function HolidayImageExample() {
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Holiday Icons Example</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {holidayData.map((holiday, index) => (
          <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
            <PublicHolidayIcon event={holiday.event} />
            <div>
              <div className="font-medium">{holiday.event}</div>
              <div className="text-sm text-gray-500">{holiday.date}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Example function to get image for a specific holiday
export function getHolidayImageForEvent(eventName) {
  return getHolidayImageByEvent(eventName);
}
