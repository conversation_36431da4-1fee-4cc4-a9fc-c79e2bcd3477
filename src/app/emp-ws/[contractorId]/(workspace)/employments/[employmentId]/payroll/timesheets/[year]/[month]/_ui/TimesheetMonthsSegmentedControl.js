"use client";

import HStack from "@/ui/HStack";
import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";
import Select from "@/ui/Select";
import { format } from "date-fns";
import { useParams, usePathname } from "next/navigation";

const MONTHS = [
  { month: "Jan", value: "01" },
  { month: "Feb", value: "02" },
  { month: "Mar", value: "03" },
  { month: "Apr", value: "04" },
  { month: "May", value: "05" },
  { month: "Jun", value: "06" },
  { month: "Jul", value: "07" },
  { month: "Aug", value: "08" },
  { month: "Sep", value: "09" },
  { month: "Oct", value: "10" },
  { month: "Nov", value: "11" },
  { month: "Dec", value: "12" },
];

export default function TimesheetMonthsSegmentedControl({ baseUrl }) {
  const pathname = usePathname();
  const { year, month } = useParams();
  const getMonth = month || format(new Date(), "MM");

  const timesheetUrl = `${baseUrl}/${year}/${getMonth}`;

  return (
    <div className="flex w-full justify-center bg-white p-2">
      <Select
        className="w-fit pl-2"
        wrapperClassName="border-none focus-within:ring-0 "
      >
        <option>2025</option>
        <option>2024</option>
        <option>2023</option>
      </Select>

      <div className="overflow-hidden overflow-x-scroll">
        <SegmentedControlRoot
          value={timesheetUrl}
          style={{ "--segmented-control-border-radius": "9999px" }}
        >
          {MONTHS.map(({ month, value }, index) => {
            return (
              <SegmentedControlItem
                key={index}
                href={`${baseUrl}/${year}/${value}`}
              >
                <HStack className="uppercase">{month}</HStack>
              </SegmentedControlItem>
            );
          })}
        </SegmentedControlRoot>
      </div>
    </div>
  );
}
