import { OTHERS_SELECT_OPTIONS } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/_ui/ClaimsConstant";
import Select from "@/ui/Select";

export default function OthersSubTypeSelect({ label = "Subtype", ...props }) {
  return (
    <Select label={label} {...props}>
      <option value=""></option>
      {OTHERS_SELECT_OPTIONS.map((option) => (
        <option key={option} value={option}>
          {option}
        </option>
      ))}
    </Select>
  );
}
