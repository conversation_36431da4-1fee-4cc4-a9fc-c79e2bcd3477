import cn from "@/lib/cn";
import Button from "@/ui/Button";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import { useState } from "react";
import AutocompleteWorkingHour from "@/components/autocomplete/AutocompleteWorkingHour";
import { useFormContext } from "react-hook-form";

export default function TimesheetDaySlotCardHeader({
  index,
  className,
  edit = false,
}) {
  if (index !== 0) {
    return null;
  }

  return (
    <VStack>
      <div className={cn("pl-2", { hidden: !edit })}>
        <HStack className="flex w-full justify-end border-b pb-2">
          {/* <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger>
              <Button variant="primary" size="sm">
                Fill all
              </Button>
            </PopoverTrigger>

            <PopoverContent sideOffset={0}>
              <HStack className="p-4">
                <div className="grid w-[375px] grid-cols-2 gap-2">
                  <AutocompleteWorkingHour
                    label="Time-in"
                    name={`timeIn`}
                    defaultValue={getTimeIn}
                    suffix={<div />}
                  />

                  <AutocompleteWorkingHour
                    label="Time-out"
                    name={`timeOut`}
                    defaultValue={getTimeOut}
                    suffix={<div />}
                  />
                </div>

                <Button
                  variant="ghost"
                  onClick={() => {
                    if (getTimeIn && getTimeOut) {
                      setOpen(false);
                      // setValue;
                    }
                  }}
                >
                  Save
                </Button>
              </HStack>
            </PopoverContent>
          </Popover> */}

          <Button variant="ghost" size="sm">
            Clear all
          </Button>
        </HStack>
      </div>

      <div className={cn("text-muted pb-4 text-sm", className)}>
        <div className="pl-3">Date</div>

        <HStack>
          <HStack className="px-2">
            <div className="w-[24px]" />
            <div className={edit ? "w-[375px]" : "w-[200px]"}>
              Working hours
            </div>
          </HStack>

          <div className={cn("w-[80px] text-left", { "w-[110px]": edit })}>
            Break
          </div>

          <div className="w-[80px] text-left">Total hrs</div>
        </HStack>

        <div>Remark</div>
      </div>
    </VStack>
  );
}
