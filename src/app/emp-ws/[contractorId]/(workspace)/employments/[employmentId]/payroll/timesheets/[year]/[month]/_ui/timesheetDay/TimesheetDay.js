import cn from "@/lib/cn";
import Card from "@/ui/Card";
import VStack from "@/ui/VStack";
import { validateWorkingDay } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";
import Overtime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/Overtime";
import DateArrow from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/DateArrow";
import NormalTime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/NormalTime";
import RemarkPopover from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/RemarkPopover";
import TimesheetDayHeader from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetDay/TimesheetDayHeader";

export default function TimesheetDay({ index, className, timesheetDay }) {
  const isWorkingDay = validateWorkingDay(
    timesheetDay.normalTimeIn,
    timesheetDay.normalTimeOut,
  );

  return (
    <Card
      key={`${index}_${timesheetDay.id}`}
      className={cn(
        "rounded-none p-2 pl-0 first:rounded-t-2xl last:rounded-b-2xl",
        {
          "bg-gray-50": !isWorkingDay,
        },
      )}
    >
      <TimesheetDayHeader index={index} className={className} />

      <div className={className}>
        <DateArrow timesheetDay={timesheetDay} />

        <VStack className="tabular-num my-auto gap-1">
          {/* Public holiday place here */}
          {/* <TimeOff publicHoliday label="PH" content={publicHolidayRemark} /> */}

          {/* Leave */}
          {/* <TimeOff leave label={leaveRemark} content={leaveRemark} /> */}

          {/* AM timeOff */}
          {/* {timeOff === "AM" && <TimeOff timeOff label={timeOff} />} */}

          <NormalTime timesheetDay={timesheetDay} />

          {/* PM timeOff */}
          {/* {timeOff === "AM" && <TimeOff timeOff label={timeOff} />} */}

          <Overtime timesheetDay={timesheetDay} />
        </VStack>

        <RemarkPopover contractorRemark={timesheetDay.contractorRemark} />
      </div>
    </Card>
  );
}
