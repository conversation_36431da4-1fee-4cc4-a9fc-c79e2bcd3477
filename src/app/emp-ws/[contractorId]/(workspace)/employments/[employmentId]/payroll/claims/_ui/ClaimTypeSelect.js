"use client";

import { FormRow } from "@/ui/Form";
import Select from "@/ui/Select";
import { useFormContext } from "react-hook-form";

const claimTypes = [
  { type: "Meal" },
  { type: "Medical" },
  { type: "Communication" },
  { type: "Transport" },
  { type: "Work Related Payment" },
  { type: "Others" },
  { type: "Not Applicable" },
];

const subClaimTypes = {
  Meal: [
    { sub: "Meal (PH)" },
    { sub: "Meal (Weekday)" },
    { sub: "Meal (Weekend)" },
  ],
  Medical: [{ sub: "Medical" }, { sub: "Dental" }],
  Communication: [{ sub: "Mobile" }, { sub: "Mobile Broadband" }],
  Transport: [
    { sub: "ERP" },
    { sub: "Car Park" },
    { sub: "Public Transport" },
    { sub: "Car Mileage" },
    { sub: "Bike Mileage" },
  ],
  "Work Related Payment": [
    { sub: "Duty" },
    { sub: "Overtime" },
    { sub: "Recall" },
    { sub: "Shift" },
    { sub: "Standby" },
  ],
  Others: [
    { sub: "Boat Trip (Horsberg)" },
    { sub: "Boat Trip (Lighthouse)" },
    { sub: "Boat Trip (Sudong Island)" },
    { sub: "Boat Trip (Tekong Island)" },
    { sub: "Hardship" },
    { sub: "Shipboard/Height" },
    { sub: "IWF Essential OPS" },
    { sub: "Offshore Site Testing" },
    { sub: "Sea Trial" },
    { sub: "Ops Sentinel" },
    { sub: "Travel" },
    { sub: "Yard" },
    { sub: "Miscellaneous" },
    { sub: "Sea Exercise (Weekdays)" },
    { sub: "Sea Exercise (Weekends & PH)" },
    { sub: "Tower Height" },
    { sub: "On Board Ship (Testing)" },
    { sub: "Sub Safe" },
    { sub: "Overnight" },
    { sub: "USV Steersman" },
    { sub: "MRT" },
    { sub: "DLP/Warranty Standby" },
    { sub: "Flexi-Benefits" },
  ],
  "Not Applicable": [{ sub: "Not Applicable" }],
};

export default function ClaimTypeSelect({ showMain = false, type }) {
  const {
    register,
    watch,
    formState: { errors },
  } = useFormContext();

  const watchClaimType = type || "Medical";
  const chosenSubClaimType = subClaimTypes[watchClaimType] || [];

  return (
    <FormRow>
      {showMain && (
        <Select label="Claim type" {...register("claimType")}>
          <option value=""></option>
          {claimTypes.map((claimType) => (
            <option key={claimType.type} value={claimType.type}>
              {claimType.type}
            </option>
          ))}
        </Select>
      )}

      <Select
        label="Subtype"
        {...register("subClaimType", { required: "Subtype is required!" })}
        error={errors?.claims?.subClaimType}
      >
        <option value=""></option>
        {chosenSubClaimType?.map((subClaimType) => (
          <option key={subClaimType.sub} value={subClaimType.sub}>
            {subClaimType.sub}
          </option>
        ))}
      </Select>
    </FormRow>
  );
}
