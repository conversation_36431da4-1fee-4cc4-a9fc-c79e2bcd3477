import ForkSpoonIcon from "@/icons/emp/ForkKnifeIcon";
import HourglassIcon from "@/icons/emp/HourglassIcon";
import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import pluralize from "pluralize";

export default function TimesheetTime({
  overtime = false,
  timeIn,
  timeOut,
  breakDuration,
  totalHours,
}) {
  return (
    <HStack
      className={cn("tabular-num rounded-md", {
        "bg-amber-50 py-1.5 text-amber-900": overtime,
      })}
    >
      <HStack className="px-2">
        <p className={cn("w-[24px] text-center text-sm opacity-80")}>
          {overtime && "OT"}
        </p>

        <div className="w-[200px]">
          {timeIn || "NA"} &mdash; {timeOut || "NA"}
        </div>
      </HStack>

      <HStack className="w-[80px] gap-1">
        <ForkSpoonIcon color={overtime ? "#78350f" : "#000"} />

        <HStack className="gap-1">
          <div>
            {breakDuration || 0}{" "}
            <span className="text-xs">{pluralize("mins", breakDuration)}</span>
          </div>
        </HStack>
      </HStack>

      <HStack className="w-[80px] gap-1">
        <HourglassIcon color={overtime ? "#78350f" : "#000"} />

        <div>
          {totalHours || 0}{" "}
          <span className="text-xs">{pluralize("hrs", totalHours)}</span>
        </div>
      </HStack>
    </HStack>
  );
}
