import { Check, ChevronDownIcon, PartyPopperIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import { isEmpty } from "lodash";
import { useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import { isHalfDayLeave } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/validator";

export default function EditTimeOff({
  index,
  content = "Time-off",
  timesheetDay = {},
}) {
  const [open, setOpen] = useState(false);

  const { control, setValue, trigger } = useFormContext();
  const watchTimeOff = useWatch({
    control,
    name: `timesheetDays.[${index}].timeOff`,
  });
  const { leave } = timesheetDay;
  const hasHalfDayLeave = isHalfDayLeave(leave);
  const timeOffMeridians = hasHalfDayLeave
    ? ["AM", "PM"].filter((meridian) => meridian !== leave.details)
    : ["AM", "PM", "Full"];

  const onItemClick = (meridian) => {
    const selected = watchTimeOff === meridian;

    !selected && setValue(`timesheetDays.[${index}].timeOff`, meridian);

    // If Full day time-off is selected, clear normal time fields
    if (meridian === "Full") {
      setValue(`timesheetDays.[${index}].normalTimeIn`, "");
      setValue(`timesheetDays.[${index}].normalTimeOut`, "");
      setValue(`timesheetDays.[${index}].normalBreakDuration`, 0);
    }

    // setTimeout(() => {
    //   trigger([
    //     `timesheetDays.[${index}].normalTimeIn`,
    //     `timesheetDays.[${index}].normalTimeOut`,
    //     `timesheetDays.[${index}].normalBreakDuration`,
    //   ]);
    // }, 100);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger>
        <button className="outline-none" type="button">
          <TimeOffRow label={watchTimeOff} content={content} />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent sideOffset={0}>
        {timeOffMeridians.map((meridian) => {
          const selected = watchTimeOff === meridian;

          return (
            <DropdownMenuItem
              key={meridian}
              onClick={() => {
                onItemClick(meridian);
              }}
            >
              <div className="flex items-center gap-2">
                <CheckMark selected={selected} />
                {meridian}
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

const TimeOffRow = ({ label, content }) => {
  const Content = () => {
    if (isEmpty(content)) {
      return null;
    }

    // if (publicHoliday) {
    //   return (
    //     <HStack className="flex-1 justify-between">
    //       <p>{content}</p>

    //       {publicHoliday && <PartyPopperIcon size={16} />}
    //     </HStack>
    //   );
    // }

    return content;
  };

  return (
    <HStack className="tabular-num cursor-pointer rounded-md bg-gray-100 px-2">
      <p className="w-[24px] text-sm opacity-80">{label}</p>
      {content}
      <ChevronDownIcon size={18} />
    </HStack>
  );
};

const CheckMark = ({ selected }) => {
  return selected ? (
    <Check size={16} strokeWidth={2} />
  ) : (
    <div className="invisible w-4" />
  );
};
