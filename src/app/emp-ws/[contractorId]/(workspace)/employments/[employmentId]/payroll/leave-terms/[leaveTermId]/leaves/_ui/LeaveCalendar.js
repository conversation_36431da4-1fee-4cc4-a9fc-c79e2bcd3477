import apiQuery from "@/lib/apiQuery";
import { format, parseISO } from "date-fns";
import isNotEmpty from "@/lib/isNotEmpty";
import Calendar from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/Calendar/Calendar";

const QUERY = `
  query empLeaves($employmentId: ID!, $leaveTermId: ID!, $leaveTypes: [String!], $statuses: [String!], $workMonthY: Int, $workMonthM: Int, $startDate: DatePickerScalar, $endDate: DatePickerScalar) {
    empLeaves(employmentId: $employmentId, leaveTermId: $leaveTermId, leaveTypes: $leaveTypes, statuses: $statuses, workMonthY: $workMonthY, workMonthM: $workMonthM, page: 1, perPage: 1000) {
      nodes {
        id
        fmId
        contractorResourceUrl

        leaveType
        shortLeaveType
        longLeaveType
        leaveDate
        dayPeriod
        dayPeriodLabel
        entitled

        mcSource
        contractorRemark

        status
        statusUpdatedAt
      }
    }

    empPublicHolidays(startDate: $startDate, endDate: $endDate) {
      date
      event
    }
  }
`;

export default async function LeaveCalendar({
  selectedTerm,
  params,
  searchParams,
  debug = false,
}) {
  const { employmentId, leaveTermId } = await params;
  let { type, status, year, month, ...restSearchParams } = await searchParams;
  const leaveTypes = isNotEmpty(type)
    ? type.split(",").filter((t) => t !== "ALL")
    : [];
  const statuses = isNotEmpty(status)
    ? status.split(",").filter((s) => s !== "ALL")
    : [];
  const workMonthY = Number(year || new Date().getFullYear());
  const workMonthM = Number(month || new Date().getMonth() + 1);
  const initialDate = new Date(workMonthY, workMonthM - 1, 1);

  const res = await apiQuery(QUERY, {
    employmentId,
    leaveTermId,
    leaveTypes,
    statuses,
    workMonthY,
    workMonthM,
    startDate: format(parseISO(selectedTerm.startDate), "dd/MM/yyyy"),
    endDate: format(parseISO(selectedTerm.endDate), "dd/MM/yyyy"),
  });

  const leaves = res.data.empLeaves.nodes;
  const holidays = res.data.empPublicHolidays;

  return (
    <>
      <Calendar
        initialDate={initialDate}
        selectedTerm={selectedTerm}
        leaves={leaves}
        holidays={holidays}
        wrapperClassName="mx-auto mt-8 max-w-2xl"
      />
      {debug && (
        <pre className="mx-auto mt-4 max-w-2xl overflow-auto text-sm whitespace-pre-wrap text-muted">
          {JSON.stringify(
            {
              query: {
                employmentId,
                leaveTermId,
                leaveTypes,
                statuses,
                workMonthY,
                workMonthM,
              },
              searchParams: {
                type,
                status,
                year,
                month,
                ...restSearchParams,
              },
            },
            null,
            2,
          )}
          <br />
          <br />
          {JSON.stringify(leaves, null, 2)}
        </pre>
      )}
    </>
  );
}
