"use client";

import { useRef } from "react";
import { isSameDay, parseISO } from "date-fns";
import dayGridPlugin from "@fullcalendar/daygrid";
import cn from "@/lib/cn";
import isNotEmpty from "@/lib/isNotEmpty";
import HStack from "@/ui/HStack";
import Tooltip from "@/ui/Tooltip";
import LeaveIcon from "@/icons/leaves/icons";
import FullCalendar from "@fullcalendar/react";
import NavigationBar from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/Calendar/NavigationBar";
import PublicHolidayIcon from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/Calendar/PublicHolidayIcon";

const LEAVE_TYPE_COLORS = {
  AL: {
    backgroundColor: "#22c55e",
    textColor: "#fff",
  },
  ML: {
    backgroundColor: "#f43f5e",
    textColor: "#fff",
  },
  HOSL: {
    backgroundColor: "#b91c1c",
    textColor: "#fff",
  },
  WML: {
    backgroundColor: "#f43f5e",
    textColor: "#fff",
  },
  WHOSL: {
    backgroundColor: "#b91c1c",
    textColor: "#fff",
  },
  RL: {
    backgroundColor: "#facc15",
    textColor: "#fff",
  },
  BDL: {
    backgroundColor: "#facc15",
    textColor: "#fff",
  },
  MRL: {
    backgroundColor: "#facc15",
    textColor: "#fff",
  },
  MTNL: {
    backgroundColor: "#eab308",
    textColor: "#fff",
  },
  PL: {
    backgroundColor: "#eab308",
    textColor: "#fff",
  },
  SPL: {
    backgroundColor: "#eab308",
    textColor: "#fff",
  },
  CCL: {
    backgroundColor: "#eab308",
    textColor: "#fff",
  },
  ECCL: {
    backgroundColor: "#eab308",
    textColor: "#fff",
  },
  FCL: {
    backgroundColor: "#eab308",
    textColor: "#fff",
  },
  CPL: {
    backgroundColor: "#1f2937",
    textColor: "#fff",
  },
  ICT: {
    backgroundColor: "#166534",
    textColor: "#fff",
  },
};

const leaveTypeColor = (leaveType) => {
  switch (leaveType) {
    case "AL":
      return "bg-green-50 text-green-900";
    case "ML":
    case "HOSL":
    case "WML":
    case "WHOSL":
      return "bg-blue-50 text-blue-900";
    case "RL":
    case "BDL":
    case "MRL":
      return "bg-yellow-50 text-yellow-900";
    case "MTNL":
    case "PL":
    case "SPL":
    case "CCL":
    case "ECCL":
    case "FCL":
      return "bg-orange-50 text-orange-900";
    case "CPL":
      return "bg-gray-50 text-gray-900";
    case "ICT":
      return "bg-green-50 text-green-900";
    default:
      return "bg-gray-50 text-gray-900";
  }
};

export default function Calendar({
  initialDate,
  selectedTerm,
  leaves,
  wrapperClassName,
  holidays = [],
}) {
  const ref = useRef(null);
  const events = leaves.map((leave, index) => ({
    // Fullcalendar will sort by title alphabetically
    // an AL PM leave should be at bottom if there is AM leave, e.g. FCL
    // But Fullcalendar will put AL on top of FCL if we give leave type only
    // Therefore I need to stringify by index first to make it in order:
    // 001_AL
    // 002_FCL
    title: `${index.toString().padStart(3, "0")}_${leave.leaveType}`,
    date: leave.leaveDate,
    backgroundColor: LEAVE_TYPE_COLORS[leave.leaveType].backgroundColor,
    borderColor: LEAVE_TYPE_COLORS[leave.leaveType].backgroundColor,
    leave,
  }));

  const handleMonthChange = (date) => {
    if (ref.current) {
      ref.current.getApi().gotoDate(date);
    }
  };

  const renderDayCellContent = (dayInfo) => {
    const { date, dayNumberText, isToday } = dayInfo;
    const holiday = holidays.find((holiday) =>
      isSameDay(parseISO(holiday.date), date),
    );

    return (
      <HStack className="justify-between">
        {isNotEmpty(holiday) && (
          <Tooltip
            content={holiday.event}
            sideOffset={5}
            className="px-2 py-0.5 text-xs"
          >
            <PublicHolidayIcon event={holiday.event} />
          </Tooltip>
        )}
        <span
          className={cn("ml-auto text-sm", {
            "font-semibold": isToday,
          })}
        >
          {dayNumberText}
        </span>
      </HStack>
    );
  };

  const renderEventBackgroundColor = (eventInfo) => {
    return leaveTypeColor(eventInfo.extendedProps.leave.leaveType);
  };

  const renderEventContent = (eventInfo) => {
    const {
      event: {
        extendedProps: {
          leave: {
            leaveType,
            shortLeaveType,
            dayPeriod,
            dayPeriodLabel,
            entitled,
          },
        },
      },
    } = eventInfo;

    return (
      <Tooltip
        content={`${shortLeaveType} ${dayPeriodLabel} ${entitled ? "entitled" : "unpaid"}`}
        className="px-2 py-0.5 text-xs"
        side={dayPeriod === "AM" ? "top" : "bottom"}
        sideOffset={5}
      >
        <HStack className="flex-nowrap justify-between px-1">
          <LeaveIcon
            type={leaveType}
            size={16}
            color={LEAVE_TYPE_COLORS[leaveType].textColor}
            strokeWidth={leaveType === "MTNL" ? 1.5 : 2}
          />
          <p className="overflow-auto text-sm text-ellipsis">{leaveType}</p>
        </HStack>
      </Tooltip>
    );
  };

  return (
    <div className={cn("space-y-2", wrapperClassName)}>
      <NavigationBar selectedTerm={selectedTerm} onChange={handleMonthChange} />
      <FullCalendar
        ref={ref}
        initialDate={initialDate}
        firstDay={1}
        plugins={[dayGridPlugin]}
        events={events}
        dayHeaderClassNames="text-sm bg-gray-50 font-normal text-muted dark:bg-gray-600 dark:text-white"
        dayCellClassNames="w-full"
        dayCellContent={renderDayCellContent}
        eventBackgroundColor={renderEventBackgroundColor}
        eventContent={renderEventContent}
        headerToolbar={false}
      />
    </div>
  );
}
