"use client";

import { useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { useProgressBarLink } from "@/ui/ProgressBar";
import cn from "@/lib/cn";
import isNotEmpty from "@/lib/isNotEmpty";
import pluralize from "pluralize";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import { ChevronDownIcon } from "lucide-react";
import Button from "@/ui/Button";
import Checkbox from "@/ui/Checkbox";

const STATUSES = [
  "ALL",
  "PENDING",
  "CANCELLING",
  "VERIFYING",
  "APPROVED",
  "REJECTED",
  "CANCELLED",
];

export default function StatusFilterSelect({ fetching, size = "sm" }) {
  const handleProgressBarLink = useProgressBarLink({ scroll: false });
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const statuses = searchParams.get("status")
    ? searchParams.get("status").split(",")
    : ["ALL"];
  const [open, setOpen] = useState(false);

  const handleFilterChange = (e, status) => {
    const params = new URLSearchParams(searchParams.toString());
    let newStatuses = [...statuses];

    if (status === "ALL") {
      if (newStatuses.includes("ALL")) {
        newStatuses = [];
      } else {
        newStatuses = ["ALL"];
      }
    } else {
      if (newStatuses.includes(status)) {
        newStatuses = newStatuses.filter((s) => s !== status);
      } else {
        newStatuses = [...newStatuses.filter((s) => s !== "ALL"), status];
      }
    }

    if (isNotEmpty(newStatuses)) {
      params.set("status", newStatuses.join(","));
    } else {
      params.delete("status");
    }

    params.set("page", 1);

    const newUrl = `${pathname}?${params.toString()}`;
    handleProgressBarLink(newUrl)(e);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger>
        <Button
          size={size}
          variant="secondary"
          className={cn("w-[130px] justify-between", {
            "w-[110px]": size === "sm",
          })}
          suffix={<ChevronDownIcon size={size === "sm" ? 18 : 20} />}
          outline
        >
          {statuses.includes("ALL")
            ? "All status"
            : pluralize("status", statuses.length, true)}
        </Button>
      </PopoverTrigger>

      <PopoverContent
        className={cn("space-y-2 p-2", {
          "space-y-1": size === "sm",
        })}
        align="start"
      >
        {STATUSES.map((status) => {
          return (
            <Checkbox
              key={status}
              className={cn({ "size-4 rounded-sm text-sm": size === "sm" })}
              checked={statuses.includes(status)}
              onChange={(e) => handleFilterChange(e, status)}
              suffix={status}
              disabled={fetching}
            />
          );
        })}
      </PopoverContent>
    </Popover>
  );
}
