import { CREATE_CLAIM_MUTATION } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/_ui/ClaimsConstant";
import TransportGoogleMap from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/TransportGoogleMap";
import TransportSubTypeSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/TransportSubTypeSelect";
import UploadClaimReceipt from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/UploadClaimReceipt";
import AmountInput from "@/components/input/AmountInput";
import { currency } from "@/formatters/numeric";
import useAction from "@/hooks/useAction";
import TransportIcon from "@/icons/emp/claim-types/TransportIcon";
import boundAction from "@/lib/boundAction";
import Button from "@/ui/Button";
import Card from "@/ui/Card";
import DatePickerInput from "@/ui/datetime/DatePickerInput";
import { Form, FormRow, FormSection, showFormErrors } from "@/ui/Form";
import HStack from "@/ui/HStack";
import Input from "@/ui/Input";
import { parseISO } from "date-fns";
import { CirclePlusIcon } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { use, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export default function TransportClaimForm({ fetchClaimPromise }) {
  const { employmentId } = useParams();
  const router = useRouter();
  const res = use(fetchClaimPromise);
  const claimsBalance = res.data?.empClaimsBalance?.claimBalances;
  const transportRate = res.data?.empClaimsBalance?.transportLimits;
  const result = claimsBalance?.find(
    (item) => item.claimType === "Transport",
  )?.limit;
  const minDate = parseISO(res.data?.empClaimsBalance?.minClaimableDate);
  const maxDate = parseISO(res.data?.empClaimsBalance?.maxClaimableDate);

  const [formData, setFormData] = useState(null);
  // const form = useForm();
  const form = useForm({
    defaultValues: {
      claimType: "Transport",
      subClaimType: "ERP",
    },
  });

  const {
    register,
    reset,
    setError,
    watch,
    formState: { errors },
  } = form;

  const [create, pending] = useAction(boundAction(CREATE_CLAIM_MUTATION), {
    onSuccess: () => {
      toast.success("Claim created successfully");
      router.refresh();
      reset({
        incurredDate: "",
        amount: "",
        description: "",
        transportStartTime: "",
        transportEndTime: "",
        receipt: null,
      });
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    let rate = null;

    if (input.subClaimType === "Car Mileage") rate = Number(transportRate?.car);
    if (input.subClaimType === "Motorbike Mileage")
      rate = Number(transportRate?.bike);

    const submissionInput = { transportRate: rate, ...input };

    setFormData(submissionInput);
    create({ employmentId, input: submissionInput });
  };

  register("receipt", {
    required: true,
  });

  const showBalance = currency(result?.limit);
  const hasErrors = Object.keys(errors).length > 0;
  const isEvCharging = watch("subClaimType") === "EV Charging";
  const isMileage =
    watch("subClaimType") === "Car Mileage" ||
    watch("subClaimType") === "Motorbike Mileage";

  return (
    <Form
      form={form}
      onSubmit={onSubmit}
      formData={formData}
      className="p-2"
      debug
    >
      <Card className="mx-auto max-w-[800px] space-y-4">
        <HStack className="mb-8 justify-between">
          <HStack asChild className="font-semibold">
            <h1>
              <TransportIcon />
              Transport
            </h1>
          </HStack>
          {showBalance && <span>Amount claimable: {showBalance}</span>}
        </HStack>

        <div className="mx-auto max-w-[600px] space-y-4">
          <FormSection>
            <FormRow colsCount={2}>
              <TransportSubTypeSelect
                transportRate={transportRate}
                {...register("subClaimType", {
                  required: "Subtype is required!",
                })}
                error={errors.subClaimType}
              />
              {!isMileage && (
                <AmountInput
                  min={0.1}
                  label="Amount"
                  {...register("amount", {
                    required: "Amount is required!",
                  })}
                  error={errors.amount}
                />
              )}
            </FormRow>

            <FormRow colsCount={2}>
              <DatePickerInput
                label="Incurred date"
                name="incurredDate"
                rules={{ required: "Incurred date is required!" }}
                shortcuts={false}
                minDate={minDate}
                maxDate={maxDate}
              />
            </FormRow>

            {!isEvCharging && (
              <FormRow>
                <Input
                  label="Start time"
                  {...register("transportStartTime", {
                    required: "Start time is required!",
                    pattern: {
                      value: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
                      message: "Time period must be in HH:MM format!",
                    },
                  })}
                  error={errors.transportStartTime}
                />
                <Input
                  label="End time"
                  {...register("transportEndTime", {
                    required: "End time is required!",
                    pattern: {
                      value: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
                      message: "Time period must be in HH:MM format!",
                    },
                  })}
                  error={errors.transportEndTime}
                />
                <span className="my-auto text-sm text-gray-300">
                  24-hour format only
                </span>
              </FormRow>
            )}

            <FormRow>
              <Input
                label="Description"
                {...register("description", {
                  required: "Description is required!",
                })}
                error={errors.description}
              />
            </FormRow>
          </FormSection>

          {isMileage && <TransportGoogleMap transportRate={transportRate} />}

          <FormSection
            title="Upload receipt"
            description="Accept images, PDF, Microsoft Word and Excel document."
          >
            {/* <Switch
              suffix="Reuse receipt for subsequent claims"
              size="sm"
              onChange={(e) => setSingleReceipt(e.target.checked)}
            /> */}

            <FormRow>
              <UploadClaimReceipt />
            </FormRow>
          </FormSection>

          <Button
            type="submit"
            fullWidth
            prefix={<CirclePlusIcon size={20} />}
            disabled={pending || hasErrors}
          >
            Add
          </Button>
        </div>
      </Card>
    </Form>
  );
}
