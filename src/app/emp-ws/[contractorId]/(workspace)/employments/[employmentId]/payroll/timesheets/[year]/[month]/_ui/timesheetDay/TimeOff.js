import { PartyPopperIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import cn from "@/lib/cn";
import { isEmpty } from "lodash";

const Content = ({ content, publicHoliday }) => {
  if (isEmpty(content)) {
    return null;
  }

  if (publicHoliday) {
    return (
      <HStack className="flex-1 justify-between">
        <p>{content}</p>

        {publicHoliday && <PartyPopperIcon size={16} />}
      </HStack>
    );
  }

  return content;
};

export default function TimeOff({
  label = "",
  timeOff = false,
  publicHoliday = false,
  content = "Time-off",
}) {
  const classNames = cn("tabular-num rounded-md", {
    "bg-gray-100 px-2": timeOff,
    "bg-sky-50 px-2 text-sky-900": publicHoliday,
  });

  return (
    <HStack className={classNames}>
      <p className="w-[24px] text-sm opacity-80">{label}</p>
      <Content content={content} publicHoliday={publicHoliday} />
    </HStack>
  );
}
