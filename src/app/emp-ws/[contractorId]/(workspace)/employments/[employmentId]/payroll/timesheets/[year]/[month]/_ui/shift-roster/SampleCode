const CalendarWithShifts = ({ selectedShift }) => {
  const daysInMonth = 31;
  const daysPerWeek = 7;

  // Define shift colors
  const shiftColors = {
    dayShift: "bg-amber-100 border-amber-900",
    noonShift: "bg-orange-100 border-orange-900",
    nightShift: "bg-indigo-100 border-indigo-900",
    off: "bg-gray-100 border-input",
  };

  // Adjust this for the first day of the month (e.g. June 2025 is Sunday=0, Monday=1, etc.)
  // Let’s assume the first day of the month is Sunday for now.
  // Since our week starts on Monday, we need to shift it.
  const firstDayOfWeek = 0; // 0=Sunday
  const offset = (firstDayOfWeek - 1 + 7) % 7; // Shift so Monday=0

  // Utility: group consecutive days into ranges
  const groupConsecutiveDays = (days) => {
    const sorted = days.map(Number).sort((a, b) => a - b);
    const ranges = [];
    let start = sorted[0];
    let prev = sorted[0];

    for (let i = 1; i < sorted.length; i++) {
      if (sorted[i] === prev + 1) {
        prev = sorted[i];
      } else {
        ranges.push([start, prev]);
        start = sorted[i];
        prev = sorted[i];
      }
    }
    ranges.push([start, prev]);
    return ranges;
  };

  // For each shift, generate segments split by weeks
  const generateSegments = (shiftType, days) => {
    const segments = [];
    const ranges = groupConsecutiveDays(days);

    ranges.forEach(([startDay, endDay]) => {
      let segmentStart = startDay;

      while (segmentStart <= endDay) {
        const adjustedStartIndex = offset + segmentStart - 1;
        const startWeekIndex = Math.floor(adjustedStartIndex / daysPerWeek);
        const startDayOfWeek = adjustedStartIndex % daysPerWeek;

        const weekEndDay = (startWeekIndex + 1) * daysPerWeek - offset;
        const segmentEnd = Math.min(endDay, weekEndDay);

        const adjustedSegmentEndIndex = offset + segmentEnd - 1;
        const segmentEndDayOfWeek = adjustedSegmentEndIndex % daysPerWeek;
        const duration = segmentEndDayOfWeek - startDayOfWeek + 1;

        segments.push({
          shiftType,
          weekIndex: startWeekIndex,
          startDayOfWeek,
          duration,
        });

        segmentStart = segmentEnd + 1;
      }
    });

    return segments;
  };

  // Generate all shift segments
  const allSegments = [];
  ["dayShift", "noonShift", "nightShift", "off"].forEach((shiftType) => {
    const days = selectedShift[shiftType];
    allSegments.push(...generateSegments(shiftType, days));
  });

  // Generate grid cells with proper offset
  const cells = [];
  const totalCells =
    Math.ceil((offset + daysInMonth) / daysPerWeek) * daysPerWeek;

  for (let i = 0; i < totalCells; i++) {
    const dayNumber = i - offset + 1;
    cells.push(
      <div key={i} className="relative h-20 border border-gray-300 bg-white">
        {dayNumber >= 1 && dayNumber <= daysInMonth && (
          <span className="absolute top-1 right-1 text-xs font-semibold">
            {String(dayNumber).padStart(2, "0")}
          </span>
        )}
      </div>,
    );
  }

  return (
    <div className="relative mx-auto w-full max-w-4xl space-y-2">
      {/* Weekday Labels */}
      <div className="grid grid-cols-7 text-center">
        {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
          <div key={day}>{day}</div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 bg-gray-200">{cells}</div>

      {/* Shift Bars */}
      {allSegments.map((segment, index) => (
        <div
          key={index}
          className={`h-4s absolute border ${shiftColors[segment.shiftType]} rounded-md opacity-90`}
          style={{
            top: `calc((100% / ${Math.ceil((offset + daysInMonth) / daysPerWeek)}) * ${segment.weekIndex} + 50px)`, // adjust vertical offset: 70px to account for labels
            left: `calc((100% / 7) * ${segment.startDayOfWeek} + 4px)`,
            width: `calc((100% / 7) * ${segment.duration} - 8px)`,
          }}
        >
          <div
            className={cn("px-2 py-1 text-xs", {
              "py-0.75": segment.shiftType === "off",
            })}
          >
            <Icon shift={segment.shiftType} />
          </div>
        </div>
      ))}
    </div>
  );
};
