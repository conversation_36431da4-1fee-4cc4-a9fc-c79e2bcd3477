import EditTimesheetTime from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/timesheetTime/edit/EditTimesheetTime";
import { useFormContext, useWatch } from "react-hook-form";

export default function EditOvertime({ index, currentLeaveTerm }) {
  const { control } = useFormContext();
  const hasOvertime = useWatch({
    control,
    name: `timesheetDays.[${index}].hasOvertime`,
  });

  if (!hasOvertime) return null;

  const fieldNames = {
    timeInName: `timesheetDays.[${index}].overtimeTimeIn`,
    timeOutName: `timesheetDays.[${index}].overtimeTimeOut`,
    breakDurationName: `timesheetDays.[${index}].overtimeBreakDuration`,
    hoursName: `timesheetDays.[${index}].overtimeHours`,
  };

  return (
    <EditTimesheetTime
      overtime
      index={index}
      currentLeaveTerm={currentLeaveTerm}
      timesheetDay={timesheetDay}
      fieldNames={fieldNames}
    />
  );
}
