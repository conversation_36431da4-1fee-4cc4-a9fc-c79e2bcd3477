"use client";

import DataGridTable from "@/ui/DataGrid/DataGridTable";
import LeaveTypeCell from "@/ui/DataGrid/cells/LeaveTypeCell";
import LeaveUnpaidCountCell from "@/ui/DataGrid/cells/LeaveUnpaidCountCell";

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "Leave Type",
    columnWidth: 200,
    propertyName: "shortLeaveType",
    icon: { show: true, size: 16 },
    cellComponent: LeaveTypeCell,
  },
  {
    columnName: "Entitled",
    columnWidth: 90,
    className: "block text-right text-sm tabular-nums",
    propertyName: "totalEntitled",
  },
  {
    columnName: "Used",
    columnWidth: 90,
    className: "block text-right tabular-nums",
    propertyName: "used",
  },
  {
    columnName: "Balance",
    columnWidth: 90,
    className: "block text-right tabular-nums",
    propertyName: "balance",
  },
  {
    columnName: "Unpaid",
    columnWidth: 90,
    className: "block text-right tabular-nums",
    propertyName: "unpaid",
    cellComponent: LeaveUnpaidCountCell,
  },
];

export default function LeaveEntTable({ leaveEnts }) {
  return (
    <div className="mx-auto max-w-2xl">
      <DataGridTable
        numRows={leaveEnts.length}
        data={leaveEnts}
        tableColumns={DEFAULT_TABLE_COLUMNS}
        defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        enableColumnReordering={false}
        enableColumnResizing={false}
        enableGhostCells={false}
      />
    </div>
  );
}
