import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import { ProgressBarLink } from "@/ui/ProgressBar";
import VStack from "@/ui/VStack";
import { useParams } from "next/navigation";
import ShiftRostersDialog from "./ShiftRostersDialog";
import TimesheetMonthsSegmentedControl from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetMonthsSegmentedControl";
import { ChevronDown, CirclePlusIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/ui/Popover";
import ShiftRosterCard from "./ShiftRosterCard";
import Card from "@/ui/Card";
import { ShiftRosterExamples } from "./ShiftRosterExamples";

export default function TimesheetShiftRosterHeader({ edit = false }) {
  const { contractorId, employmentId, year, month } = useParams();
  const baseUrl = `/emp-ws/${contractorId}/employments/${employmentId}/payroll/timesheets/${year}/${month}`;

  return (
    <div className="sticky top-[106px] z-20">
      <TimesheetMonthsSegmentedControl
        baseUrl={`/emp-ws/${contractorId}/employments/${employmentId}/payroll/timesheets`}
      />
      <div className="space-y-2 border-b bg-white px-4 py-2 drop-shadow-md drop-shadow-gray-200">
        <HStack className="justify-between">
          <VStack className="gap-0">
            <h1 className="text-2xl font-semibold">
              Timesheet for {month} 2025
            </h1>

            <p className="text-muted text-xs">Ref ID: TS169920</p>
          </VStack>

          {edit ? (
            <HStack>
              <ProgressBarLink href={`${baseUrl}`}>
                <Button size="md" variant="secondary" outline type="submit">
                  Cancel
                </Button>
              </ProgressBarLink>
              <ProgressBarLink href={`${baseUrl}`}>
                <Button
                  size="md"
                  variant="success"
                  outline={false}
                  type="submit"
                >
                  Save as draft
                </Button>
              </ProgressBarLink>
            </HStack>
          ) : (
            <ProgressBarLink href={`${baseUrl}/edit`}>
              <Button
                variant="secondary"
                outline
                suffix={<ChevronDown size={20} />}
              >
                Choose a roster
              </Button>
            </ProgressBarLink>
            // <ShiftRostersDialog month={month} />
          )}
          {/* {!edit && <Button disabled>Submit for approval</Button>} */}
        </HStack>
      </div>
    </div>
  );
}
