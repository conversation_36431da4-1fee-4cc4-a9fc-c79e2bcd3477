import { CREATE_CLAIM_MUTATION } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/_ui/ClaimsConstant";
import CommunicationSubTypeSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/CommunicationSubTypeSelect";
import UploadClaimReceipt from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/UploadClaimReceipt";
import AmountInput from "@/components/input/AmountInput";
import { currency } from "@/formatters/numeric";
import useAction from "@/hooks/useAction";
import CommunicationIcon from "@/icons/emp/claim-types/CommunicationIcon";
import boundAction from "@/lib/boundAction";
import Button from "@/ui/Button";
import Card from "@/ui/Card";
import DatePickerInput from "@/ui/datetime/DatePickerInput";
import { Form, FormRow, FormSection, showFormErrors } from "@/ui/Form";
import HStack from "@/ui/HStack";
import Input from "@/ui/Input";
import { parseISO } from "date-fns";
import { CirclePlusIcon } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { use, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export default function CommunicationClaimForm({ fetchClaimPromise }) {
  const { employmentId } = useParams();
  const router = useRouter();
  const res = use(fetchClaimPromise);
  const claimsBalance = res.data?.empClaimsBalance?.claimBalances;
  const balance = claimsBalance.filter(
    (item) => item.claimType === "Communication",
  );
  const minDate = parseISO(res.data?.empClaimsBalance?.minClaimableDate);
  const maxDate = parseISO(res.data?.empClaimsBalance?.maxClaimableDate);

  const [formData, setFormData] = useState(null);
  const form = useForm();
  const {
    register,
    reset,
    watch,
    setError,
    formState: { errors },
  } = form;

  const [create, pending] = useAction(boundAction(CREATE_CLAIM_MUTATION), {
    onSuccess: () => {
      toast.success("Claim created successfully");
      router.refresh();
      reset({
        incurredDate: "",
        amount: "",
        description: "",
        receipt: null,
      });
    },
    onError: ({ userErrors }) => {
      showFormErrors({ userErrors, setError });
    },
  });

  const onSubmit = (input) => {
    const submissionInput = { ...input, claimType: "Communication" };
    setFormData(input);
    create({ employmentId, input: submissionInput });
  };

  const subClaimType = watch("subClaimType");
  const result = balance.find((item) => item.subClaimType === subClaimType);
  const showBalance = currency(result?.limit);
  const hasErrors = Object.keys(errors).length > 0;

  return (
    <Form form={form} onSubmit={onSubmit} formData={formData} className="p-2">
      <Card className="mx-auto max-w-[800px] space-y-4">
        <HStack className="mb-8 justify-between">
          <HStack asChild className="font-semibold">
            <h1>
              <CommunicationIcon />
              Communication
            </h1>
          </HStack>
          {showBalance && <span>Amount claimable: {showBalance}</span>}
        </HStack>

        <div className="mx-auto max-w-[600px] space-y-4">
          <FormSection>
            <FormRow>
              <CommunicationSubTypeSelect
                {...register("subClaimType", {
                  required: "Subtype is required!",
                })}
                error={errors.subClaimType}
              />
              <DatePickerInput
                label="Incurred date"
                name="incurredDate"
                rules={{ required: "Incurred date is required!" }}
                shortcuts={false}
                minDate={minDate}
                maxDate={maxDate}
              />
              <AmountInput
                label="Amount"
                {...register("amount", {
                  required: "Amount is required!",
                })}
                min={0.1}
                error={errors.amount}
              />
            </FormRow>
            <FormRow>
              <Input
                label="Description"
                {...register("description", {
                  required: "Description is required!",
                })}
                error={errors.description}
              />
            </FormRow>
          </FormSection>

          <FormSection
            title="Upload receipt"
            description="Accept images, PDF, Microsoft Word and Excel document."
            className="space-y-0"
          >
            <FormRow>
              <UploadClaimReceipt />
            </FormRow>
          </FormSection>

          <Button
            type="submit"
            fullWidth
            prefix={<CirclePlusIcon size={20} />}
            disabled={pending || hasErrors}
          >
            Add
          </Button>
        </div>
      </Card>
    </Form>
  );
}
