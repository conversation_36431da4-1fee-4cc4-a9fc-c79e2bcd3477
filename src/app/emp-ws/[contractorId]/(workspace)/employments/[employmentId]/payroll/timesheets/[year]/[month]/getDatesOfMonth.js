import {
  differenceInMinutes,
  eachDayOfInterval,
  endOfMonth,
  format,
  parse,
  startOfMonth,
} from "date-fns";

const MONTHS = [
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
];

const DAY_ORDER = ["<PERSON>", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

const getDatesOfMonth = (month) => {
  if (!MONTHS.includes(month)) {
    return [];
  }

  const parsedDate = parse(month, "MM", new Date());
  const start = startOfMonth(parsedDate);
  const end = endOfMonth(parsedDate);
  const firstDayIndex = DAY_ORDER.indexOf(format(start, "EEE"));

  return eachDayOfInterval({ start, end }).map((date, dayOffset) => {
    const dayOfWeek = format(date, "EEE");
    const index = firstDayIndex + dayOffset;
    const isWorkingDay = ["Mon", "Tue", "Wed", "Thu", "Fri"].includes(
      dayOfWeek,
    );
    const halfDay = dayOfWeek === "Sat";

    const publicHoliday = () => {
      switch (index) {
        case 12:
          return "Hari Raya Haji";
        default:
          return null;
      }
    };

    return {
      index,
      publicHoliday: publicHoliday(),
      isWorkingDay: isWorkingDay,
      dayOfWeek,
      // halfDay,
      date: format(date, "dd"),
      leave: [14, 15].includes(index) ? "Full" : null,
      leaveType: [14, 15].includes(index) ? "Annual Leave" : null,
    };
  });
};

export { getDatesOfMonth };
