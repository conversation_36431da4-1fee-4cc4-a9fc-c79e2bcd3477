"use client";

import HStack from "@/ui/HStack";
import { useProgressBar } from "@/ui/ProgressBar";
import {
  SegmentedControlItem,
  SegmentedControlRoot,
} from "@/ui/SegmentedControl";
import Select from "@/ui/Select";
import { format } from "date-fns";
import { useParams, useRouter } from "next/navigation";
import { startTransition } from "react";

const MONTHS = [
  { month: "Jan", value: "01" },
  { month: "Feb", value: "02" },
  { month: "Mar", value: "03" },
  { month: "Apr", value: "04" },
  { month: "May", value: "05" },
  { month: "Jun", value: "06" },
  { month: "Jul", value: "07" },
  { month: "Aug", value: "08" },
  { month: "Sep", value: "09" },
  { month: "Oct", value: "10" },
  { month: "Nov", value: "11" },
  { month: "Dec", value: "12" },
];

export default function ClaimMonthsSegmentedControl({ baseUrl }) {
  const { year, month } = useParams();
  const getMonth = month || format(new Date(), "MM");
  const progress = useProgressBar();
  const router = useRouter();

  const claimsUrl = `${baseUrl}/${year}/${getMonth}`;

  const handleChange = ({ target: { value } }) => {
    progress.start();

    startTransition(() => {
      router.push(`${baseUrl}/${value}/${getMonth}`);
      progress.done();
    });
  };

  return (
    <div className="flex w-full justify-center bg-white px-2 pb-2">
      <Select
        className="w-fit pl-2"
        wrapperClassName="border-none focus-within:ring-0"
        onChange={handleChange}
        defaultValue={year}
      >
        <option>2025</option>
        <option>2024</option>
        <option>2023</option>
      </Select>

      <div className="overflow-hidden overflow-x-scroll">
        <SegmentedControlRoot
          value={claimsUrl}
          style={{ "--segmented-control-border-radius": "9999px" }}
        >
          {MONTHS.map(({ month, value }, index) => {
            return (
              <SegmentedControlItem
                key={index}
                href={`${baseUrl}/${year}/${value}`}
              >
                <HStack className="uppercase">{month}</HStack>
              </SegmentedControlItem>
            );
          })}
        </SegmentedControlRoot>
      </div>
    </div>
  );
}
