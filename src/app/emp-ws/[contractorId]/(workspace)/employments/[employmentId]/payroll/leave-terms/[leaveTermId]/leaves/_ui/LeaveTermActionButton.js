"use client";

import { useState } from "react";
import { useProgressBarLink } from "@/ui/ProgressBar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuEmptyIcon,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import {
  CalendarIcon,
  ChevronDownIcon,
  DownloadIcon,
  FilesIcon,
  ListIcon,
  PlusIcon,
} from "lucide-react";
import Button from "@/ui/Button";
import SplitButton from "@/ui/SplitButton";

export default function LeaveTermActionButton({ size, selectedTerm }) {
  const handleProgressBarLink = useProgressBarLink();

  return (
    <SplitButton
      variant="success"
      size={size}
      menu={<SideActionDropdown selectedTerm={selectedTerm} />}
      prefix={<CalendarIcon strokeWidth={2} size={size === "sm" ? 16 : 20} />}
      onClick={(e) =>
        handleProgressBarLink(
          `${selectedTerm.contractorResourceUrl}/leaves/new`,
        )(e)
      }
    >
      Take Leave
    </SplitButton>
  );
}

function SideActionDropdown({ selectedTerm }) {
  const [open, setOpen] = useState(false);

  const baseUrl = selectedTerm.contractorResourceUrl;

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger>
        <Button
          variant="success"
          className="px-1"
          suffix={<ChevronDownIcon size={20} />}
        />
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuLabel>
          <DropdownMenuEmptyIcon /> Pro-family leaves
        </DropdownMenuLabel>
        <DropdownMenuItem href={`${baseUrl}/pro-family-leaves/new`}>
          <PlusIcon
            className="stroke-green-600 dark:stroke-green-500"
            size={20}
          />{" "}
          New request
        </DropdownMenuItem>
        <DropdownMenuItem href={`${baseUrl}/pro-family-leaves`}>
          <ListIcon size={20} /> View request
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem>
          <FilesIcon size={20} /> Document
        </DropdownMenuItem>
        <DropdownMenuItem>
          <DownloadIcon size={20} /> Download report
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
