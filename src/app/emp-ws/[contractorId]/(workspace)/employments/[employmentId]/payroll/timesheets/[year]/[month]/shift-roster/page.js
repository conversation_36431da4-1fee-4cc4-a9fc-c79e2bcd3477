import apiQuery from "@/lib/apiQuery";
import { isEmpty } from "lodash";
import Timesheet from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/Timesheet";
import { format, getYear } from "date-fns";
import { redirect } from "next/navigation";

const QUERY = `
  query empEmployment($id: ID!) {
    empEmployment(id: $id) {
      id
      slug
      status
      designation

      commencementDate
      expiredDate

      approvers(status: "ACTIVE") {
        id
        approver {
          name
          email
          id
          status
          memberStatus
          memberId
        }

        leave
        timesheet
        claim
        level
        status
        createdAt
        updatedAt
      }
    }
  }
`;

export default async function Page({ params }) {
  const {
    employmentId,
    contractorId,
    year: urlYear,
    month: urlMonth,
  } = await params;

  const res = await apiQuery(QUERY, { id: employmentId });
  const employment = res.data.empEmployment;

  const year = getYear(new Date());
  const month = format(new Date(), "MMM");

  if (!urlYear || !urlMonth) {
    redirect(
      `/emp-ws/${contractorId}/employments/${employmentId}/payroll/timesheets/${year}/${month}`,
    );
  }

  return <Timesheet employment={employment} shiftRoster uploadExcels />;
}
