"use client";

import { useProgressBarLink } from "@/ui/ProgressBar";
import { isEmpty } from "lodash";
import Select from "@/ui/Select";

export default function LeaveTermSelect({
  size = "md",
  leaveTerms,
  defaultValue,
}) {
  const handleProgressBarLink = useProgressBarLink();

  if (isEmpty(leaveTerms)) return null;

  const handleChange = (e) => {
    const selectedTerm = leaveTerms.find((t) => t.slug === e.target.value);
    handleProgressBarLink(`${selectedTerm.contractorResourceUrl}/leaves`)(e);
  };

  return (
    <Select
      className="bg-white dark:bg-background"
      wrapperClassName="max-w-[192px] sm:max-w-[248px]"
      size={size}
      defaultValue={defaultValue}
      onChange={handleChange}
    >
      {leaveTerms.map((leaveTerm) => {
        return (
          <option key={leaveTerm.slug} value={leaveTerm.slug}>
            {size === "sm"
              ? leaveTerm.displayPeriodShort
              : leaveTerm.displayPeriod}
            {leaveTerm.status !== "ACTIVE" ? ` (${leaveTerm.status})` : ""}
          </option>
        );
      })}
    </Select>
  );
}
