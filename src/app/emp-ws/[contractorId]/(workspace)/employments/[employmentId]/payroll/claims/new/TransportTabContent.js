/* eslint-disable @next/next/no-img-element */
"use client";

import ClaimTypeSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/_ui/ClaimTypeSelect";
import AutocompleteWorkingHour from "@/components/autocomplete/AutocompleteWorkingHour";
import cn from "@/lib/cn";
import Button from "@/ui/Button";
import DatePickerInput from "@/ui/datetime/DatePickerInput";
import { FormBody, FormRow, FormSection } from "@/ui/Form";
import HStack from "@/ui/HStack";
import Input from "@/ui/Input";
import TextArea from "@/ui/TextArea";
import VStack from "@/ui/VStack";
import { isEmpty } from "lodash";
import { UploadIcon } from "lucide-react";
import { useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { useFormContext } from "react-hook-form";

export default function TransportTabContent() {
  const {
    register,
    watch,
    setValue,
    setError,
    formState: { errors },
  } = useFormContext();

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false,
    onDrop: (files) => setFileUpload(files[0]),
  });

  const watchFiles = watch("files");
  const filesRootErrorMessage = errors.files?.root?.message;
  const noFileUploaded = isEmpty(watchFiles) && filesRootErrorMessage;

  const showMileage = ["Car Mileage", "Bike Mileage"].includes(
    watch("subClaimType"),
  );
  useEffect(() => {
    setValue("subClaimType", "Car Mileage");
  }, [setValue]);

  return (
    <>
      <h2 className="pb-4 pl-2 text-lg font-bold">Transport</h2>

      <FormBody className="mx-auto max-w-[800px] space-y-4 px-2">
        <FormSection>
          <FormRow colsCount={3}>
            <ClaimTypeSelect type="Transport" />

            <DatePickerInput
              label="Incurred date"
              name="incurredDate"
              rules={{ required: "Incurred date is required!" }}
              shortcuts={false}
            />

            {!showMileage && <Input label="Amount" />}
          </FormRow>

          <TextArea
            size="sm"
            rows={2}
            label="Description"
            className="col-span-2"
          />
        </FormSection>

        <FormSection title="Timing">
          <FormRow colsCount={3}>
            <AutocompleteWorkingHour
              label="Start from"
              name="startFrom"
              rules={{ required: true }}
              suffixStyling
              suffix="12hrs"
            />

            <AutocompleteWorkingHour
              label="End at"
              name="endAt"
              rules={{ required: true }}
              suffixStyling
              suffix="12hrs"
            />
          </FormRow>
        </FormSection>

        {showMileage && <MileageSection />}

        <FormSection
          title="Receipt"
          description="Only image or PDF file. Please save or export the Microsoft Word or Excel as PDF."
        >
          <FormRow>
            <div
              {...getRootProps()}
              className={cn(
                "mx-auto w-full max-w-[390px] rounded-lg transition-all md:w-2/3",
                {
                  "bg-gray-100": isDragActive,
                },
              )}
            >
              <input {...getInputProps()} />
              <VStack
                className={cn(
                  "text-muted h-full cursor-pointer items-center gap-0 rounded-md border-2 py-2",
                  {
                    "text-red-500": noFileUploaded,
                  },
                )}
              >
                <UploadIcon size={30} strokeWidth={1} />
                {filesRootErrorMessage || (
                  <VStack className="items-center gap-0">
                    <span className="text-center text-sm">
                      Drag or click here.
                    </span>
                  </VStack>
                )}
              </VStack>
            </div>
          </FormRow>
        </FormSection>
      </FormBody>

      <HStack className="justify-end-safe p-4">
        <Button variant="primary" type="submit">
          Add
        </Button>
      </HStack>
    </>
  );
}

const MileageSection = () => {
  return (
    <FormSection title="Mileage Calculation">
      <FormRow colsCount={3}>
        <Input label="Starting point" suffix="KM" suffixStyling />
        <Input label="Destination" suffix="KM" suffixStyling />
      </FormRow>

      <GoogleMapsSection />

      <FormRow colsCount={3}>
        <Input label="Incurred mileage" />
        <Input label="Claimed mileage" />
      </FormRow>

      <FormRow colsCount={3}>
        <Input label="Amount" />
        <span className="text-muted text-sm">
          Amount is auto calculated at the rate of $0.60 per KM.
        </span>
      </FormRow>
    </FormSection>
  );
};

const GoogleMapsSection = () => {
  return (
    <>
      <img src="/images/IRAS_building.png" alt="IRAS" className="h-52 w-96" />
      {/* <div ref={mapRef} style={{ width: "100%", height: "500px" }} />

      <div className="space-y-4">
         <div>
          <h2>SingaporeMapRoute</h2>
          <SingaporeMapRoute />
        </div>
         <div>
          <h2>GeminiRoute</h2>
          <GeminiRoute />
        </div>
        <div>
          <h2>GoogleMapsRoute</h2>
          <GoogleMapsRoute />
        </div>
        <div>
          <h2>SixMaps</h2>
          <SixMaps />
        </div>
        <div>
          <h2>GoogleMapsRoute2</h2>
           <GeminiRoute2 />
        </div>
      </div>
      */}
    </>
  );
};
