import {
  Baby<PERSON><PERSON>,
  BadgeDollarSignIcon,
  CalendarSyncIcon,
  HeartIcon,
  HospitalIcon,
  HouseIcon,
  LeafIcon,
  PillIcon,
  PlaneTakeoffIcon,
} from "lucide-react";
import { BabyCarriageIcon } from "@phosphor-icons/react";

export default function TimesheetLeaveIcon({ leaveType }) {
  switch (leaveType) {
    case "Medical Leave":
      return <PillIcon size={16} />;
    case "Childcare Leave":
    case "Extended Childcare Leave":
      return <BabyIcon size={16} />;
    case "Hospitalization Leave":
      return <HospitalIcon size={16} />;
    case "Annual Leave":
      return <PlaneTakeoffIcon size={16} />;
    case "Replacement Leave":
      return <CalendarSyncIcon size={16} />;
    case "Paternity Leave":
    case "Maternity Leave":
      return <BabyCarriageIcon size={18} />;
    case "Unpaid Leave":
      return <BadgeDollarSignIcon size={16} />;
    case "Profamily Leave":
    case "Family Leave":
      return <HouseIcon size={16} />;
    case "Marriage Leave":
      return <HeartIcon size={16} />;
    default:
      return null;
  }
}
