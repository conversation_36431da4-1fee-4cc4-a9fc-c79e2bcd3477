"use client";

import ClaimDrawer from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/new/ClaimDrawer";
import ClaimTabsSection from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/new/ClaimTabsSection";
import { GroupedClaimsList as ClaimsList } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/new/ClaimsConstant2";
import ClaimsListing from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/new/ClaimsListing";
import { formatWorkMonth } from "@/formatters/date";
import AlertCard from "@/ui/AlertCard";
import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import { useParams, useRouter } from "next/navigation";
import { useRef, useState } from "react";
import { useQuery } from "urql";
import ClaimLimitsDialog from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/new/ClaimLimitsDialog";

const QUERY = `
  query empFetchEmploymentClaims($employmentId: ID!) {
    empFetchClaimLimits(employmentId: $employmentId) {
      claimLimits
      claimableMedical
      lastSubmissionDate
    }

    empClaims(employmentId: $employmentId) {
      nodes {
        fmId
        description
        amountClaimed
      }
    }
  }
`;

export default function Page() {
  const { employmentId } = useParams();
  const today = new Date();
  const [claimSelected, setClaimSelected] = useState(null);
  const [open, setOpen] = useState(false);
  const claimRefs = useRef({});

  // const [res] = useQuery({
  //   query: QUERY,
  //   variables: { employmentId },
  // });

  // const { data } = res;

  // const claimLimits = data?.empFetchClaimLimits;
  // const claims = data?.empClaims?.nodes || [];
  const claims = ClaimsList;

  const allAccordionValues = [
    "Medical",
    "Transport",
    "Communication",
    "Meal",
    "Work Related Payment",
    "Others",
  ];
  const [accordionValues, setAccordionValues] = useState(allAccordionValues);

  const closeDrawer = () => {
    Object.values(claimRefs.current).forEach((el) => {
      el?.classList.remove(
        "bg-blue-100",
        "drop-shadow-blue-200",
        "border-blue-200",
      );
      el?.classList.add("bg-white");
    });
  };

  return (
    <>
      {claimSelected && (
        <ClaimDrawer
          claim={claimSelected}
          open={open}
          setOpen={setOpen}
          closeDrawer={closeDrawer}
        />
      )}

      <div className="bg-background min-h-[calc(100vh-180px)] space-y-4 p-4">
        <HStack className="justify-between">
          <h1 className="text-2xl font-bold">
            New claim for {formatWorkMonth(today)}
          </h1>
          {/* <ClaimLimitsDialog /> */}
        </HStack>

        <div className="mx-auto max-w-[900px] px-4">
          <AlertCard variant="info" center contentClassName="w-full">
            <HStack className="justify-between">
              <span>
                You&apos;re about to draft 10 claims amounting to $100.00
              </span>

              <Button className="mr-0 ml-auto">Draft</Button>
            </HStack>
          </AlertCard>
        </div>

        <div className="mx-auto max-w-[900px] px-4">
          <ClaimTabsSection />
        </div>

        <ClaimsListing
          claims={claims}
          setOpen={setOpen}
          setClaimSelected={setClaimSelected}
          claimRefs={claimRefs}
          accordionValues={accordionValues}
          setAccordionValues={setAccordionValues}
          allAccordionValues={allAccordionValues}
        />
      </div>
    </>
  );
}
