import { CircleAlertIcon, PartyPopperIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import cn from "@/lib/cn";
import { isEmpty } from "lodash";
import TimesheetLeaveTypeIcon from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetLeaveTypeIcon";
import Tooltip from "@/ui/Tooltip";

const ContentWithIcon = ({ content, publicHoliday, leave, leaveStatus }) => {
  if (isEmpty(content)) {
    return null;
  }

  if (publicHoliday || leave) {
    return (
      <HStack className="flex-1 justify-between">
        <p>{content}</p>

        {publicHoliday && <PartyPopperIcon size={16} />}
        {leave && leaveStatus ? (
          <Tooltip
            side="right"
            content="Leave is still pending approval."
            sideOffset={6}
            align="center"
            className="bg-black/70 text-xs opacity-80 backdrop-blur-sm"
          >
            <HStack>
              <p className="cursor-default text-sm">{leaveStatus}</p>
              <CircleAlertIcon size={16} />
            </HStack>
          </Tooltip>
        ) : (
          <TimesheetLeaveTypeIcon leaveType={content} />
        )}
      </HStack>
    );
  }

  return content;
};

export default function TimesheetTimeOff({
  index,
  label = "",
  timeOff = false,
  leave = false,
  publicHoliday = false,
  content = "Time-off",
  leaveStatus = "",
}) {
  const classNames = cn("tabular-num rounded-md", {
    "bg-gray-100 px-2": timeOff,
    "bg-sky-50 px-2 text-sky-900": leave || publicHoliday,
  });

  if (leave && leaveStatus) {
    return (
      <Tooltip
        side="right"
        content="Leave is still pending approval."
        sideOffset={6}
        align="center"
        className="bg-black/70 text-xs opacity-80 backdrop-blur-sm"
      >
        <HStack className={classNames}>
          <p className="w-[24px] text-sm opacity-80">{label}</p>
          <ContentWithIcon
            content={content}
            publicHoliday={publicHoliday}
            leave={leave}
            leaveStatus={leaveStatus}
          />
        </HStack>
      </Tooltip>
    );
  }

  return (
    <HStack className={classNames}>
      <p className="w-[24px] text-sm opacity-80">{label}</p>
      <ContentWithIcon
        content={content}
        publicHoliday={publicHoliday}
        leave={leave}
        leaveStatus={leaveStatus}
      />
    </HStack>
  );
}
