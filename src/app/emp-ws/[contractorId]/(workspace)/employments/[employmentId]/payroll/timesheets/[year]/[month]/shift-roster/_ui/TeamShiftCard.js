import Card from "@/ui/Card";
import { List, PropertyList } from "@/ui/PropertyList";
import { SunIcon, MoonIcon, SunHorizonIcon } from "@phosphor-icons/react";
import { useState } from "react";
import cn from "@/lib/cn";
import SunFilledIcon from "@/icons/filled/SunFilledIcon";
import SunHorizonFilledIcon from "@/icons/filled/SunHorizonFilledIcon";
import MoonFilledIcon from "@/icons/filled/MoonFilledIcon";
import HStack from "@/ui/HStack";
import OffFilledIcon from "@/icons/filled/OffFilledIcon";
import Tooltip from "@/ui/Tooltip";
import { getDatesOfMonth } from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/getDatesOfMonth";
import { useFormContext } from "react-hook-form";
import { isEmpty } from "lodash";
import workingHoursCalculator from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/workingHoursCalculator";

const getShift = (selectedTeam, date) => {
  if (isEmpty(selectedTeam)) {
    return {};
  }

  const {
    dayShifts,
    dayShiftTimeIn,
    dayShiftTimeOut,
    noonShifts,
    noonShiftTimeIn,
    noonShiftTimeOut,
    nightShifts,
    nightShiftTimeIn,
    nightShiftTimeOut,
    off,
  } = selectedTeam;

  if (dayShifts.includes(date)) {
    return { shift: "Day", timeIn: dayShiftTimeIn, timeOut: dayShiftTimeOut };
  } else if (noonShifts.includes(date)) {
    return {
      shift: "Noon",
      timeIn: noonShiftTimeIn,
      timeOut: noonShiftTimeOut,
    };
  } else if (nightShifts.includes(date)) {
    return {
      shift: "Night",
      timeIn: nightShiftTimeIn,
      timeOut: nightShiftTimeOut,
    };
  } else if (off.includes(date)) {
    return { shift: "Off", timeIn: null, timeOut: null };
  } else {
    return { timeIn: null, timeOut: null };
  }
};

export default function TeamShiftCard({
  month,
  roster,
  selectedTeam,
  setSelectedTeam = () => {},
  setFormData = () => {},
}) {
  const {
    team,
    dayShiftTimeIn,
    dayShiftTimeOut,
    dayShiftDays,
    noonShiftTimeIn,
    noonShiftTimeOut,
    noonShiftDays,
    nightShiftTimeIn,
    nightShiftTimeOut,
    nightShiftDays,
    offDays,
  } = roster;

  const datesOfMonth = getDatesOfMonth(month);
  const selected = selectedTeam?.team === team;

  const onItemSelect = () => {
    setSelectedTeam((prev) => {
      return prev?.team === team ? null : roster;
    });

    const updatedData = datesOfMonth.map((item) => {
      const { shift, timeIn, timeOut } = getShift(roster, item.date);

      return {
        ...item,
        shift,
        timeIn,
        timeOut,
        break: 60,
        workingHours: workingHoursCalculator(timeIn, timeOut, 60),
        isWorkingDay: ["Day", "Noon", "Night"].includes(shift),
      };
    });

    setFormData(updatedData);
  };

  return (
    <Card
      className={cn("min-w-[280px]s border-0s cursor-pointer rounded-md p-2", {
        "hover:opacity-80": !selected,
        "bg-white outline-2 outline-blue-600 drop-shadow": selected,
      })}
      onClick={onItemSelect}
    >
      <h1
        className={cn("text-lg", {
          "font-semibold": selected,
        })}
      >
        Team {team}
      </h1>

      <PropertyList>
        <List
          label={
            <ListLabel icon={<SunFilledIcon color="#d97706" />} label="Day" />
          }
        >
          <TimeInTimeOutWithDays
            timeIn={dayShiftTimeIn}
            timeOut={dayShiftTimeOut}
            days={dayShiftDays}
          />
        </List>
        <List
          label={
            <ListLabel
              icon={<SunHorizonFilledIcon color="#ea580c" />}
              label="Noon"
            />
          }
        >
          <TimeInTimeOutWithDays
            timeIn={noonShiftTimeIn}
            timeOut={noonShiftTimeOut}
            days={noonShiftDays}
          />
        </List>
        <List
          label={
            <ListLabel
              icon={<MoonFilledIcon color="#4f46e5" />}
              label="Night"
            />
          }
        >
          <TimeInTimeOutWithDays
            timeIn={nightShiftTimeIn}
            timeOut={nightShiftTimeOut}
            days={nightShiftDays}
          />
        </List>
        <List
          label={
            <ListLabel icon={<OffFilledIcon color="#6b7280" />} label="Off" />
          }
        >
          <span className="text-muted text-center text-xs tabular-nums">
            {offDays}
          </span>
        </List>
      </PropertyList>
    </Card>
  );
}

const ListLabel = ({ icon, label }) => (
  <HStack className="gap-1">
    <div className="flex w-[16px] justify-center">{icon}</div>
    {label}
  </HStack>
);

const TimeInTimeOutWithDays = ({ timeIn, timeOut, days }) => {
  return (
    <>
      {timeIn || "NA"} &mdash; {timeOut || "NA"}{" "}
      <span className="text-muted my-auto text-center text-xs">({days})</span>
    </>
  );
};
