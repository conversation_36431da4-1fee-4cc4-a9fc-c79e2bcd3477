import cn from "@/lib/cn";
import VStack from "@/ui/VStack";

export default function TimesheetDate({ item, className, arrowClassName }) {
  const { isWorkingDay, dayOfWeek, date } = item;

  return (
    <VStack
      className={cn(
        "sticky left-0 z-10 gap-4 pl-2",
        {
          "bg-white": isWorkingDay,
        },
        className,
      )}
    >
      <DateArrowWrapper item={item} arrowClassName={arrowClassName}>
        <VStack className={cn("min-w-[40px] items-center gap-0 pr-2")}>
          <p className="tabular-num text-sm font-semibold">{date}</p>
          <p className="text-xs">{dayOfWeek}</p>
        </VStack>
      </DateArrowWrapper>
    </VStack>
  );
}

const DateArrowWrapper = ({ item, arrowClassName, children }) => {
  const { isWorkingDay, halfDay } = item;

  return (
    <div
      className={cn(
        "my-auto w-fit rounded-md bg-amber-100 px-2 py-1 text-amber-900",
        {
          "bg-gray-100 text-black": !isWorkingDay,
          "bg-[linear-gradient(135deg,_transparent_50%,_#f3f4f6_50%)]": halfDay,
        },
        arrowClassName,
      )}
      style={{
        clipPath:
          "polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%)",
      }}
    >
      {children}
    </div>
  );
};
