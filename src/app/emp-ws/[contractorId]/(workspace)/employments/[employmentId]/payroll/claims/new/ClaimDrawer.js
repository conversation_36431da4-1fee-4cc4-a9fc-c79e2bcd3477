"use client";

import ClaimTypeSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/_ui/ClaimTypeSelect";
import cn from "@/lib/cn";
import Button from "@/ui/Button";
import DatePickerInput from "@/ui/datetime/DatePickerInput";
import { Drawer, DrawerContent } from "@/ui/Drawer";
import FileViewer from "@/ui/file-management/FileViewer";
import {
  Form,
  FormBody,
  FormRow,
  FormSection,
  formatDateForDatePicker,
} from "@/ui/Form";
import HStack from "@/ui/HStack";
import Input from "@/ui/Input";
import TextArea from "@/ui/TextArea";
import VStack from "@/ui/VStack";
import { XCircleIcon } from "@phosphor-icons/react";
import { UploadIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useForm, useFormContext } from "react-hook-form";
import AutocompleteWorkingHour from "@/components/autocomplete/AutocompleteWorkingHour";

export default function ClaimDrawer({ claim, open, setOpen, closeDrawer }) {
  const form = useForm();
  const [formData, setFormData] = useState(null);
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false,
    onDrop: (files) => setFileUpload(files[0]),
  });
  const {
    register,
    watch,
    setValue,
    formState: { errors },
  } = form;

  const onSubmit = (input) => {
    console.log(input);
  };

  useEffect(() => {
    setValue("claimType", claim.claimType);
    setValue("subClaimType", claim.subClaimType);
    setValue("description", claim.description);
  }, [claim, setValue]);

  const transport = watch("claimType") === "Transport";
  const mileage = ["Car Mileage", "Bike Mileage"].includes(
    watch("subClaimType"),
  );

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent
        side="right"
        className="w-full max-w-[800px] md:w-2/5"
        onInteractOutside={() => {
          setOpen(false);
          closeDrawer();
        }}
        onEscapeKeyDown={() => {
          setOpen(false);
          closeDrawer();
        }}
      >
        <div className="p-4">
          <div className="flex items-center justify-end pb-2">
            <Button
              variant="plainIcon"
              onClick={() => {
                setOpen(false);
                closeDrawer();
              }}
            >
              <span className="text-gray-300 dark:text-neutral-500">
                <XCircleIcon size="32" weight="fill" />
              </span>
            </Button>
          </div>

          <Form form={form} onSubmit={onSubmit} formData={formData} debug>
            <FormBody className="space-y-4">
              {!transport && !mileage && <NormalForm claim={claim} />}
              {transport && !mileage && <TransportForm claim={claim} />}
              {transport && mileage && <MileageForm claim={claim} />}
              <FormSection
                title="Receipt"
                description="Only image or PDF file. Please save or export the Microsoft Word or Excel as PDF."
              >
                {claim.fileUrl ? (
                  <FileViewer
                    file={{
                      fileUrl: claim.fileUrl,
                      fileType: claim.fileType,
                    }}
                  />
                ) : (
                  <div
                    {...getRootProps()}
                    className={cn(
                      "mx-auto w-full rounded-lg transition-all md:w-2/3",
                      {
                        "bg-gray-100": isDragActive,
                      },
                    )}
                  >
                    <VStack
                      className={cn(
                        "text-muted h-full cursor-pointer items-center gap-0 rounded-md border-2 py-2",
                      )}
                    >
                      <UploadIcon size={30} strokeWidth={1} />

                      <VStack className="items-center gap-0">
                        <span className="text-sm">Drag or click here.</span>
                      </VStack>
                    </VStack>
                  </div>
                )}
              </FormSection>

              <HStack className="w-full justify-end gap-4">
                <Button variant="danger">Remove</Button>
                <Button type="submit">Update</Button>
              </HStack>
            </FormBody>
          </Form>
        </div>
      </DrawerContent>
    </Drawer>
  );
}

const NormalForm = ({ claim }) => {
  const {
    register,
    setValue,
    formState: { errors },
  } = useFormContext();

  return (
    <>
      <FormSection>
        <ClaimTypeSelect showMain type={claim.claimType} />

        <FormRow>
          <Input label="Amount Claimed" defaultValue={claim.amountClaimed} />
          <DatePickerInput
            label="Incurred At"
            name="claimDate"
            defaultValue={formatDateForDatePicker(claim.claimDate)}
          />
        </FormRow>
        <FormRow>
          <TextArea
            size="sm"
            rows={2}
            label="Description"
            {...register("description")}
          />
        </FormRow>
      </FormSection>
    </>
  );
};

const TransportForm = ({ claim }) => {
  const {
    register,
    setValue,
    formState: { errors },
  } = useFormContext();

  return (
    <>
      <FormSection>
        <ClaimTypeSelect showMain type={claim.claimType} />

        <FormRow colsCount={2}>
          <DatePickerInput
            label="Incurred At"
            name="claimDate"
            defaultValue={formatDateForDatePicker(claim.claimDate)}
          />
        </FormRow>
        <FormRow>
          <TextArea
            size="sm"
            rows={2}
            label="Description"
            {...register("description")}
          />
        </FormRow>
      </FormSection>
      <FormSection title="Timing">
        <FormRow colsCount={2}>
          <AutocompleteWorkingHour
            label="Start from"
            name="startFrom"
            rules={{ required: true }}
            suffixStyling
            suffix="12hrs"
          />

          <AutocompleteWorkingHour
            label="End at"
            name="endAt"
            rules={{ required: true }}
            suffixStyling
            suffix="12hrs"
          />
        </FormRow>
      </FormSection>
    </>
  );
};

const MileageForm = ({ claim }) => {
  const {
    register,
    setValue,
    formState: { errors },
  } = useFormContext();

  return (
    <>
      <FormSection>
        <ClaimTypeSelect showMain type={claim.claimType} />

        <FormRow colsCount={2}>
          <DatePickerInput
            label="Incurred At"
            name="claimDate"
            defaultValue={formatDateForDatePicker(claim.claimDate)}
          />
        </FormRow>
        <FormRow>
          <TextArea
            size="sm"
            rows={2}
            label="Description"
            {...register("description")}
          />
        </FormRow>
      </FormSection>
      <FormSection title="Timing">
        <FormRow colsCount={2}>
          <AutocompleteWorkingHour
            label="Start from"
            name="startFrom"
            rules={{ required: true }}
            suffixStyling
            suffix="12hrs"
          />

          <AutocompleteWorkingHour
            label="End at"
            name="endAt"
            rules={{ required: true }}
            suffixStyling
            suffix="12hrs"
          />
        </FormRow>
      </FormSection>
      <FormSection title="Mileage Calculation">
        <FormRow>
          <Input label="Starting point" suffix="KM" suffixStyling />
          <Input label="Destination" suffix="KM" suffixStyling />
        </FormRow>

        <div>Google Map here</div>

        <FormRow>
          <Input label="Incurred mileage" />
          <Input label="Claimed mileage" />
        </FormRow>

        <FormRow>
          <Input label="Amount" />
          <span className="text-muted text-sm">
            Amount is auto calculated at the rate of $0.60 per KM.
          </span>
        </FormRow>
      </FormSection>
    </>
  );
};
