import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import Button from "@/ui/Button";
import { useFormContext, useWatch } from "react-hook-form";
import { sumBy, toNumber } from "lodash";
import { getTotalHours } from "@/lib/timesheet/calculator";

export default function EditTimesheetDayHeader({
  index,
  className,
  hasRemark = false,
  shift = false,
}) {
  const { control } = useFormContext();
  const watchTimesheetDays = useWatch({ control, name: "timesheetDays" });
  const watchTotalNormalHours = sumBy(
    watchTimesheetDays,
    (timesheetDay) => toNumber(timesheetDay?.normalHours) || 0,
  );
  const watchTotalOvertimeHours = sumBy(
    watchTimesheetDays,
    (timesheetDay) => toNumber(timesheetDay.overtimeHours) || 0,
  );
  const totalOvertimeDays = watchTimesheetDays.filter(
    ({ hasOvertime }) => hasOvertime,
  );

  if (index !== 0) return null;

  return (
    <VStack>
      <div className="pl-2">
        <HStack className="flex w-full justify-between border-b pb-2">
          <Button variant="ghost" size="sm">
            Clear all
          </Button>

          <VStack className="gap-1">
            <p className="text-center text-sm text-muted">
              Normal:{" "}
              <span className="text-center">
                <span className="text-black">{watchTotalNormalHours}</span>{" "}
                <span className="text-sm text-muted">hrs</span>{" "}
                <span className="text-center text-xs text-muted">
                  (24 days)
                </span>
              </span>
            </p>

            <p className="text-center text-sm text-muted">
              Overtime:{" "}
              <span className="text-center">
                <span className="text-black">{watchTotalOvertimeHours}</span>{" "}
                <span className="text-sm text-muted">hrs</span>{" "}
                <span className="text-center text-xs text-muted">
                  ({totalOvertimeDays.length} days)
                </span>
              </span>
            </p>
          </VStack>
        </HStack>
      </div>

      <div className={cn("pb-4 text-sm text-muted", className)}>
        <div className="pl-3">Date</div>

        <HStack>
          <HStack className="px-2">
            <div className="w-[24px]" />
            <div className="w-[375px]">Working hours</div>
          </HStack>

          <div className={cn("w-[110px] text-left")}>Break</div>

          <div className="w-[80px] text-left">Total hrs</div>
        </HStack>

        {shift && <div className="pl-4">Shift</div>}

        {hasRemark && <div>Remark</div>}
      </div>
    </VStack>
  );
}
