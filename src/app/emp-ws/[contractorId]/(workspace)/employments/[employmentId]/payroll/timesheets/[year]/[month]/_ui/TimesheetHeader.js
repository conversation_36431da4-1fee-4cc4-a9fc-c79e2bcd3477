"use client";

import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import { ProgressBarLink } from "@/ui/ProgressBar";
import VStack from "@/ui/VStack";
import { useParams } from "next/navigation";
import TimesheetMonthsSegmentedControl from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetMonthsSegmentedControl";
import { getMonth } from "date-fns";

const MONTHS = [
  { month: "Jan", value: "01" },
  { month: "Feb", value: "02" },
  { month: "Mar", value: "03" },
  { month: "Apr", value: "04" },
  { month: "May", value: "05" },
  { month: "Jun", value: "06" },
  { month: "Jul", value: "07" },
  { month: "Aug", value: "08" },
  { month: "Sep", value: "09" },
  { month: "Oct", value: "10" },
  { month: "Nov", value: "11" },
  { month: "Dec", value: "12" },
];

export default function TimesheetHeader({ edit = false }) {
  const { contractorId, employmentId, year, month } = useParams();
  const baseUrl = `/emp-ws/${contractorId}/employments/${employmentId}/payroll/timesheets/${year}/${month}`;
  const getMonth = MONTHS.find((item) => item.value === month)?.month;

  return (
    <div className="sticky top-[106px] z-20">
      <TimesheetMonthsSegmentedControl
        baseUrl={`/emp-ws/${contractorId}/employments/${employmentId}/payroll/timesheets`}
      />
      <div className="space-y-2 border-b bg-white px-4 py-2 drop-shadow-md drop-shadow-gray-200">
        <HStack className="justify-between">
          <VStack className="gap-0">
            <h1 className="text-2xl font-semibold">
              Timesheet for {getMonth} 2025
            </h1>

            <p className="text-muted text-xs">Ref ID: TS169920</p>
          </VStack>

          {edit ? (
            <HStack>
              <ProgressBarLink href={`${baseUrl}`}>
                <Button size="md" variant="secondary" outline type="submit">
                  Cancel
                </Button>
              </ProgressBarLink>
              <ProgressBarLink href={`${baseUrl}`}>
                <Button
                  size="md"
                  variant="success"
                  outline={false}
                  type="submit"
                >
                  Save as draft
                </Button>
              </ProgressBarLink>
            </HStack>
          ) : (
            <ProgressBarLink href={`${baseUrl}/edit`}>
              <Button variant="primary">Fill timesheet</Button>
            </ProgressBarLink>
          )}
          {/* {!edit && <Button disabled>Submit for approval</Button>} */}
        </HStack>
      </div>
    </div>
  );
}
