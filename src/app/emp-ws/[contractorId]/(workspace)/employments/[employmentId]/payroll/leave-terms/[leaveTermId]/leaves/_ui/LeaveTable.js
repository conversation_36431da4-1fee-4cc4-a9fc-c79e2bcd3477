import apiQuery from "@/lib/apiQuery";
import mergeColumns from "@/ui/DataGrid/mergeColumns";
import getTableMetadata from "@/ui/DataGrid/getTableMetadata";
import isNotEmpty from "@/lib/isNotEmpty";
import { DataGrid } from "@/ui/DataGrid";
import DataGridTable from "@/ui/DataGrid/DataGridTable";
import Pagination from "@/ui/Pagination";
import DateCell from "@/ui/DataGrid/cells/DateCell";
import ResourceIdCell from "@/ui/DataGrid/cells/emp/ResourceIdCell";
import StatusWithTimestampCell from "@/ui/DataGrid/cells/emp/StatusWithTimestampCell";
import BooleanCell from "@/ui/DataGrid/cells/BooleanCell";
import LeaveTypeCell from "@/ui/DataGrid/cells/LeaveTypeCell";
import HtmlCell from "@/ui/DataGrid/cells/HtmlCell";

const QUERY = `
  query empLeaves($employmentId: ID!, $leaveTermId: ID!, $leaveTypes: [String!], $statuses: [String!], $page: Int) {
    empLeaves(employmentId: $employmentId, leaveTermId: $leaveTermId, leaveTypes: $leaveTypes, statuses: $statuses, page: $page) {
      nodes {
        id
        fmId
        contractorResourceUrl

        leaveType
        shortLeaveType
        longLeaveType
        leaveDate
        dayPeriod
        dayPeriodLabel
        entitled

        mcSource
        contractorRemark

        status
        statusUpdatedAt
      }

      totalCount
      totalPages
      processingTimeMs
    }

    personalTableColumn(tableName: "EMP_PAYROLL_LEAVES") {
      tableName
      tableColumns {
        columnName
        columnWidth
        propertyName
        visible
      }
    }
  }
`;

const DEFAULT_TABLE_COLUMNS = [
  {
    columnName: "ID",
    columnWidth: 100,
    propertyName: "id",
    visible: true,
    cellComponent: ResourceIdCell,
  },
  {
    columnName: "Date",
    columnWidth: 200,
    propertyName: "leaveDate",
    visible: true,
    cellComponent: DateCell,
  },
  {
    columnName: "Leave Type",
    columnWidth: 240,
    propertyName: "shortLeaveType",
    mcSourcePropertyName: "mcSource",
    visible: true,
    cellComponent: LeaveTypeCell,
  },
  {
    columnName: "Period",
    columnWidth: 120,
    propertyName: "dayPeriodLabel",
    visible: true,
  },
  {
    columnName: "Entitled",
    columnWidth: 100,
    propertyName: "entitled",
    visible: true,
    displayFalseValue: true,
    cellComponent: BooleanCell,
  },
  {
    columnName: "Status",
    columnWidth: 200,
    propertyName: "status",
    visible: true,
    cellComponent: StatusWithTimestampCell,
  },
  {
    columnName: "Remark",
    columnWidth: 200,
    propertyName: "contractorRemark",
    visible: true,
    cellComponent: HtmlCell,
  },
];

export default async function LeaveTable({ params, searchParams }) {
  const { employmentId, leaveTermId } = await params;
  let { type, status, page } = await searchParams;
  const leaveTypes = isNotEmpty(type)
    ? type.split(",").filter((t) => t !== "ALL")
    : [];
  const statuses = isNotEmpty(status)
    ? status.split(",").filter((s) => s !== "ALL")
    : [];
  const currentPage = Number(page || 1);

  const res = await apiQuery(QUERY, {
    employmentId,
    leaveTermId,
    leaveTypes,
    statuses,
    page: currentPage,
  });

  const {
    records: leaves,
    totalPages,
    totalCount,
    processingTimeMs,
    personalTableColumns,
  } = getTableMetadata(res.data.empLeaves, res.data.personalTableColumn);

  const tableColumns = mergeColumns(
    DEFAULT_TABLE_COLUMNS,
    personalTableColumns,
  );

  return (
    <DataGrid.Root className="space-y-2">
      <Pagination currentPage={currentPage} totalPages={totalPages} />
      <DataGrid.TotalCountAndTime
        totalCount={totalCount}
        processingTimeMs={processingTimeMs}
      />

      <DataGrid.Content>
        <DataGridTable
          numRows={leaves.length}
          data={leaves}
          tableName="EMP_PAYROLL_LEAVES"
          tableColumns={tableColumns}
          defaultTableColumns={DEFAULT_TABLE_COLUMNS}
        />
      </DataGrid.Content>
    </DataGrid.Root>
  );
}
