import { isEmpty } from "lodash";

export default function timesheetDaysForm(timesheetDays) {
  if (isEmpty(timesheetDays)) return [];

  return timesheetDays.map((timesheetDay) => {
    const {
      normalTimeIn,
      normalTimeOut,
      overtimeTimeIn,
      overtimeTimeOut,
      ...remainData
    } = timesheetDay;

    return {
      normalTimeIn: { value: normalTimeIn },
      normalTimeOut: { value: normalTimeOut },
      overtimeTimeIn: { value: overtimeTimeIn },
      overtimeTimeOut: { value: overtimeTimeOut },
      ...remainData,
    };
  });
}
