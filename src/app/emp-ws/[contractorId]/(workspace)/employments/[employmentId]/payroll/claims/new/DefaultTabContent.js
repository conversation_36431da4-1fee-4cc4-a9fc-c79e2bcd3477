"use client";

import ClaimTypeSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/_ui/ClaimTypeSelect";
import cn from "@/lib/cn";
import Button from "@/ui/Button";
import DatePickerInput from "@/ui/datetime/DatePickerInput";
import DecimalNumberInput from "@/ui/DecimalNumberInput";
import { FormBody, FormRow, FormSection } from "@/ui/Form";
import TextArea from "@/ui/TextArea";
import VStack from "@/ui/VStack";
import { Dollar } from "@blueprintjs/icons";
import { isEmpty } from "lodash";
import { UploadIcon } from "lucide-react";
import { useDropzone } from "react-dropzone";
import { useFormContext } from "react-hook-form";

export default function DefaultTabContent({ type }) {
  const {
    register,
    watch,
    setValue,
    setError,
    formState: { errors },
  } = useFormContext();

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false,
    onDrop: (files) => setFileUpload(files[0]),
  });

  const watchFiles = watch("files");
  const filesRootErrorMessage = errors.files?.root?.message;
  const noFileUploaded = isEmpty(watchFiles) && filesRootErrorMessage;

  return (
    <>
      <h2 className="pb-4 pl-2 text-lg font-bold">{type}</h2>

      <FormBody className="mx-auto max-w-[600px] space-y-4 px-2">
        <FormSection>
          <FormRow>
            <ClaimTypeSelect fieldArrayName="claims" type={type} />
            <DatePickerInput
              label="Incurred date"
              name="startDate"
              rules={{ required: "Start date is required!" }}
              shortcuts={false}
            />

            <DecimalNumberInput
              step={1}
              label="Amount"
              decimalPoints={2}
              {...register(`claims.amount`, {
                valueAsNumber: true,
                required: "Amount is required!",
              })}
              prefix={
                <span className="text-foreground">
                  <Dollar color="currentColor" />
                </span>
              }
              error={errors.claims?.amount}
            />
          </FormRow>

          <FormRow>
            <TextArea size="sm" rows={2} label="Description" />
          </FormRow>
        </FormSection>

        <FormSection
          title="Upload receipt"
          description="Only image or PDF file. Please save or export the Microsoft Word or Excel as PDF."
        >
          <FormRow>
            <div
              {...getRootProps()}
              className={cn(
                "mx-auto w-full rounded-lg transition-all md:w-2/3",
                {
                  "bg-gray-100": isDragActive,
                },
              )}
            >
              <input {...getInputProps()} />
              <VStack
                className={cn(
                  "text-muted h-full cursor-pointer items-center gap-0 rounded-md border-2 py-2",
                  {
                    "text-red-500": noFileUploaded,
                  },
                )}
              >
                <UploadIcon size={30} strokeWidth={1} />
                {filesRootErrorMessage || (
                  <VStack className="items-center gap-0">
                    <span className="text-center text-sm">
                      Drag or click here.
                    </span>
                  </VStack>
                )}
              </VStack>
            </div>
          </FormRow>
        </FormSection>

        <div className="pb-4">
          <Button variant="primary" type="submit" className="w-full">
            Add
          </Button>
        </div>
      </FormBody>
    </>
  );
}
