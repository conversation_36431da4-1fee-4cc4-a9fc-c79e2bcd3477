"use client";

import CommunicationClaimForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/CommunicationClaimForm";
import MealClaimForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/MealClaimForm";
import MedicalClaimForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/MedicalClaimForm";
import OthersClaimForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/OthersClaimForm";
import PortalForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/PortalForm";
import TransportClaimForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/TransportClaimForm";
import WorkRelatedPaymentClaimForm from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/claims/[year]/[month]/new/_ui/claim-forms/WorkRelatedPaymentClaimForm";
import AiUploadIcon from "@/icons/emp/claim-types/AiUploadIcon";
import CommunicationIcon from "@/icons/emp/claim-types/CommunicationIcon";
import MealIcon from "@/icons/emp/claim-types/MealIcon";
import MedicalIcon from "@/icons/emp/claim-types/MedicalIcon";
import OthersIcon from "@/icons/emp/claim-types/OthersIcon";
import TransportIcon from "@/icons/emp/claim-types/TransportIcon";
import WorkRelatedPaymentIcon from "@/icons/emp/claim-types/WorkRelatedPaymentIcon";
import HStack from "@/ui/HStack";
import { StateTab, TabContent, TabList, TabTrigger } from "@/ui/StateTab";

export default function ClaimTypesTab({ fetchClaimPromise }) {
  return (
    <HStack scrollable className="justify-center-safe">
      <StateTab variant="plain" defaultValue="Transport">
        <TabList>
          <TabTrigger value="Medical">
            <MedicalIcon />
            <span className="hidden sm:block">Medical</span>
          </TabTrigger>
          <TabTrigger value="Transport">
            <TransportIcon />
            <span className="hidden sm:block">Transport</span>
          </TabTrigger>
          <TabTrigger value="Communication">
            <CommunicationIcon />
            <span className="hidden sm:block">Communication</span>
          </TabTrigger>
          <TabTrigger value="Meal">
            <MealIcon />
            <span className="hidden sm:block">Meal</span>
          </TabTrigger>
          <TabTrigger value="Work Related Payment">
            <WorkRelatedPaymentIcon />
            <span className="hidden sm:block">Work Related Payment</span>
          </TabTrigger>
          <TabTrigger value="Others">
            <OthersIcon />
            <span className="hidden sm:block">Others</span>
          </TabTrigger>
          <TabTrigger value="AI Upload">
            <AiUploadIcon />
            <span className="hidden sm:block">AI Upload</span>
          </TabTrigger>
        </TabList>

        <TabContent value="Medical">
          <PortalForm>
            <MedicalClaimForm fetchClaimPromise={fetchClaimPromise} />
          </PortalForm>
        </TabContent>

        <TabContent value="Transport">
          <PortalForm>
            <TransportClaimForm fetchClaimPromise={fetchClaimPromise} />
          </PortalForm>
        </TabContent>

        <TabContent value="Communication">
          <PortalForm>
            <CommunicationClaimForm fetchClaimPromise={fetchClaimPromise} />
          </PortalForm>
        </TabContent>

        <TabContent value="Meal">
          <PortalForm>
            <MealClaimForm fetchClaimPromise={fetchClaimPromise} />
          </PortalForm>
        </TabContent>

        <TabContent value="Work Related Payment">
          <PortalForm>
            <WorkRelatedPaymentClaimForm
              fetchClaimPromise={fetchClaimPromise}
            />
          </PortalForm>
        </TabContent>

        <TabContent value="Others">
          <PortalForm>
            <OthersClaimForm fetchClaimPromise={fetchClaimPromise} />
          </PortalForm>
        </TabContent>
      </StateTab>
    </HStack>
  );
}
