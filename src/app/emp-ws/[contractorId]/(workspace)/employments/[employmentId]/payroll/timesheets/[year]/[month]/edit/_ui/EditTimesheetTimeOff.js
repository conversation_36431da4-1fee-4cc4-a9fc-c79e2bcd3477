import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/DropdownMenu";
import { Check, ChevronDownIcon, PartyPopperIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import cn from "@/lib/cn";
import { isEmpty } from "lodash";
import TimesheetLeaveTypeIcon from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/timesheets/[year]/[month]/_ui/TimesheetLeaveTypeIcon";

const ContentWithIcon = ({ content, publicHoliday, leave }) => {
  if (isEmpty(content)) {
    return null;
  }

  if (publicHoliday || leave) {
    return (
      <HStack className="flex-1 justify-between">
        <p>{content}</p>

        {publicHoliday && <PartyPopperIcon size={16} />}
        {leave && <TimesheetLeaveTypeIcon leaveType={content} />}
      </HStack>
    );
  }

  return content;
};

export default function EditTimesheetTimeOff({
  index,
  label = "",
  timeOff = false,
  leave = false,
  publicHoliday = false,
  content = "Time-off",
}) {
  const [open, setOpen] = useState(false);

  const { watch, setValue } = useFormContext();
  const watchTimeOff = watch(`timesheetDays.[${index}].timeOff`);

  const TimeOffRow = ({ label }) => {
    return (
      <HStack
        className={cn("tabular-num rounded-md px-2", {
          "cursor-pointer bg-gray-100": timeOff,
          "bg-sky-50 text-sky-900": leave || publicHoliday,
        })}
      >
        <p className="w-[24px] text-sm opacity-80">{label}</p>
        <ContentWithIcon
          content={content}
          publicHoliday={publicHoliday}
          leave={leave}
        />
        <ChevronDownIcon size={18} />
      </HStack>
    );
  };

  if (leave || publicHoliday) {
    return <TimeOffRow label={label} />;
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        onPointerDown={(e) => e.preventDefault()}
        onClick={() => setOpen((prev) => !prev)}
      >
        <button className="outline-none">
          <TimeOffRow label={watchTimeOff} />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent sideOffset={0}>
        {["Full", "AM", "PM"].map((meridian) => {
          const selected = watchTimeOff === meridian;

          return (
            <DropdownMenuItem
              key={meridian}
              onClick={() => {
                setValue(
                  `timesheetDays.[${index}].timeOff`,
                  selected ? null : meridian,
                );
              }}
            >
              <div className="flex items-center gap-2">
                <CheckMark selected={selected} />
                {meridian}
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

const CheckMark = ({ selected }) => {
  return selected ? (
    <Check size={16} strokeWidth={2} />
  ) : (
    <div className="invisible w-4" />
  );
};
