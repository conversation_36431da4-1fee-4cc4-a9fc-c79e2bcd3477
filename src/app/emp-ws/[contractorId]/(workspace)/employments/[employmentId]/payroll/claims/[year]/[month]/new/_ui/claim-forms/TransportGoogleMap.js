import { FormRow, FormSection } from "@/ui/Form";
import Input from "@/ui/Input";
import { MultiSelect } from "@/ui/MultiSelect";
import StrictNumberInput from "@/ui/StrictNumberInput";
import { isEmpty } from "lodash";
import { usePlacesAutocomplete } from "places-autocomplete-hook";
import { useEffect, useRef, useState } from "react";
import Highlighter from "react-highlight-words";
import { useFormContext } from "react-hook-form";

const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY;
const SCRIPT_ID = "google-maps-script";
const SINGAPORE = { lat: 1.3521, lng: 103.8198 };

// Main App component
const TransportGoogleMap = ({
  initialDistance = 0,
  initialAmount = 0,
  transportRate,
}) => {
  // https://github.com/gstrobl/places-autocomplete-hook
  const { suggestions, setValue: setGoogleValue } = usePlacesAutocomplete({
    apiKey: GOOGLE_MAPS_API_KEY,
    includedRegionCodes: "sg",
    includedPrimaryTypes: ["geocode", "establishment"],
    location: SINGAPORE,
  });

  const {
    register,
    setValue,
    clearErrors,
    getValues,
    watch,
    formState: { errors },
  } = useFormContext();

  const rateType =
    watch("subClaimType") === "Car Mileage"
      ? transportRate.car
      : transportRate.bike;

  // Refs to hold Google Maps instances
  const mapRef = useRef(null);

  // Using refs for Google Maps objects to prevent re-renders
  const mapInstanceRef = useRef(null);
  const startMarkerRef = useRef(null);
  const endMarkerRef = useRef(null);
  const directionsServiceRef = useRef(null);
  const directionsRendererRef = useRef(null);

  // State to display the calculated distance
  const [distance, setDistance] = useState(initialDistance);
  const [amountClaimed, setAmountClaimed] = useState(initialAmount);

  // Handles script loading, map initialization, and autocomplete setup.
  useEffect(() => {
    // Load the initial route from defaultValues
    const loadInitialRoute = () => {
      const defaultStart = getValues("transportOrigin");
      const defaultEnd = getValues("transportOrigin");

      if (!isEmpty(defaultStart) || !isEmpty(defaultEnd)) {
        calculateAndDisplayRoute();
      }
    };

    const initMap = async () => {
      const { Map } = await window.google.maps.importLibrary("maps");
      const { AdvancedMarkerElement } =
        await window.google.maps.importLibrary("marker");

      const map = new Map(mapRef.current, {
        zoom: 14,
        center: SINGAPORE,
        mapId: "SINGAPORE_ROUTE_PLANNER",
        disableDefaultUI: true,
      });
      mapInstanceRef.current = map;

      directionsServiceRef.current = new window.google.maps.DirectionsService();
      directionsRendererRef.current = new window.google.maps.DirectionsRenderer(
        {
          draggable: false,
          map: map,
        },
      );

      // Trigger the initial route loading after the map is ready
      loadInitialRoute(AdvancedMarkerElement);
    };

    if (!document.getElementById(SCRIPT_ID)) {
      const script = document.createElement("script");
      script.id = SCRIPT_ID;
      script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places,marker&callback=initMap&v=beta`;
      script.async = true;
      script.defer = true;
      window.initMap = initMap;
      document.body.appendChild(script);
    } else if (!window.google) {
      window.initMap = initMap;
    } else {
      initMap();
    }

    return () => {
      if (window.initMap) delete window.initMap;
    };
  }, [register, setValue]);

  // Plot the path
  const calculateAndDisplayRoute = () => {
    // Watch a specific field
    const startingValue = getValues("transportOrigin");
    const destinationValue = getValues("transportDestination");

    if (isEmpty(startingValue) || isEmpty(destinationValue)) return;
    directionsRendererRef.current.setDirections({ routes: [] });

    const origin = startingValue[0]?.placeId;
    const destination = destinationValue[0]?.placeId;

    if (!origin || !destination) return;

    directionsServiceRef.current.route(
      {
        origin: { placeId: origin },
        destination: { placeId: destination },
        travelMode: window.google.maps.TravelMode.DRIVING,
      },
      (response, status) => {
        if (status === "OK") {
          if (startMarkerRef.current) startMarkerRef.current.map = null;
          if (endMarkerRef.current) endMarkerRef.current.map = null;
          directionsRendererRef.current.setDirections(response);

          // Get distance from the route response
          const route = response.routes[0].legs[0];
          const calculatedKm = Number((route.distance.value / 1000).toFixed(1));
          const total = (Number(calculatedKm || 0) * rateType).toFixed(2);

          setDistance(calculatedKm);
          setAmountClaimed(total);
          clearErrors("claimedMileage");
          setValue("incurredMileage", calculatedKm);
          setValue("claimedMileage", calculatedKm);
          setValue("amount", total);
        } else {
          console.log("Directions request failed due to " + status);
          setDistance("");
          setAmountClaimed(0);
          setValue("incurredMileage", 0);
          setValue("claimedMileage", 0);
          setValue("amount", 0);
        }
      },
    );
  };

  const reCalculateRoute = (dest, value) => {
    setValue(dest, value);
    clearErrors(dest);
    calculateAndDisplayRoute();
  };

  const onClaimedMileageChange = (mileage) => {
    const total = (Number(mileage || 0) * rateType).toFixed(2);

    setAmountClaimed(total);
    setValue("amount", total);
  };

  return (
    <FormSection>
      <FormRow>
        <MultiSelect
          allowCustomValue={false}
          name="transportOrigin"
          label="Starting"
          multiple={false}
          itemName="shortAddress"
          defaultValue={getValues("transportOrigin")}
          minWidth={450}
          items={
            suggestions.status === "OK"
              ? suggestions?.data.map(
                  ({ placeId, text, structuredFormat }) => ({
                    placeId,
                    longAddress: text.text,
                    shortAddress: structuredFormat.mainText.text,
                  }),
                )
              : []
          }
          rules={{ required: true }}
          onInputValueChange={(inputValue) => {
            setGoogleValue(inputValue);
          }}
          onChange={(value) => reCalculateRoute("transportOrigin", value)}
        >
          <MultiSelect.Item>
            {({ item, inputValue }) => (
              <Highlighter
                textToHighlight={item.longAddress}
                autoEscape={true}
                searchWords={inputValue.split(/\s+/)}
              />
            )}
          </MultiSelect.Item>
        </MultiSelect>

        <MultiSelect
          allowCustomValue={false}
          name="transportDestination"
          label="Destination"
          multiple={false}
          itemName="shortAddress"
          defaultValue={getValues("transportDestination")}
          minWidth={450}
          items={
            suggestions.status === "OK"
              ? suggestions?.data.map(
                  ({ placeId, text, structuredFormat }) => ({
                    placeId,
                    longAddress: text.text,
                    shortAddress: structuredFormat.mainText.text,
                  }),
                )
              : []
          }
          rules={{ required: true }}
          onInputValueChange={(inputValue) => {
            setGoogleValue(inputValue);
          }}
          onChange={(value) => reCalculateRoute("transportDestination", value)}
        >
          <MultiSelect.Item>
            {({ item, inputValue }) => (
              <Highlighter
                textToHighlight={item.longAddress}
                autoEscape={true}
                searchWords={inputValue.split(/\s+/)}
              />
            )}
          </MultiSelect.Item>
        </MultiSelect>
      </FormRow>

      {/* The div size needs to be fixed for Maps.js as it doesn't load without it */}
      {/* TODO:(Alvin:Claim) Set width and height based on screen size */}
      <div ref={mapRef} className="h-[400px] w-[600px] rounded-xl">
        {/* Fallback content while map loads */}
        <div className="flex h-full w-full items-center justify-center bg-gray-700">
          <p className="text-white">Loading Map...</p>
        </div>
      </div>

      <FormRow>
        <Input label="Incurred Mileage" suffix="KM" value={distance} disabled />

        <StrictNumberInput
          {...register("claimedMileage", {
            required: "Claimed Mileage is required",
            valueAsNumber: true,
          })}
          label="Claimed Mileage"
          suffix="KM"
          min={0}
          max={distance}
          limit={distance}
          step={0.1}
          onChange={(e) => onClaimedMileageChange(e.target.value)}
          error={errors.claimedMileage}
        />

        <Input
          label="Amount"
          prefix="$"
          disabled
          value={amountClaimed}
          error={errors.amount}
        />
      </FormRow>
    </FormSection>
  );
};

export default TransportGoogleMap;
