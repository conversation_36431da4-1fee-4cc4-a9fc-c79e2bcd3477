import { isEmpty } from "lodash";
import { EyeIcon, FilterIcon } from "lucide-react";
import HStack from "@/ui/HStack";
import Card from "@/ui/Card";
import LeaveTypeFilterSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/LeaveTypeFilterSelect";
import StatusFilterSelect from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/StatusFilterSelect";
import ViewModeSegmentedControl from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/ViewModeSegmentedControl";
import LeaveTable from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/LeaveTable";
import LeaveCalendar from "@/app/emp-ws/[contractorId]/(workspace)/employments/[employmentId]/payroll/leave-terms/[leaveTermId]/leaves/_ui/LeaveCalendar";

export default async function LeaveHistoryCard({
  selectedTerm,
  leaveEnts,
  params,
  searchParams,
}) {
  let { view } = await searchParams;
  if (isEmpty(view)) view = "table";

  return (
    <Card variant="transparent" className="space-y-4">
      <HStack className="justify-end-safe gap-x-6 sm:gap-x-6">
        <HStack className="flex-nowrap">
          <span className="hidden text-sm text-muted sm:block">Filters</span>
          <span className="block sm:hidden">
            <FilterIcon size={16} className="stroke-muted" />
          </span>
          <LeaveTypeFilterSelect leaveEnts={leaveEnts} />
          <StatusFilterSelect />
        </HStack>

        <HStack className="flex-nowrap">
          <span className="hidden text-sm text-muted sm:block">View as</span>
          <span className="block sm:hidden">
            <EyeIcon size={16} className="stroke-muted" />
          </span>
          <ViewModeSegmentedControl />
        </HStack>
      </HStack>

      {view === "table" && (
        <LeaveTable params={params} searchParams={searchParams} />
      )}

      {view === "calendar" && (
        <LeaveCalendar
          selectedTerm={selectedTerm}
          params={params}
          searchParams={searchParams}
          debug={false}
        />
      )}
    </Card>
  );
}
