import { toSimpleDate } from "@/formatters/date";
import { addMonths, isBefore, parseISO, startOfToday } from "date-fns";
import { isEmpty } from "lodash";

export default function isEligibleToRenewal(employment) {
  const { fmId, status, expiredDate, lastDayDate, activeAgreement } =
    employment;

  let errors = [];

  if (status !== "ACTIVE") {
    errors.push(`${fmId} must be active`);
  }

  if (!isEmpty(lastDayDate)) {
    errors.push(
      `${fmId} has last day date on ${toSimpleDate(lastDayDate, { zeroPadded: true })}`,
    );
  }

  if (
    !isEmpty(expiredDate) &&
    isBefore(parseISO(expiredDate), addMonths(startOfToday(), 3))
  ) {
    errors.push(
      `${fmId} has expired more than 3 months ago (Expired date=${toSimpleDate(expiredDate, { zeroPadded: true })})`,
    );
  }

  if (isEmpty(activeAgreement)) {
    errors.push(`${fmId} has no active agreement`);
  }

  return { eligible: isEmpty(errors), errors };
}
