import { addDays, differenceInMinutes, isDate, parse } from "date-fns";
import { toNumber, reduce, forEach } from "lodash";

// Parser
const parseHours = (minutes) => Math.abs(minutes ?? 0) / 60;

const parseMinutes = (hours) => (hours ? hours * 60 : 0);

const parseDateFromTime = (time) =>
  time ? parse(time, "hh:mm a", new Date()) : null;

const parsedDateFromMonthYear = (year, month) =>
  year && month ? new Date(year, month - 1) : null; // month is 0-based

// Getter
const getWorkingDays = (leaveTermWorkingHours) => {
  return reduce(
    leaveTermWorkingHours,
    (result, item) => {
      forEach(item.workingDays, (day) => {
        result[day] = {
          normalTimeIn: item.workingHourFrom,
          normalTimeOut: item.workingHourTo,
          normalBreakDuration: toNumber(item.breakDuration),
        };
      });
      return result;
    },
    {},
  );
};

const getTotalHoursWithoutBreak = (timeIn, timeOut) => {
  const fromDate = parseDateFromTime(timeIn);
  const toDate = checkIfNextDay(fromDate, parseDateFromTime(timeOut));

  if (!isDate(fromDate) || !isDate(toDate)) return 0;

  const totalMinutes = Math.abs(differenceInMinutes(toDate, fromDate));

  return totalMinutes / 60 || 0;
};

const getTotalHours = (timeIn, timeOut, breakMinutes = 0) => {
  const fromDate = parseDateFromTime(timeIn);
  const toDate = checkIfNextDay(fromDate, parseDateFromTime(timeOut));

  if (!isDate(fromDate) || !isDate(toDate)) return 0;

  const totalMinutes = Math.abs(differenceInMinutes(toDate, fromDate));

  return (totalMinutes - breakMinutes) / 60 || 0;
};

const getCurrentLeaveTermNormalHours = (leaveTermWorkingHour) => {
  const { normalTimeIn, normalTimeOut, normalBreakDuration } =
    leaveTermWorkingHour || {};

  return getTotalHours(normalTimeIn, normalTimeOut, normalBreakDuration);
};

// Checker
const checkIfNextDay = (fromDate, toDate) => {
  if (toDate && fromDate && toDate < fromDate) {
    return addDays(toDate, 1);
  }
  return toDate;
};

const checkBreakEntitlement = (timeIn, timeOut) => {
  if (!isDate(timeIn) || !isDate(timeOut)) return false;

  return (
    timeIn && timeOut && Math.abs(differenceInMinutes(timeIn, timeOut)) / 60 > 6
  );
};

const calculateBreak = (actual, entitled, timeIn, timeOut) => {
  if (isDifferentBreakTime(actual, entitled)) {
    return parseHours(actual);
  }
  return checkBreakEntitlement(timeIn, timeOut) ? parseHours(entitled) : 0;
};

// const calculateTotalHours = (day, workHours) => {
//   const {
//     time_in,
//     time_out,
//     normal_break,
//     overtime_in,
//     overtime_out,
//     overtime_break,
//   } = day;

//   const entNormalBreak = workHours.normal_break;
//   const entOvertimeBreak = workHours.overtime_break;
//   let normalBreak = 0;
//   let overtimeBreak = 0;
//   const timeIn = parseDateFromTime(time_in);
//   const overtimeIn = parseDateFromTime(overtime_in);
//   let timeOut = parseDateFromTime(time_out);
//   let overtimeOut = parseDateFromTime(overtime_out);

//   if (isEarlierThan(timeIn, timeOut)) {
//     timeOut = addDays(timeOut, 1);
//   }

//   if (isDifferentBreakTime(normal_break, entNormalBreak)) {
//     normalBreak = (normal_break && Math.abs(normal_break / 60)) || 0;
//   } else if (isEntitledForBreak(timeIn, timeOut)) {
//     normalBreak = (entNormalBreak && Math.abs(entNormalBreak / 60)) || 0;
//   }

//   if (isEarlierThan(overtimeIn, overtimeOut)) {
//     overtimeOut = addDays(overtimeOut, 1);
//   }

//   if (isDifferentBreakTime(overtime_break, entOvertimeBreak)) {
//     overtimeBreak = parseHours(overtime_break);
//   } else if (isEntitledForBreak(overtimeIn, overtimeOut)) {
//     overtimeBreak = parseHours(entOvertimeBreak);
//   }

//   const totalNormalHours = getTotalHours(timeIn, timeOut, normalBreak);
//   const totalOvertimeHours = getTotalHours(
//     overtimeIn,
//     overtimeOut,
//     overtimeBreak,
//   );

//   return {
//     normal_break: parseMinutes(normalBreak),
//     overtime_break: parseMinutes(overtimeBreak),
//     normal_hours: totalNormalHours,
//     overtime_hours: totalOvertimeHours,
//     total_hours: sum([totalNormalHours, totalOvertimeHours]),
//   };
// };

export {
  parseHours,
  parseMinutes,
  parseDateFromTime,
  parsedDateFromMonthYear,
  getWorkingDays,
  getTotalHoursWithoutBreak,
  getTotalHours,
  checkIfNextDay,
  getCurrentLeaveTermNormalHours,
};
