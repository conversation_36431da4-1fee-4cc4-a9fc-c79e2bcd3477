import { toSimpleDate } from "@/formatters/date";
import { add, eachDayOfInterval, getDate } from "date-fns";
import pluralize from "pluralize";

/**
 * 3 use-cases to be aware of when dealing with getting back the endDate after a certain duration.
 * Most of the time a 12 months duration is easier to figure out.
 * It is only when we are dealing with less than 1 year or days that we need to be careful.
 *
 * ### Use-case 1: Beginning of the month
 *
 *     add(new Date(2023, 0, 1), { months: 1}) = new Date(2023, 1, 1) // 2023-02-01
 *
 * Here, since the day is the same, we can subtract 1 day to get new Date(2023, 0, 31)
 *
 *
 *
 * ### Use-case 2: Middle of the month
 *
 *     add(new Date(2023, 0, 15), { months: 1 }) = new Date(2023, 1, 15) // 2023-02-15
 *
 * Here, since the day is the same, we can subtract 1 day to get new Date(2023, 1, 14)
 *
 *
 *
 * ### Use-case 3: End of the month
 *
 *     add(new Date(2023, 0, 31), { months: 1 }) = new Date(2023, 1, 28) // 2023-02-28
 *
 * Here, we cannot subtract 1 day as that will mean it will become new Date(2023, 1, 27)
 *
 *
 *
 * ### Use-case 4: Using days as duration
 *
 *     add(new Date(2023, 0, 1), { days: 5 }) = new Date(2023, 0, 6) // 2023-01-06
 *
 * Here, we need to subtract 1 day to get new Date(2023, 0, 5)
 *
 * #### Usage
 *
 * ```
 *     const finalDate = dateAfter({ startDate: new Date(2023, 1, 1), length: 1, unit: "month" }); // 2023-02-28
 * ```
 *
 * @param {Date|String} startDate
 * @param {number} length - Integer
 * @param {string} unit - day, days, month, months, year, years
 * @returns {Date}
 */
export function dateAfter({ startDate, length, unit = "month" }) {
  if (!startDate) {
    console.warn("startDate is required!");
    return null;
  }

  if (!length) {
    console.warn("length is required!");
    return null;
  }

  if (!unit) {
    console.warn("unit is required!");
    return null;
  } else if (
    !["day", "days", "month", "months", "year", "years"].includes(unit)
  ) {
    console.warn(
      `\`unit\` is expecting \`day\`, \`month\` or \`year\`, but got \`${unit}\`!`,
    );
    return null;
  }

  // date-fns only accepts plural unit
  switch (unit) {
    case "day":
      unit = "days";
      break;
    case "month":
      unit = "months";
      break;
    case "year":
      unit = "years";
      break;
  }

  let endDate = add(startDate, { [unit]: length });

  if (unit === "days") {
    endDate = add(endDate, { days: -1 });
  } else {
    const startDateDay = getDate(startDate);
    const endDateDay = getDate(endDate);

    if (startDateDay === endDateDay) {
      endDate = add(endDate, { days: -1 });
    }
  }

  return endDate;
}

export function testDates() {
  const startDate = new Date(2024, 0, 1); // January 1, 2024
  const endDate = new Date(2024, 2, 31); // March 31, 2024

  const dates = eachDayOfInterval({
    start: startDate,
    end: endDate,
  });

  let results = {};
  dates.forEach((startDate) => {
    const label = toSimpleDate(startDate, { zeroPadded: true });
    results[label] = {};

    ["day", "month", "year"].forEach((unit) => {
      [1, 3, 6].forEach((length) => {
        const finalDate = dateAfter({ startDate, length, unit });
        results[label][pluralize(unit, length, true)] = toSimpleDate(
          finalDate,
          { zeroPadded: true },
        );
      });
    });
  });

  console.group("%cDay Tests", "color: orange; font-size: 14px;");
  console.table(results, ["1 day", "3 days", "6 days"]);
  console.groupEnd("End of day test");

  console.group("%cMonth Tests", "color: orange; font-size: 14px;");
  console.table(results, ["1 month", "3 months", "6 months"]);
  console.groupEnd("End of month test");

  console.group("%cYear Tests", "color: orange; font-size: 14px;");
  console.table(results, ["1 year", "3 years", "6 years"]);
  console.groupEnd("End of year test");

  return results;
}
