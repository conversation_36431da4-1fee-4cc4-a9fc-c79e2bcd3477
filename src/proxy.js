import { NextResponse } from "next/server";
import { jwtVerify } from "jose";

const SIGNING_KEY =
  "225976d77a035f7c8dc2f01707315533d4975d136e74f808e8b3d347c60e485bf8067ab4f96ffa22dc85671286bee5061be88bd3efd3418a89fbc8e1ae19045b";

export async function proxy(request) {
  const requestHeaders = new Headers(request.headers);

  // Store the current request pathname in a custom header
  requestHeaders.set("HTTP_REQUEST_AS_PATH", request.nextUrl.pathname);

  const autCookie = request.cookies.get("AUT");

  if (autCookie) {
    const secret = new TextEncoder().encode(SIGNING_KEY);

    // try catch and logout due to expired autCookie...
    try {
      const verified = await jwtVerify(autCookie.value, secret);

      if (verified) {
        const payload = verified.payload;

        // console.log("userType", payload.userType, request.nextUrl.pathname);

        if (
          payload.userType === "Contractor" &&
          !request.nextUrl.pathname.startsWith("/emp-ws")
        ) {
          return NextResponse.redirect(
            `${process.env.JOBLINE_NEXT_API_URL}/sessions/new?FORBIDDEN-CA`,
          );
        }

        if (
          payload.userType === "Client" &&
          !request.nextUrl.pathname.startsWith("/org-ws")
        ) {
          return NextResponse.redirect(
            `${process.env.JOBLINE_NEXT_API_URL}/sessions/new?FORBIDDEN-CL`,
          );
        }
      }
    } catch (error) {
      return NextResponse.redirect(
        `${process.env.JOBLINE_NEXT_API_URL}/sessions/sign_out`,
      );
    }
  } else {
    return NextResponse.redirect(
      `${process.env.JOBLINE_NEXT_API_URL}/sessions/new?ALIEN`,
    );
  }

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|auth|after-sign-in|after-sign-out|images).*)",
  ],
};
