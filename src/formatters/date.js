import {
  format,
  parseISO,
  isDate,
  isValid,
  formatDistanceStrict,
  formatDistanceToNow,
  intlFormatDistance,
} from "date-fns";
import Toggle from "@/ui/Toggle";
import React from "react";

export const FORMAL_DATE_FORMAT = "d MMM yyyy";
export const FORMAL_DATE_ZERO_PADDED_FORMAT = "dd MMM yyyy";
export const FORMAL_DATETIME_FORMAT = "d MMM yyyy h:mm aa";
export const FORMAL_DATETIME_ZERO_PADDED_FORMAT = "dd MMM yyyy hh:mm aa";
export const SIMPLE_DATE_FORMAT = "d/M/yyyy";
export const SIMPLE_DATE_ZERO_PADDED_FORMAT = "dd/MM/yyyy";
export const SIMPLE_DATETIME_FORMAT = "d/M/yyyy h:mm aa";
export const SIMPLE_DATETIME_ZERO_PADDED_FORMAT = "dd/MM/yyyy hh:mm aa";

export const formatDate = (
  date,
  { showDistance = false, longFormat = false } = {},
) => {
  if (!date) return "";

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  const formatString = longFormat ? "E, MMM d, yyyy" : "d/M/yyyy";

  return [
    format(parsedDate, formatString),
    showDistance ? ` (${formatDistanceToNow(parsedDate)})` : null,
  ]
    .filter(Boolean)
    .join(" ");
};

export const FormatDateToggle = ({ date, className }) => {
  if (!date) return null;

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  if (isValid(parsedDate)) {
    const longFormat = format(parsedDate, "E, MMM d, yyyy");
    const shortFormat = format(parsedDate, "d/M/yyyy");

    return <Toggle on={shortFormat} off={longFormat} className={className} />;
  } else {
    return <strong className="text-red-500">Invalid date</strong>;
  }
};

export const formatDateTime = (
  date,
  { showDistance = false, longFormat = false } = {},
) => {
  if (!date) return "";

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  const formatString = longFormat
    ? "E, MMM d, yyyy, h:mm aa"
    : "d/M/yyyy h:mm aa";

  return [
    format(parsedDate, formatString),
    showDistance ? ` (${intlFormatDistance(parsedDate, new Date())})` : null,
  ]
    .filter(Boolean)
    .join(" ");
};

export const periodLength = (startDate, endDate) => {
  if (!startDate || !endDate) return "";

  let parsedStartDate = startDate;
  let parsedEndDate = endDate;

  if (!isDate(startDate)) {
    parsedStartDate = parseISO(startDate);
  }

  if (!isDate(endDate)) {
    parsedEndDate = parseISO(endDate);
  }

  return formatDistanceStrict(parsedStartDate, parsedEndDate);
};

export const formatWorkMonth = (
  date,
  { showDistance = false, longFormat = false } = {},
) => {
  if (!date) return "";

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  const formatString = longFormat ? "MMMM yy" : "MMM yy";

  return [
    format(parsedDate, formatString),
    showDistance ? ` (${formatDistanceToNow(parsedDate)})` : null,
  ]
    .filter(Boolean)
    .join(" ");
};

export function PeriodDateText({
  from,
  to,
  className,
  dateFormat = "MMM d, yyyy",
}) {
  // We allow `to` to be empty to represent `present`
  if (!from) return null;

  const formatFrom = format(parseISO(from), dateFormat);
  let formatTo = "";

  if (!to) {
    formatTo = "Present";
  } else {
    formatTo = format(parseISO(to), dateFormat);
  }

  return (
    <span className={className}>
      {formatFrom} &mdash; {formatTo}
    </span>
  );
}

export function toFormalDate(date, { zeroPadded = false } = {}) {
  if (!date) return "";

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  return format(
    parsedDate,
    zeroPadded ? FORMAL_DATE_ZERO_PADDED_FORMAT : FORMAL_DATE_FORMAT,
  );
}

export function toFormalDateTime(date, { zeroPadded = false } = {}) {
  if (!date) return "";

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  return format(
    parsedDate,
    zeroPadded ? FORMAL_DATETIME_ZERO_PADDED_FORMAT : FORMAL_DATETIME_FORMAT,
  );
}

export function toSimpleDate(date, { zeroPadded = false } = {}) {
  if (!date) return "";

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  return format(
    parsedDate,
    zeroPadded ? SIMPLE_DATE_ZERO_PADDED_FORMAT : SIMPLE_DATE_FORMAT,
  );
}

export function toSimpleDateTime(date, { zeroPadded = false } = {}) {
  if (!date) return "";

  let parsedDate = date;

  if (!isDate(date)) {
    parsedDate = parseISO(date);
  }

  return format(
    parsedDate,
    zeroPadded ? SIMPLE_DATETIME_ZERO_PADDED_FORMAT : SIMPLE_DATETIME_FORMAT,
  );
}
