import { Mountain as MountainIcon } from "@blueprintjs/icons";
import {
  BabyI<PERSON>,
  CalendarSyncIcon,
  HospitalIcon,
  HouseIcon,
  CakeIcon,
  GemIcon,
  PlaneIcon,
  HandHeartIcon,
  StethoscopeIcon,
  OmegaIcon,
  BlendIcon,
  SmileIcon,
  ShieldIcon,
} from "lucide-react";

export default function LeaveIcon({ type, ...props }) {
  switch (type) {
    case "AL":
    case "Annual":
    case "Annual Leave":
      return <PlaneIcon {...props} />;
    case "ML":
    case "Medical":
    case "Medical Leave":
    case "WML":
    case "WICA Medical":
    case "WICA Medical Leave":
      return <StethoscopeIcon {...props} />;
    case "HOSL":
    case "Hospitalisation":
    case "Hospitalisation Leave":
    case "WHOSL":
    case "WICA Hospitalisation":
    case "WICA Hospitalisation Leave":
      return <HospitalIcon {...props} />;
    case "RL":
    case "Replacement":
    case "Replacement Leave":
      return <CalendarSyncIcon {...props} />;
    case "BDL":
    case "Birthday":
    case "Birthday Leave":
      return <CakeIcon {...props} />;
    case "MRL":
    case "Marriage":
    case "Marriage Leave":
      return <GemIcon {...props} />;
    case "CPL":
    case "Compassionate":
    case "Compassionate Leave":
      return <HandHeartIcon {...props} />;
    case "MTNL":
    case "Maternity":
    case "Maternity Leave":
      if (props.size <= 16) {
        return <MaternityXsIcon {...props} />;
      } else if (props.size <= 20) {
        return <MaternitySIcon {...props} />;
      } else if (props.size <= 24) {
        return <MaternityMIcon {...props} />;
      } else if (props.size <= 28) {
        return <MaternityLIcon {...props} />;
      } else {
        return <MaternityXlIcon {...props} />;
      }
    case "PL":
    case "Paternity":
    case "Paternity Leave":
      return <OmegaIcon {...props} />;
    case "SPL":
    case "Shared Parental":
    case "Shared Parental Leave":
      return <BlendIcon {...props} />;
    case "CCL":
    case "Childcare":
    case "Childcare Leave":
      return <BabyIcon {...props} />;
    case "ECCL":
    case "Extended Childcare":
    case "Extended Childcare Leave":
      return <SmileIcon {...props} />;
    case "FCL":
    case "Family Care":
    case "Family Care Leave":
      return <HouseIcon {...props} />;
    case "ICT":
    case "Reservist":
    case "Reservist Leave":
      return <ShieldIcon {...props} />;
    default:
      return <MountainIcon {...props} />;
  }
}

function MaternityXsIcon({ ...props }) {
  const { strokeWidth = 2 } = props;
  let finalStrokeWidth = strokeWidth - 0.5;
  if (finalStrokeWidth < 0.75) finalStrokeWidth = 0.75;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="-0.5 -0.5 16 16"
      height="16"
      width="16"
      className={props.className}
    >
      <path
        d="M13.408 14.536c0-3.74-1.274-4.141-1.513-6.608-.032-1.055.071-2.11.306-3.138M3.59.5a8.362 8.362 0 0 0-1.09.598c-.802.74.362 2.18 1.567 2.625a5 5 0 0 0 2.36 9.408M4.535 12.759v1.777"
        stroke={props.color || "#000"}
        // strokeWidth={finalStrokeWidth}
        strokeWidth={props.strokeWidth || 2}
      />
      <path
        d="M13.575.464c-.554 2.987-2.388 8.889-7.976 9.64l-.058.005a1.617 1.617 0 0 1-.375-3.212C8.435 6.46 9.74 2.174 9.948.54"
        stroke={props.color || "#000"}
        // strokeWidth={finalStrokeWidth}
        strokeWidth={props.strokeWidth || 2}
      />
    </svg>
  );
}

function MaternitySIcon({ ...props }) {
  const { strokeWidth = 2 } = props;
  let finalStrokeWidth = strokeWidth - 0.25;
  if (finalStrokeWidth < 1) finalStrokeWidth = 1;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="-0.625 -0.625 20 20"
      height="20"
      width="20"
      className={props.className}
    >
      <path
        d="M16.76 18.17c0-4.675-1.592-5.176-1.89-8.26-.04-1.32.088-2.637.382-3.923M4.487.625c-.472.215-.927.465-1.362.748-1.002.924.453 2.725 1.96 3.28a6.25 6.25 0 0 0 2.948 11.761M5.669 15.948v2.221"
        stroke={props.color || "#000"}
        // strokeWidth={finalStrokeWidth}
        strokeWidth={props.strokeWidth || 2}
      />
      <path
        d="M16.97.58c-.694 3.733-2.986 11.11-9.972 12.049l-.072.008a2.021 2.021 0 0 1-.47-4.015c4.088-.547 5.719-5.904 5.979-7.947"
        stroke={props.color || "#000"}
        // strokeWidth={finalStrokeWidth}
        strokeWidth={props.strokeWidth || 2}
      />
    </svg>
  );
}

function MaternityMIcon({ ...props }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      height="24"
      width="24"
      className={props.className}
    >
      <path
        d="M21.453 23.257c0-5.984-2.038-6.625-2.42-10.572a19.822 19.822 0 0 1 .489-5.021M5.744.8c-.605.275-1.188.595-1.744.957-1.282 1.183.58 3.489 2.508 4.2a8 8 0 0 0 3.775 15.053M7.256 20.414v2.843"
        stroke={props.color || "#000"}
        strokeWidth={props.strokeWidth || 2}
      />
      <path
        d="M21.72.743c-.886 4.778-3.82 14.221-12.762 15.422l-.093.01a2.587 2.587 0 0 1-.6-5.139c5.23-.7 7.319-7.557 7.652-10.172"
        stroke={props.color || "#000"}
        strokeWidth={props.strokeWidth || 2}
      />
    </svg>
  );
}

function MaternityLIcon({ ...props }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="-0.875 -0.875 28 28"
      height="28"
      width="28"
      className={props.className}
    >
      <path
        d="M23.464 25.437c0-6.545-2.23-7.246-2.647-11.563a21.68 21.68 0 0 1 .535-5.492M6.282.875c-.66.301-1.298.651-1.907 1.047-1.403 1.294.634 3.816 2.743 4.594a8.75 8.75 0 0 0 4.129 16.464M7.936 22.328v3.11"
        stroke={props.color || "#000"}
        strokeWidth={props.strokeWidth || 2}
      />
      <path
        d="M23.757.813c-.97 5.226-4.18 15.554-13.96 16.867l-.101.011a2.83 2.83 0 0 1-.656-5.62c5.721-.766 8.005-8.266 8.369-11.126"
        stroke={props.color || "#000"}
        strokeWidth={props.strokeWidth || 2}
      />
    </svg>
  );
}

function MaternityXlIcon({ ...props }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="-1 -1 32 32"
      height="32"
      width="32"
      className={props.className}
    >
      <path
        d="M26.816 29.071c0-7.48-2.548-8.281-3.025-13.215-.064-2.11.141-4.22.612-6.276M7.18 1c-.756.344-1.485.744-2.18 1.196-1.603 1.48.724 4.362 3.134 5.25a10 10 0 0 0 4.72 18.816M9.07 25.517v3.554"
        stroke={props.color || "#000"}
        strokeWidth={props.strokeWidth || 2}
      />
      <path
        d="M27.151.929C26.042 6.9 22.375 18.705 11.197 20.206l-.116.013a3.234 3.234 0 0 1-.75-6.424c6.539-.875 9.149-9.446 9.565-12.715"
        stroke={props.color || "#000"}
        strokeWidth={props.strokeWidth || 2}
      />
    </svg>
  );
}
