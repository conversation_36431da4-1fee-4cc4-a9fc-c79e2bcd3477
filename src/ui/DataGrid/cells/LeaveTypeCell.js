"use client";

import React from "react";
import { get, isEmpty } from "lodash";
import cn from "@/lib/cn";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import LeaveIcon from "@/icons/leaves/icons";

export default React.memo(function LeaveTypeCell({
  rowIndex,
  data,
  propertyName,
  column,
  size,
}) {
  if (!data[rowIndex]) return null;

  const getPropertyValue = get(data[rowIndex], propertyName);
  if (!getPropertyValue) return null;

  const mcSourcePropertyName = column.mcSourcePropertyName;
  const mcSource = get(data[rowIndex], mcSourcePropertyName);

  return (
    <VStack className="gap-0">
      <HStack>
        {column.icon?.show && (
          <LeaveIcon
            type={getPropertyValue}
            size={column.icon.size}
            strokeWidth={
              ["MTNL", "Maternity", "Maternity Leave"].includes(
                getPropertyValue,
              )
                ? 1.5
                : 2
            }
          />
        )}
        <span
          className={cn("text-base", {
            "text-sm": size === "sm",
            "text-lg": size === "lg",
          })}
        >
          {getPropertyValue}
        </span>
      </HStack>

      {!isEmpty(mcSource) && (
        <span className="text-sm text-muted"> Type of visit: {mcSource}</span>
      )}
    </VStack>
  );
});
