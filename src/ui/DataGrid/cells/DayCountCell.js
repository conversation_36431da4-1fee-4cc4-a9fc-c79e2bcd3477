"use client";

import React from "react";
import { get } from "lodash";
import cn from "@/lib/cn";
import pluralize from "pluralize";

export default React.memo(function DayCountCell({
  rowIndex,
  data,
  propertyName,
  size,
  showUnitInFull = false,
}) {
  if (!data[rowIndex]) return null;

  const getPropertyValue = get(data[rowIndex], propertyName);
  if (!getPropertyValue) return null;

  return (
    <span
      className={cn("text-base", {
        "text-sm": size === "sm",
        "text-lg": size === "lg",
      })}
    >
      {showUnitInFull
        ? pluralize("day", getPropertyValue, true)
        : `${getPropertyValue}d`}
    </span>
  );
});
