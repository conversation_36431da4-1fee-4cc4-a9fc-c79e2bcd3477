"use client";

import React from "react";
import { get } from "lodash";
import cn from "@/lib/cn";

export default React.memo(function LeaveUnpaidCountCell({
  rowIndex,
  data,
  propertyName,
  column,
  size,
}) {
  if (!data[rowIndex]) return null;

  const getPropertyValue = get(data[rowIndex], propertyName);
  if (getPropertyValue === null || getPropertyValue === undefined) return null;

  return (
    <span
      className={cn(
        "text-base",
        {
          "text-sm": size === "sm",
          "text-lg": size === "lg",
        },
        column.className,
      )}
    >
      {getPropertyValue > 0 ? getPropertyValue : "-"}
    </span>
  );
});
