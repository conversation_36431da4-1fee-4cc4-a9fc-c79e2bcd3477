"use client";

import React from "react";
import get from "lodash/get";
import { isEmpty } from "lodash";
import { format, parseISO } from "date-fns";
import Toggle from "@/ui/Toggle";
import cn from "@/lib/cn";
import { FORMAL_DATE_FORMAT, SIMPLE_DATE_FORMAT } from "@/formatters/date";

export default React.memo(function LeaveDateCell({
  rowIndex,
  data,
  propertyName,
  size,
}) {
  if (!data[rowIndex]) return null;

  // Because propertyName can be a nested property like "organization.name"
  const getPropertyValue = get(data[rowIndex], propertyName);

  if (isEmpty(getPropertyValue)) return null;

  const date = parseISO(getPropertyValue);

  const longFormat = format(date, `${FORMAL_DATE_FORMAT}, EEEE`);
  const shortFormat = format(date, `${SIMPLE_DATE_FORMAT}, EEE`);

  return (
    <span
      className={cn("text-base", {
        "text-sm": size === "sm",
        "text-lg": size === "lg",
      })}
    >
      <Toggle on={shortFormat} off={longFormat} />
    </span>
  );
});
