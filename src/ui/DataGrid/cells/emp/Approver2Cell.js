"use client";

import React from "react";
import useQuerySearchParams from "@/hooks/useQuerySearchParams";
import { toSimpleDateTime } from "@/formatters/date";
import VStack from "@/ui/VStack";
import Highlighter from "react-highlight-words";

export default React.memo(function Approver2Cell({ rowIndex, data }) {
  const { searchWords } = useQuerySearchParams();
  if (!data[rowIndex]) return null;
  const { approver2 } = data[rowIndex];

  if (!approver2) return null;

  return (
    <VStack className="gap-0">
      <span className="text-base">
        <Highlighter
          textToHighlight={approver2.approver.fmIdName}
          autoEscape={true}
          searchWords={searchWords}
        />
      </span>
      <span className="text-sm text-muted">
        Assigned by: {approver2.modifier?.name}{" "}
        {toSimpleDateTime(approver2.updatedAt)}
      </span>
    </VStack>
  );
});
