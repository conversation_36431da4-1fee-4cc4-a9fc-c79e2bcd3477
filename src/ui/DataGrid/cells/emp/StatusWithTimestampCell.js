"use client";

import { toSimpleDateTime } from "@/formatters/date";
import VStack from "@/ui/VStack";
import StatusText from "@/ui/StatusText";

export default function StatusWithTimestampCell({ data, rowIndex }) {
  const { status, statusUpdatedAt } = data[rowIndex];

  if (!statusUpdatedAt) console.warn("No statusUpdatedAt detected!");

  return (
    <VStack className="gap-0">
      <StatusText status={status} className="text-base" />
      <span className="text-muted text-sm">
        {toSimpleDateTime(statusUpdatedAt)}
      </span>
    </VStack>
  );
}
