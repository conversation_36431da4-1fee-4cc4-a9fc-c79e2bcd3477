"use client";

import React from "react";
import useQuerySearchParams from "@/hooks/useQuerySearchParams";
import { toSimpleDateTime } from "@/formatters/date";
import VStack from "@/ui/VStack";
import Highlighter from "react-highlight-words";

export default React.memo(function Approver1Cell({ rowIndex, data }) {
  const { searchWords } = useQuerySearchParams();
  if (!data[rowIndex]) return null;
  const { approver1 } = data[rowIndex];

  if (!approver1) return null;

  return (
    <VStack className="gap-0">
      <span className="text-base">
        <Highlighter
          textToHighlight={approver1.approver.fmIdName}
          autoEscape={true}
          searchWords={searchWords}
        />
      </span>
      <span className="text-sm text-muted">
        Assigned by: {approver1.modifier?.name}{" "}
        {toSimpleDateTime(approver1.updatedAt)}
      </span>
    </VStack>
  );
});
