"use client";

import React from "react";
import get from "lodash/get";
import locale from "date-fns/locale/en-US";
import { isEmpty } from "lodash";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { format, formatDistanceToNowStrict, parseISO } from "date-fns";
import { FORMAL_DATE_FORMAT, SIMPLE_DATE_FORMAT } from "@/formatters/date";
import { ProgressBarLink } from "@/ui/ProgressBar";
import Toggle from "@/ui/Toggle";

const customLocale = {
  lessThanXSeconds: "{{count}}sec",
  xSeconds: "{{count}}sec",
  halfAMinute: "30sec",
  lessThanXMinutes: "{{count}}min",
  xMinutes: "{{count}}min",
  aboutXHours: "{{count}}hr",
  xHours: "{{count}}hr",
  xDays: "{{count}}d",
  aboutXWeeks: "{{count}}wk",
  xWeeks: "{{count}}wk",
  aboutXMonths: "{{count}}m",
  xMonths: "{{count}}m",
  aboutXYears: "{{count}}y",
  xYears: "{{count}}y",
  overXYears: "{{count}}y",
  almostXYears: "{{count}}y",
};

function formatDistance(token, count, options) {
  options = options || {};

  const result = customLocale[token].replace("{{count}}", count);

  if (options.addSuffix) {
    if (options.comparison > 0) {
      return "in " + result;
    } else {
      return result + " ago";
    }
  }

  return result;
}

export default React.memo(function AnniversaryDateCell({
  rowIndex,
  data,
  propertyName,
  column,
}) {
  const params = useParams();
  const [dangerouslyEvalHref, setDangerouslyEvalHref] = useState("");
  const row = data[rowIndex];
  const { href } = column;

  useEffect(() => {
    if (row && href) {
      setDangerouslyEvalHref(eval("`" + href + "`"));
    }
  }, [row, href, params]);

  if (!data[rowIndex]) return null;

  // Because propertyName can be a nested property like "organization.name"
  const getPropertyValue = get(data[rowIndex], propertyName);

  if (isEmpty(getPropertyValue)) return null;

  const date = parseISO(getPropertyValue);
  const distance = formatDistanceToNowStrict(date, {
    addSuffix: false,
    locale: { ...locale, formatDistance },
  });

  const longFormat = format(date, FORMAL_DATE_FORMAT);
  const shortFormat = format(date, SIMPLE_DATE_FORMAT);

  if (href) {
    return (
      <div
        className="flex items-center justify-between gap-1 text-base"
        title={longFormat}
      >
        <ProgressBarLink
          href={dangerouslyEvalHref}
          className="text-base text-link"
        >
          {shortFormat}
        </ProgressBarLink>
        <span className="text-sm text-gray-500">{distance}</span>
      </div>
    );
  } else {
    return (
      <div
        className="flex items-center justify-between gap-1 text-base"
        title={longFormat}
      >
        <Toggle on={shortFormat} off={longFormat} />
        <span className="text-sm text-gray-500">{distance}</span>
      </div>
    );
  }
});
