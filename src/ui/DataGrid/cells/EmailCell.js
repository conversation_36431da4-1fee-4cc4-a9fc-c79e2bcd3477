"use client";

import cn from "@/lib/cn";
import React from "react";
import get from "lodash/get";
import Highlighter from "react-highlight-words";
import useQuerySearchParams from "@/hooks/useQuerySearchParams";

export default React.memo(function EmailCell({
  rowIndex,
  data,
  propertyName,
  size,
}) {
  const { searchWords } = useQuerySearchParams();

  if (!data[rowIndex]) return null;

  const getPropertyValue = get(data[rowIndex], propertyName);

  return (
    <a
      href={`mailto:${data[rowIndex][propertyName]}`}
      className={cn("text-link text-base dark:text-blue-400", {
        "text-sm": size === "sm",
        "text-lg": size === "lg",
      })}
    >
      <Highlighter
        textToHighlight={getPropertyValue}
        autoEscape={true}
        searchWords={searchWords}
      />
    </a>
  );
});
