import { Tooltip as TooltipPrimitive } from "radix-ui";
import cn from "@/lib/cn";

export default function Tooltip({
  children,
  content,
  side = "bottom",
  className,
  triggerClassName,
  ...props
}) {
  const contentClassName = cn(
    "z-100 rounded-full bg-foreground/60 px-3 py-1 text-sm text-background",
    "dark:bg-neutral-600/60 dark:text-foreground",
    className,
  );

  return (
    <TooltipPrimitive.Root>
      <TooltipPrimitive.Trigger asChild>
        <span className={triggerClassName}>{children}</span>
      </TooltipPrimitive.Trigger>

      <TooltipPrimitive.Portal>
        <TooltipPrimitive.Content
          side={side}
          className={contentClassName}
          collisionPadding={8}
          {...props}
        >
          {content}
        </TooltipPrimitive.Content>
      </TooltipPrimitive.Portal>
    </TooltipPrimitive.Root>
  );
}
