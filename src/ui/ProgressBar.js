"use client";

import {
  AnimatePresence,
  motion,
  useMotionTemplate,
  useSpring,
} from "framer-motion";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  createContext,
  startTransition,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import cn from "@/lib/cn";

const ProgressBarContext = createContext();

export function useProgressBar() {
  let progress = useContext(ProgressBarContext);

  if (progress === null) {
    throw new Error("useProgressBar must be used within a ProgressBarProvider");
  }

  return progress;
}

export function ProgressBar({ className, children }) {
  let progress = useProgress();
  let width = useMotionTemplate`${progress.value}%`;

  const classNames = cn("fixed top-0 z-[100] h-0.5 bg-orange-500", className);

  return (
    <ProgressBarContext.Provider value={progress}>
      <AnimatePresence onExitComplete={progress.reset}>
        {progress.state !== "complete" && (
          <motion.div
            style={{ width }}
            exit={{ opacity: 0 }}
            className={classNames}
          />
        )}
      </AnimatePresence>
      {children}
    </ProgressBarContext.Provider>
  );
}

export function ProgressBarLink({
  href,
  children,
  activeClassName = "",
  className = "",
  ...props
}) {
  let progress = useProgressBar();
  let router = useRouter();
  const pathname = usePathname();

  const active = pathname === href;

  return (
    <Link
      href={href}
      className={cn(className, { [activeClassName]: active })}
      data-active={active ? "" : undefined}
      onClick={(e) => {
        e.preventDefault();

        const blank = "_blank";

        // Check if the Ctrl or Cmd key is pressed for a new tab
        if (e.metaKey || e.ctrlKey) {
          window.open(href.toString(), blank, "noopener,noreferrer");
        } else {
          progress.start();

          startTransition(() => {
            router.push(href.toString());
            progress.done();
          });
        }
      }}
      {...props}
    >
      {children}
    </Link>
  );
}

function useProgress() {
  const [state, setState] = useState("initial");

  let value = useSpring(0, {
    damping: 25,
    mass: 0.5,
    stiffness: 300,
    restDelta: 0.1,
  });

  useInterval(
    () => {
      // If we start progress but the bar is currently complete, reset it first.
      if (value.get() === 100) {
        value.jump(0);
      }

      let current = value.get();

      let diff;
      if (current === 0) {
        diff = 15;
      } else if (current < 50) {
        diff = rand(1, 10);
      } else {
        diff = rand(1, 5);
      }

      value.set(Math.min(current + diff, 99));
    },
    state === "in-progress" ? 750 : null,
  );

  useEffect(() => {
    if (state === "initial") {
      value.jump(0);
    } else if (state === "completing") {
      value.set(100);
    }

    return value.on("change", (latest) => {
      if (latest === 100) {
        setState("complete");
      }
    });
  }, [state, value]);

  function reset() {
    setState("initial");
  }

  function start() {
    setState("in-progress");
  }

  function done() {
    setState((state) =>
      state === "initial" || state === "in-progress" ? "completing" : state,
    );
  }

  return { state, value, start, done, reset };
}

function rand(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function useInterval(callback, delay) {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    function tick() {
      savedCallback.current();
    }

    if (delay !== null) {
      tick();

      let id = setInterval(tick, delay);
      return () => clearInterval(id);
    }
  }, [delay]);
}

export function useProgressBarLink(routerOptions = {}) {
  const progress = useProgressBar();
  const router = useRouter();

  return (href) => (e, isMetaPressed) => {
    e.preventDefault();

    const blank = "_blank";

    // Check if Ctrl or Cmd key is pressed for new tab
    if (e.metaKey || e.ctrlKey || isMetaPressed) {
      window.open(href.toString(), blank, "noopener,noreferrer");
    } else {
      progress.start();

      startTransition(() => {
        router.push(href.toString(), routerOptions);
        progress.done();
      });
    }
  };
}
