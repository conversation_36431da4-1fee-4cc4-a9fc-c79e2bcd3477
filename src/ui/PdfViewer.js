"use client";

import { Document, Page, pdfjs } from "react-pdf";
// import "react-pdf/dist/esm/Layout/AnnotationLayer.css";
// import "react-pdf/dist/esm/Layout/TextLayer.css";
import "react-pdf/src/Page/AnnotationLayer.css";
import "react-pdf/src/Page/TextLayer.css";
import { useState } from "react";
import { Spinner } from "@blueprintjs/core";
import HStack from "@/ui/HStack";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Button from "@/ui/Button";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url,
).toString();

// pdfjs.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.mjs";

export default function PdfViewer({
  blob,
  fileUrl,
  loading = false,
  width = 900,
  showAll = true,
  onClick = () => {},
}) {
  const [pages, setPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);

  const file = blob ? window.URL.createObjectURL(blob) : fileUrl;

  function handleOnLoadSuccess({ numPages }) {
    setPages(numPages);
  }

  if (loading)
    return (
      <div className="py-8">
        <Spinner size={48} />
      </div>
    );

  // https://github.com/wojtekmaj/react-pdf/issues/974#issuecomment-2934425521
  // Add a key prop to <Document> to prevent "sendWithPromise" error.
  const PREVENT_SEND_WITH_PROMISE_ERROR = file;

  return showAll ? (
    <Document
      loading={null}
      key={PREVENT_SEND_WITH_PROMISE_ERROR}
      file={file}
      onLoadSuccess={handleOnLoadSuccess}
    >
      <div className="space-y-4">
        {Array.from(new Array(pages), (el, index) => (
          <div key={`page_${index + 1}`} className="border shadow-md">
            <Page loading={null} pageNumber={index + 1} width={width} />
          </div>
        ))}
      </div>
    </Document>
  ) : (
    <Document
      loading={null}
      file={file}
      onLoadSuccess={handleOnLoadSuccess}
      className="relative"
    >
      <Page
        loading={null}
        pageNumber={pageNumber}
        width={width}
        onClick={onClick}
      />
      {pages !== 1 && (
        <HStack className="absolute bottom-0 left-1/2 z-30 -translate-x-1/2">
          <Button
            variant="plainIcon"
            onClick={(e) => {
              e.stopPropagation();
              setPageNumber(pageNumber - 1);
            }}
            prefix={<ChevronLeft strokeWidth={1.5} />}
            disabled={pageNumber === 1}
            aria-label="Previous Page_old"
            className="bg-secondary min-w-[30px]"
          />
          <p>
            {pageNumber} / {pages}
          </p>
          <Button
            variant="plainIcon"
            onClick={() => setPageNumber(pageNumber + 1)}
            prefix={<ChevronRight strokeWidth={1.5} />}
            disabled={pageNumber >= pages}
            aria-label="Next Page_old"
            className="bg-secondary min-w-[30px]"
          />
        </HStack>
      )}
    </Document>
  );
}
