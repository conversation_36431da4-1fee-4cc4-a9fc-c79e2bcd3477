"use client";

import cn from "@/lib/cn";
import { Dialog as DialogPrimitive, VisuallyHidden } from "radix-ui";
import { XCircleIcon } from "@phosphor-icons/react";
import Button from "@/ui/Button";
import HStack from "@/ui/HStack";
import { useRouter } from "next/navigation";

// Problem with pointer-event: none locking
// https://github.com/radix-ui/primitives/issues/1859
// https://github.com/radix-ui/primitives/issues/2219

const DialogPortal = DialogPrimitive.Portal;
const DialogClose = DialogPrimitive.Close;

// -----
// Dialog
// Automatically remove pointer-events: none from <body> so that Combobox can overlap.
// -----
const Dialog = ({ onOpenChange = (open) => {}, ref, ...props }) => {
  const router = useRouter();

  return (
    <DialogPrimitive.Root
      ref={ref}
      onOpenChange={(open) => {
        // Remember if you control open elsewhere, be sure to also setTimeout to remove pointerEvents
        // https://github.com/shadcn-ui/ui/issues/468#issuecomment-1607715655

        setTimeout(() => (document.body.style.pointerEvents = ""), 0);

        // if (!open) {
        //   router.refresh();
        // }

        onOpenChange && onOpenChange(open);
      }}
      {...props}
    />
  );
};
Dialog.displayName = DialogPrimitive.Root.displayName;

const DialogTrigger = ({ ref, ...props }) => {
  return <DialogPrimitive.Trigger ref={ref} asChild {...props} />;
};
DialogTrigger.displayName = DialogPrimitive.Trigger.displayName;

// -----
// DialogOverlay
// -----
const DialogOverlay = ({ className, ref, ...props }) => {
  const classNames = cn(
    "fixed inset-0 z-50 bg-gray-900/40",
    "dark:bg-neutral-900/70",
    "data-[state=open]:animate-in data-[state=closed]:animate-out",
    "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
    className,
  );

  // return (
  //   <DialogPrimitive.Overlay ref={ref} className={classNames} {...props} />
  // );

  // If using DialogPrimitive.Overlay (with react-remove-scroll), we will not be able to do text selection or use Apple Pencil.
  // If the dialog involve Dropdown, remember to use modal=false!
  // See: https://github.com/shadcn-ui/ui/issues/468
  // See: https://github.com/radix-ui/primitives/issues/1836
  return <div ref={ref} className={classNames} {...props} />;
};
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

// -----
// DialogContent
// -----
const DialogContent = ({
  preventClose = false,
  className,
  children,
  ref,
  ...props
}) => {
  const classNames = cn(
    "group/dialog",
    "bg-background z-50 mx-auto mt-18 transform rounded-lg px-4 shadow-lg outline-none",
    "max-h-[calc(100%-144px)] w-[95vw] max-w-5xl overflow-scroll",
    "data-[state=open]:animate-in data-[state=closed]:animate-out",
    "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
    "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
    "dark:border dark:bg-neutral-800 dark:shadow-2xl",
    className,
  );

  const onInteractOutside = (e) => {
    if (preventClose) {
      e.preventDefault();
    } else {
      props.onInteractOutside && props.onInteractOutside(e);
    }
  };

  return (
    <DialogPortal>
      <DialogOverlay>
        <DialogPrimitive.Content
          ref={ref}
          className={classNames}
          onInteractOutside={onInteractOutside}
          {...props}
        >
          {children}
        </DialogPrimitive.Content>
      </DialogOverlay>
    </DialogPortal>
  );
};
DialogContent.displayName = DialogPrimitive.Content.displayName;

// -----
// DialogHeader
// -----
const DialogHeader = ({
  className,
  children,
  ref,
  borderless = false,
  removeClose = false,
  ...props
}) => {
  const classNames = cn(
    "bg-background/95 sticky top-0 z-50 flex items-center justify-between border-b py-4 font-semibold backdrop-blur-sm",
    "dark:dark:bg-neutral-800/95",
    "text-lg",
    {
      "border-b-0": borderless,
    },
    className,
  );

  return (
    <div ref={ref} className={classNames} {...props}>
      <DialogPrimitive.Title>{children}</DialogPrimitive.Title>
      <VisuallyHidden.Root>
        <DialogPrimitive.Description>{children}</DialogPrimitive.Description>
      </VisuallyHidden.Root>
      {!removeClose && (
        <DialogClose className="rounded-full hover:opacity-90 focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:outline-none active:opacity-50">
          <span className="text-gray-300 dark:text-neutral-500">
            <XCircleIcon size="32" weight="fill" />
          </span>
        </DialogClose>
      )}
    </div>
  );
};
DialogHeader.displayName = "DialogHeader";

// -----
// DialogFooter
// -----
const DialogFooter = ({
  className,
  children,
  ref,
  center = false,
  ...props
}) => {
  const classNames = cn(
    "bg-background sticky bottom-0 z-10 flex justify-between gap-4 border-t py-4",
    "dark:bg-neutral-800",
    {
      "justify-center": center,
    },
    className,
  );

  return (
    <div ref={ref} className={classNames} {...props}>
      {children}
    </div>
  );
};
DialogFooter.displayName = "DialogFooter";

// -----
// DialogDismissButton
// -----
const DialogDismissButton = (props) => {
  return (
    <DialogClose asChild>
      <Button variant="secondary" outline {...props}>
        {props.children || "Dismiss"}
      </Button>
    </DialogClose>
  );
};
DialogDismissButton.displayName = "DialogDismissButton";

const DialogFooterWithCTA = ({
  label,
  pending,
  buttonProps = {},
  onDismiss = () => {},
}) => {
  return (
    <DialogFooter>
      <HStack className="w-full justify-end gap-4">
        <DialogDismissButton onClick={onDismiss} />
        <Button type="submit" loading={pending} {...buttonProps}>
          {label}
        </Button>
      </HStack>
    </DialogFooter>
  );
};

export {
  Dialog,
  DialogTrigger,
  DialogPortal,
  DialogClose,
  DialogDismissButton,
  DialogOverlay,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogFooterWithCTA,
};
