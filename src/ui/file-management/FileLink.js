"use client";

import { isEmpty } from "lodash";
import <PERSON> from "next/link";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import FileIcon from "@/ui/file-management/icons/FileIcon";

export default function FileLink({
  file,
  title = null,
  target = "_blank",
  ...props
}) {
  // File upload usually be done in queue
  // So if the record return too fast, the physical file has yet to be uploaded
  // Result in fileUrl is empty
  if (isEmpty(file.fileUrl)) console.warn("File url is not provided!");

  return (
    <Link
      href={file.fileUrl || "#"}
      target={target}
      title={title || file.fileName}
      className="w-full pb-2 last:pb-0 sm:w-fit sm:pb-0"
      rel="noreferrer"
      {...props}
    >
      <HStack>
        <FileIcon type={file.fileType} />

        <VStack className="gap-0">
          <p className="w-full overflow-hidden text-ellipsis whitespace-nowrap sm:max-w-[200px]">
            {file.fileName}
          </p>
          <p className="text-sm text-muted">{file.fileSize}</p>
        </VStack>
      </HStack>
    </Link>
  );
}
