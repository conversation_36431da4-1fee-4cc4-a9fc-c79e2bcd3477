"use client";

import createVariableMention from "@/ui/editors/mentions/createVariableMention";

const TOKEN_VARIABLES = [
  { label: "{{closureType}}" },
  { label: "{{effectiveFromDate}}" },
  { label: "{{lastDayDate}}" },
  { label: "{{currentDesignation}}" },
  { label: "{{currentBasicSalary}}" },
  { label: "{{contractorReason}}" },
  { label: "{{clientReason}}" },
  { label: "{{alBalanceDays}}" },
  { label: "{{alInstruction}}" },
  { label: "{{alBalanceDaysAndInstruction}}" },
  { label: "{{terminationNotice}}" },
  { label: "{{contractor.name}}" },
  { label: "{{company.name}}" },
];

const ClosureVariableMention = createVariableMention({
  items: TOKEN_VARIABLES,
  itemId: "label",
  itemLabel: "label",
  pluginKeyName: "ClosureVariableMention",
  char: "{{",
});

export default ClosureVariableMention;
