"use client";

import { Slot } from "radix-ui";
import { cva } from "class-variance-authority";
import cn from "@/lib/cn";
import RaceBy from "@/ui/loaders/RaceBy";

// https://web.dev/articles/building/a-button-component
// https://ui.shadcn.com/docs/components/button
// Can consider: tracking-wide, shrink-0
const variants = cva(
  "ring-offset-background relative inline-flex shrink-0 cursor-pointer items-center justify-center rounded-sm border font-normal whitespace-nowrap select-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none active:opacity-50 disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        primary:
          "border-primary-border bg-primary text-primary-foreground focus-visible:ring-primary-ring hover:enabled:bg-primary/90",
        success:
          "border-success-border bg-success text-success-foreground focus-visible:ring-success-ring hover:enabled:bg-success/90",
        secondary:
          "border-secondary-border bg-secondary text-secondary-foreground focus-visible:ring-secondary-ring hover:enabled:bg-secondary/90",
        warning:
          "border-warning-border bg-warning text-warning-foreground focus-visible:ring-warning-ring hover:enabled:bg-warning/90",
        danger:
          "border-danger-border bg-danger text-danger-foreground focus-visible:ring-danger-ring hover:enabled:bg-danger/90",
        ghost:
          "text-foreground focus-visible:ring-secondary-ring border-transparent bg-transparent hover:enabled:bg-gray-200 dark:hover:enabled:bg-neutral-700",
        plainIcon:
          "focus-visible:ring-secondary-ring border-transparent focus-visible:rounded-full focus-visible:ring-2",
        bgIcon:
          "focus-visible:ring-secondary-ring rounded-md border-transparent focus:bg-gray-200 hover:enabled:bg-gray-200 dark:focus:bg-neutral-800 dark:hover:enabled:bg-neutral-800",
      },
      size: {
        sm: ["px-2", "py-1", "text-sm"],
        md: ["px-3", "py-[0.3125rem]", "text-base"],
        lg: ["px-4", "py-1.5", "text-lg"],
      },
      fullWidth: { true: "w-full" },
      pill: { true: "rounded-full" },
      outline: { true: "bg-transparent hover:enabled:bg-transparent" },
      square: { true: "rounded-xs" },
    },
    compoundVariants: [
      {
        variant: "primary",
        outline: true,
        className: "text-primary-border",
      },
      {
        variant: "secondary",
        outline: true,
        className: "border-input",
      },
      {
        variant: "success",
        outline: true,
        className: "text-success-border",
      },
      {
        variant: "warning",
        outline: true,
        className: "text-warning-border",
      },
      {
        variant: "danger",
        outline: true,
        className: "text-danger-border",
      },
      {
        variant: "plainIcon",
        size: "md",
        className: "p-0",
      },
      {
        variant: "bgIcon",
        size: "md",
        className: "px-4 py-1",
      },
    ],
    defaultVariants: {
      variant: "success",
      size: "md",
      fullWidth: false,
    },
  },
);

const SpinnerWithoutLoadingText = () => {
  return (
    <div
      aria-live="polite"
      aria-busy
      className="absolute flex w-[calc(100%-1.5rem)] items-center justify-center"
    >
      <RaceBy size={40} />
    </div>
  );
};

/**
 *
 * @param {string} variant primary (default) | secondary | warning | danger | ghost | plainIcon | bgIcon
 * @returns
 */
const Button = ({
  asChild,
  className,
  variant,
  size,
  fullWidth,
  pill,
  outline,
  square = false,
  prefix = null,
  suffix = null,
  loading = false,
  disabled = false,
  children,
  ref,
  ...props
}) => {
  const Comp = asChild ? Slot.Root : "button";
  const _disabled = disabled || loading;

  return (
    <Comp
      className={cn(
        variants({
          variant,
          size,
          fullWidth,
          pill,
          outline,
          square,
        }),
        {
          "gap-2": prefix || suffix,
        },
        className,
      )}
      type="button"
      ref={ref}
      disabled={_disabled}
      aria-disabled={_disabled}
      tabIndex={_disabled ? -1 : 0}
      {...props}
    >
      <Prefix prefix={prefix} loading={loading} />
      {loading && <SpinnerWithoutLoadingText />}
      <Slot.Slottable>
        {loading ? <span className="invisible">{children}</span> : children}
      </Slot.Slottable>
      <Suffix suffix={suffix} loading={loading} />
    </Comp>
  );
};

const Prefix = ({ prefix, loading }) => {
  if (!prefix) return null;

  return loading ? <div className="opacity-0">{prefix}</div> : prefix;
};

const Suffix = ({ suffix, loading }) => {
  if (!suffix) return null;

  return loading ? <div className="opacity-0">{suffix}</div> : suffix;
};

Button.displayName = "Button";

export default Button;
