"use client";

import { useDropzone } from "react-dropzone";
import { useFormContext, useWatch } from "react-hook-form";
import { isEmpty } from "lodash";
import { toast } from "sonner";
import { formatFileSize } from "@/formatters/file";
import cn from "@/lib/cn";
import isNotEmpty from "@/lib/isNotEmpty";
import { UploadIcon } from "lucide-react";
import Image from "next/image";
import HStack from "@/ui/HStack";
import VStack from "@/ui/VStack";
import Button from "@/ui/Button";
import XCircleFilledIcon from "@/icons/emp/XCircleFilledIcon";

const ACCEPTABLE_TYPES = {
  images: {
    "image/png": [".png"],
    "image/jpeg": [".jpeg", ".jpg"],
    "image/tiff": [".tif", ".tiff"],
  },
  pdf: {
    "application/pdf": [".pdf"],
  },
  word: {
    "application/msword": [".doc"],
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
      ".docx",
    ],
  },
  excel: {
    "application/vnd.ms-excel": [".xls"],
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
      ".xlsx",
    ],
  },
};

export default function SingleFileUploader({
  name,
  rules,
  minFileSize = 1024, // 1KB
  maxFileSize = 52428800, // 50 * 1024 * 1024 = 50MB
  accept = ["images", "pdf", "word", "excel"],
  previewSize = 200, // if enablePreview is enabled
  enablePreview = false,
}) {
  const acceptableTypes = accept.reduce((acc, type) => {
    const types = ACCEPTABLE_TYPES[type];
    if (isEmpty(types)) return acc;

    return { ...acc, ...types };
  }, {});

  const {
    register,
    setValue,
    clearErrors,
    formState: { errors },
  } = useFormContext();
  register(name, rules);
  const file = useWatch({ name });
  const hasError = isNotEmpty(errors[name]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false,
    onDrop: (files) => {
      setValue(name, files[0]);
      clearErrors(name);
    },
    onDropRejected: (files) => {
      const file = files[0];
      if (isEmpty(file)) return;

      const { code } = file.errors[0];

      switch (code) {
        case "file-too-large":
          toast.error(`Max file size is ${formatFileSize(maxFileSize, 0)}!`);
          break;
        case "file-too-small":
          toast.error(`Min file size is ${formatFileSize(minFileSize, 0)}!`);
          break;
        case "file-invalid-type":
          toast.error(`Invalid file type!`);
          break;
        default:
          toast.error("File upload failed!");
          break;
      }
    },
    minSize: minFileSize,
    maxSize: maxFileSize,
    accept: acceptableTypes,
  });

  const dropzoneClassName = cn(
    "relative rounded-lg text-sm transition-[background-color]",
    {
      "bg-gray-100 dark:bg-neutral-700": isDragActive && !hasError,
      "text-danger outline-2 outline-input-error-ring": hasError,
    },
  );

  if (file) {
    return (
      <VStack className="items-center-safe justify-center-safe">
        {enablePreview && <Previewer name={name} size={previewSize} />}

        <HStack className="flex-nowrap">
          <div title={file.name} className="truncate overflow-hidden">
            {file.name}
          </div>
          <Button
            variant="plainIcon"
            size="sm"
            onClick={() => {
              setValue(name, null);
            }}
            suffix={<XCircleFilledIcon color="#9ca3af" />}
          />
        </HStack>
      </VStack>
    );
  }

  return (
    <div {...getRootProps()} className={dropzoneClassName}>
      <input {...getInputProps()} />
      <VStack
        className={cn("items-center py-4 text-xs text-muted", {
          "text-red-600/80": hasError,
        })}
      >
        <UploadIcon size={24} strokeWidth={1.5} />
        Drop file here (or click to browse)
      </VStack>
    </div>
  );
}

function Previewer({ name, size }) {
  const file = useWatch({ name });
  const src = URL.createObjectURL(file);

  switch (file.type) {
    case "image/png":
    case "image/jpeg":
    case "image/jpg":
      return (
        // You will see warning "Images with src ... has either width or height modified"
        // See: https://github.com/vercel/next.js/issues/61908
        <Image
          src={src}
          alt={file.name}
          width={size}
          height={size}
          unoptimized
        />
      );
    case "application/pdf":
      return <iframe src={src} title={file.name} width={size} height={size} />;
    default:
      return (
        <VStack
          className="max-h-[200px] max-w-[200px] items-center-safe justify-center-safe bg-gray-50 p-4 text-center"
          style={{ width: size, height: size }}
        >
          <p className="text-sm text-muted">Preview unavailable</p>
        </VStack>
      );
  }
}
