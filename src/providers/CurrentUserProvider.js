"use client";

import { createContext, useContext } from "react";
import { useQuery } from "urql";

export const CurrentUserContext = createContext(null);
export const useCurrentUser = () => useContext(CurrentUserContext);

const QUERY = `
  query {
    viewer
  }
`;

export const CurrentUserProvider = ({ children }) => {
  const [res] = useQuery({ query: QUERY });
  const user = res.data?.viewer;

  return <CurrentUserContext value={user}>{children}</CurrentUserContext>;
};
