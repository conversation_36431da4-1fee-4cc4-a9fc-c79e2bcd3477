{"name": "jobline-next-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@0no-co/graphql.web": "1.1.2", "@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.1.0", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^3.2.1", "@blueprintjs/core": "^5.19.1", "@blueprintjs/datetime2": "^2.3.33", "@blueprintjs/icons": "^5.23.0", "@blueprintjs/table": "^5.3.15", "@faker-js/faker": "^9.8.0", "@floating-ui/react": "^0.27.13", "@github/webauthn-json": "^2.1.1", "@hookform/resolvers": "^5.1.1", "@phosphor-icons/react": "^2.1.10", "@tiptap/extension-bullet-list": "^2.23.0", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-gapcursor": "^2.23.0", "@tiptap/extension-heading": "^2.23.0", "@tiptap/extension-highlight": "^2.23.0", "@tiptap/extension-link": "^2.23.0", "@tiptap/extension-list-item": "^2.23.0", "@tiptap/extension-mention": "^2.23.0", "@tiptap/extension-subscript": "^2.23.0", "@tiptap/extension-superscript": "^2.23.0", "@tiptap/extension-table": "^2.23.0", "@tiptap/extension-table-cell": "^2.23.0", "@tiptap/extension-table-header": "^2.23.0", "@tiptap/extension-table-row": "^2.23.0", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/extension-underline": "^2.23.0", "@tiptap/html": "^2.23.0", "@tiptap/pm": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "@tiptap/suggestion": "^2.23.0", "@uidotdev/usehooks": "^2.4.1", "@xyflow/react": "^12.7.1", "autosize": "^6.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "2.30.0", "downshift": "^9.0.9", "framer-motion": "12.19.2", "graphql": "^16.11.0", "input-otp": "^1.4.2", "install": "^0.13.0", "jose": "^6.0.11", "js-beautify": "^1.15.4", "ldrs": "^1.1.7", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "lz-string": "^1.5.0", "match-sorter": "^8.0.3", "mathjs": "^14.5.2", "next": "15.3.4", "next-themes": "^0.4.6", "pluralize": "^8.0.0", "prosemirror-state": "^1.4.3", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-complex-tree": "^2.6.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^6.0.0", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.59.0", "react-pdf": "^10.0.1", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "react-type-animation": "^3.2.0", "react-user-avatar": "^1.10.0", "server-only": "^0.0.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "urql": "^4.2.2", "use-debounce": "^10.0.5", "use-roving-index": "^2.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "4.1.11", "eslint": "^9.30.0", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^10.1.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "4.1.11", "typescript": "^5.8.3"}}